﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicRandomizerLocal : UdonSharpBehaviour
{
    public GameObject[] gameObjects;
    public int Index;

    void Start(){RandomizeObjects();}

    void OnEnable(){RandomizeObjects();}

    public void RandomizeObjects(){
        int Index = Random.Range(0, gameObjects.Length);
        for (int i = 0; i < gameObjects.Length; i++){gameObjects[i].SetActive(i == Index);}
    }
}
