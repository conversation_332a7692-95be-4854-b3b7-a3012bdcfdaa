using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CanBeWoreAsHatOutput : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public LayerMask LayerRequired;

    public int HatLimit;
    public int HatLimitMax = 1;

    void Start(){localPlayer = Networking.LocalPlayer; HatLimit = 0;}

    public void OnTriggerEnter(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;
        if (HatLimit < HatLimitMax){
            CanBeWoreAsHatInput CanBeWoreAsHatInput = collision.gameObject.GetComponent<CanBeWoreAsHatInput>();
            if(CanBeWoreAsHatInput != null)
            {
                collision.gameObject.transform.parent = transform;
                Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
                Collider[] colliders = collision.gameObject.GetComponents<Collider>();
                Collider collider = colliders[0];
                collider.isTrigger = true;
                CanBeWoreAsHatInput.IsWoren = true;
                HatLimit++;
            }
        }
    }
    public void OnTriggerExit(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        CanBeWoreAsHatInput CanBeWoreAsHatInput = collision.gameObject.GetComponent<CanBeWoreAsHatInput>();
        if(CanBeWoreAsHatInput != null && CanBeWoreAsHatInput.IsWoren == true)
        {
            collision.gameObject.transform.parent = null;
            Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
            Collider[] colliders = collision.gameObject.GetComponents<Collider>();
            Collider collider = colliders[0];
            collider.isTrigger = false;
            CanBeWoreAsHatInput.IsWoren = false;
            HatLimit--;
            if(HatLimit < 0){HatLimit = 0;}
        }
    }
}
