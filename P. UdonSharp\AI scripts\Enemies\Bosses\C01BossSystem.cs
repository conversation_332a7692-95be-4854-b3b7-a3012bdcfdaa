using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class C01BossSystem : UdonSharpBehaviour
{
    public Rigidbody rb;
    public Animator BossAnimator;
    public GameObject MainBody;
    public GameObject[] Body;
    public AudioSource GlobalAudioSource;
    private VRCPlayerApi localPlayer;
    public UniversalBossHealthSystem HealthSystem;

    public int Attacks,MovingDirection;
    public int DashStrength;
    public bool MovingCooldown;

    public float SightRange = 50f;
    public Vector3 SpawnedPoint;

    //Highlight
    public GameObject Highlight;

    //Teleport
    public GameObject TeleportIndicator;

    //Death
    public GameObject Explosion;
    public bool IsDead;

    //StoryModeEnemy
    public bool IsStoryModeEnemy;

    //Money
    public int MoneyDropAmount;

    //modes
    public bool IsOrderEnemy;

    void Start()
    {
        rb.velocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;
        rb.isKinematic = false;
        rb.drag = 500;
        rb.angularDrag = 500;
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        HealthSystem.HealthBar.SetActive(false);
        if(HealthSystem.ProtectionBar != null){HealthSystem.ProtectionBar.SetActive(true);}
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 2f);
        SendCustomEventDelayedSeconds(nameof(StareAtTarget), 1f);
    }
    public void StareAtTarget()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange && !IsDead){
            // Get player position
            Vector3 playerPos = localPlayer.GetPosition();

            // Create a flattened direction vector (only horizontal)
            Vector3 flatDirection = playerPos - transform.position;
            flatDirection.y = 0; // Remove the vertical component
            flatDirection = flatDirection.normalized;

            // Create rotation only around Y axis
            Quaternion rotation = Quaternion.LookRotation(flatDirection);
            MainBody.transform.rotation = rotation;
            SendCustomEventDelayedSeconds(nameof(StareAtTarget), 0.05f);
        }
        else{
            SendCustomEventDelayedSeconds(nameof(StareAtTarget), 1f);
        }
    }


    public void EnemyAISystem()
    {
        if(HealthSystem.Health <= 0){IsDead = true;}

        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange && !IsDead){
            HealthSystem.CanBeKilled = true;
            HealthSystem.HealthBar.SetActive(true);
            if(HealthSystem.ProtectionBar != null){HealthSystem.ProtectionBar.SetActive(false);}

            if(IsOrderEnemy == false){
                if(MovingCooldown == false){
                    if(HealthSystem.Health >= HealthSystem.HealthMax/2){Attacks = Random.Range(1, 3);}
                    else if (HealthSystem.Health < HealthSystem.HealthMax/2){Attacks = Random.Range(1, 4);}
                }
                else{
                    if(HealthSystem.Health >= HealthSystem.HealthMax/2){Attacks = 1;}
                    else if (HealthSystem.Health < HealthSystem.HealthMax/2){
                        int Attack = Random.Range(0, 2);
                        if(Attack == 0){Attacks = 1;}
                        else if(Attack == 1){Attacks = 3;}
                    }
                }
            }
            else{
                if(MovingCooldown == false){
                    Attacks = Random.Range(1, 4);
                }
                else{
                    int Attack = Random.Range(0, 2);
                    if(Attack == 0){Attacks = 1;}
                    else if(Attack == 1){Attacks = 3;}
                }
            }

            rb.velocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            rb.drag = 500;
            rb.angularDrag = 500;

            if(Attacks == 1)
            {
                Highlight.SetActive(true);
                MovingCooldown = false;
                BossAnimator.SetInteger("AnimType", 1);
                SendCustomEventDelayedSeconds(nameof(AttackOne), 1.5f);
            }
            else if(Attacks == 2)
            {
                rb.drag = 50;
                rb.angularDrag = 50;

                MovingCooldown = true;
                MovingDirection = Random.Range(1, 3);
                BossAnimator.SetInteger("AnimType", 2);
                SendCustomEventDelayedSeconds(nameof(AttackTwo), 0.1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f,5f));
            }
            else if(Attacks == 3)
            {
                Highlight.SetActive(true);
                MovingCooldown = false;
                rb.drag = 5;
                rb.angularDrag = 5;

                rb.AddForce(transform.up*DashStrength);
                BossAnimator.SetInteger("AnimType", 4);
                SendCustomEventDelayedSeconds(nameof(AttackThree), 0.6f);
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange && !IsDead){
            rb.drag = 500;
            rb.angularDrag = 500;
            rb.velocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;

            HealthSystem.CanBeKilled = false;
            HealthSystem.HealthBar.SetActive(true);
            if(HealthSystem.ProtectionBar != null){HealthSystem.ProtectionBar.SetActive(true);}
            if(HealthSystem.Health < HealthSystem.HealthMax){HealthSystem.Health += HealthSystem.HealthMax/5;}
            if(HealthSystem.Health > HealthSystem.HealthMax){HealthSystem.Health = HealthSystem.HealthMax;}
            HealthSystem.DamageText.text = HealthSystem.Health.ToString() + "/" + HealthSystem.HealthMax.ToString();
            HealthSystem.HealthSlider.value = HealthSystem.Health;
            Attacks = 1;

            if(Attacks == 1)
            {
                Highlight.SetActive(false);
                TeleportIndicator.SetActive(false);
                BossAnimator.SetInteger("AnimType", 0);
                SendCustomEventDelayedSeconds(nameof(IdleOne), 1f);
            }
        }
        else{
            HealthSystem.IsDead = true;
            HealthSystem.CanBeKilled = false;
            HealthSystem.HealthBar.SetActive(false);

            rb.drag = 500;
            rb.angularDrag = 500;
            rb.velocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            SendCustomEventDelayedSeconds(nameof(IsDying), 1f);
        }
    }

    public void AttackOne(){
        Highlight.SetActive(false);
        rb.drag = 0;
        rb.angularDrag = 0;
        transform.LookAt(localPlayer.GetPosition() + new Vector3(0,1f,0));
        rb.AddForce(transform.forward*DashStrength);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.5f);
    }
    public void AttackTwo(){
        transform.LookAt(localPlayer.GetPosition() + new Vector3(0,1f,0));
        if(MovingDirection == 1){rb.AddForce(transform.right*DashStrength/2);}
        else if(MovingDirection == 2){rb.AddForce(-transform.right*DashStrength/2);}
        if(Attacks == 2){SendCustomEventDelayedSeconds(nameof(AttackTwo), 0.05f);}
    }
    public void AttackThree(){
        rb.velocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;

        rb.drag = 0;
        rb.angularDrag = 0;
        Highlight.SetActive(false);
        transform.LookAt(localPlayer.GetPosition() + new Vector3(0,1f,0));
        rb.AddForce(transform.forward*DashStrength);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.6f);
    }


    public void IdleOne()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.parent = null;
        TeleportIndicator.transform.position = SpawnedPoint + new Vector3(Random.Range(-2f,2f),1f,Random.Range(-2f,2f));;
        SendCustomEventDelayedSeconds(nameof(IdleOne2), 1f);
    }
    public void IdleOne2()
    {
        TeleportIndicator.SetActive(false);
        transform.position = TeleportIndicator.transform.position;
        TeleportIndicator.transform.parent = transform;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }



    public void IsDying()
    {
        BossAnimator.SetInteger("AnimType", 3);
        SendCustomEventDelayedSeconds(nameof(ExplosionFunction), 1f);
    }
    public void ExplosionFunction()
    {
        //MoneyDrop
        if(MoneyDropAmount != 0){
            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(MoneyDropAmount);
        }

        //StoryModeEnemy Daily Quest
        if(IsStoryModeEnemy){
            GameObject QuestObject = GameObject.Find("Daily Quests");
            QuestObject.GetComponent<DailyQuestsSystem>().DailyQuest2();
        }

        for (int i = 0; i < Body.Length; i++){if(Body[i] != null){Body[i].SetActive(false);}}
        Explosion.SetActive(true);
        SendCustomEventDelayedSeconds(nameof(Death), 2f);
    }
    public void Death()
    {
        IsDead = true;
        Destroy(gameObject);
        Destroy(TeleportIndicator);
    }

    void OnDestroy(){Destroy(TeleportIndicator);}
}
