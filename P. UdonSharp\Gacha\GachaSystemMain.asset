%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c333ccfdd0cbdbc4ca30cef2dd6e6b9b, type: 3}
  m_Name: GachaSystemMain
  m_EditorClassIdentifier: 
  serializedUdonProgramAsset: {fileID: 11400000, guid: 555857c8851028f46873ddef44d36436,
    type: 2}
  udonAssembly: 
  assemblyError: 
  sourceCsScript: {fileID: 11500000, guid: 3d23be970f65a9346a2819c2afb2a2f7, type: 3}
  scriptVersion: 2
  compiledVersion: 2
  behaviourSyncMode: 1
  hasInteractEvent: 0
  scriptID: -6338687224774269246
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: fieldDefinitions
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UdonSharp.Compiler.FieldDefinition,
        UdonSharp.Editor]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 37
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: BannerSelected
    - Name: $v
      Entry: 7
      Data: 2|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: BannerSelected
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 3|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemID
    - Name: $v
      Entry: 7
      Data: 5|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemID
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 6|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 7|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemRarity
    - Name: $v
      Entry: 7
      Data: 8|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemRarity
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 9|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemName
    - Name: $v
      Entry: 7
      Data: 10|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemName
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 11|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.String[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 12|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemDescription
    - Name: $v
      Entry: 7
      Data: 13|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemDescription
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 14|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ROLLS_KEY
    - Name: $v
      Entry: 7
      Data: 15|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ROLLS_KEY
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 16|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.String, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 16
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 17|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemSprite
    - Name: $v
      Entry: 7
      Data: 18|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemSprite
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 19|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Sprite[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 19
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 20|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: BannerSprite
    - Name: $v
      Entry: 7
      Data: 21|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: BannerSprite
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 19
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 19
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 22|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: CurrencySprite
    - Name: $v
      Entry: 7
      Data: 23|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: CurrencySprite
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 24|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Sprite, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 24
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 25|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemNameText
    - Name: $v
      Entry: 7
      Data: 26|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemNameText
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 27|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: TMPro.TextMeshProUGUI, Unity.TextMeshPro
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 28|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemDescriptionText
    - Name: $v
      Entry: 7
      Data: 29|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemDescriptionText
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 30|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: RollCountText
    - Name: $v
      Entry: 7
      Data: 31|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: RollCountText
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 32|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemNameSummonText
    - Name: $v
      Entry: 7
      Data: 33|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemNameSummonText
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 27
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 34|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemImage
    - Name: $v
      Entry: 7
      Data: 35|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemImage
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 36|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.UI.Image, UnityEngine.UI
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 36
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 37|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemSummonImage
    - Name: $v
      Entry: 7
      Data: 38|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemSummonImage
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 36
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 36
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 39|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: BannerBackgroundImage
    - Name: $v
      Entry: 7
      Data: 40|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: BannerBackgroundImage
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 36
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 36
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 41|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AudioSource
    - Name: $v
      Entry: 7
      Data: 42|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: AudioSource
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 43|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.AudioSource, UnityEngine.AudioModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 43
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 44|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ClickSound
    - Name: $v
      Entry: 7
      Data: 45|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ClickSound
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 46|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.AudioClip, UnityEngine.AudioModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 46
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 47|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: BuzzSound
    - Name: $v
      Entry: 7
      Data: 48|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: BuzzSound
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 46
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 46
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 49|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: Animator
    - Name: $v
      Entry: 7
      Data: 50|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: Animator
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 51|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Animator, UnityEngine.AnimationModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 51
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 52|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: SoundObjects
    - Name: $v
      Entry: 7
      Data: 53|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: SoundObjects
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 54|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 54
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 55|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ParticleObjects
    - Name: $v
      Entry: 7
      Data: 56|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ParticleObjects
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 54
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 54
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 57|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: RollCount
    - Name: $v
      Entry: 7
      Data: 58|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: RollCount
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 59|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: RollGuaranteeMax
    - Name: $v
      Entry: 7
      Data: 60|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: RollGuaranteeMax
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 61|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: NotEnoughKeysObject
    - Name: $v
      Entry: 7
      Data: 62|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: NotEnoughKeysObject
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 63|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 64|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: InCooldown
    - Name: $v
      Entry: 7
      Data: 65|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: InCooldown
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 66|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Boolean, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 66
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 67|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AnimationState
    - Name: $v
      Entry: 7
      Data: 68|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: AnimationState
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 66
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 66
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 69|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: currentMonth
    - Name: $v
      Entry: 7
      Data: 70|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: currentMonth
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 71|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: Menu
    - Name: $v
      Entry: 7
      Data: 72|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: Menu
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 73|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: MenuBase
    - Name: $v
      Entry: 7
      Data: 74|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: MenuBase
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 75|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: MainPlayerSystem
    - Name: $v
      Entry: 7
      Data: 76|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: MainPlayerSystem
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 77|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: SpawnMenuSystemObject
    - Name: $v
      Entry: 7
      Data: 78|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: SpawnMenuSystemObject
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 63
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 79|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: MainPlayerCurrencySystem
    - Name: $v
      Entry: 7
      Data: 80|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: MainPlayerCurrencySystem
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 81|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: MainPlayerCurrencySystem, Assembly-CSharp
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 7
      Data: 82|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.Udon.UdonBehaviour, VRC.Udon
    - Name: 
      Entry: 8
      Data: 
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 83|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: KeyCurrencySystemMain
    - Name: $v
      Entry: 7
      Data: 84|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: KeyCurrencySystemMain
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 85|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: KeyCurrencySystemMain, Assembly-CSharp
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 86|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: SpawnMenuDataSystem
    - Name: $v
      Entry: 7
      Data: 87|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: SpawnMenuDataSystem
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 88|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: SpawnMenuDataSystem, Assembly-CSharp
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 89|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: localPlayer
    - Name: $v
      Entry: 7
      Data: 90|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: localPlayer
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 91|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.SDKBase.VRCPlayerApi, VRCSDKBase
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 91
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 92|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: PlayerInProximity
    - Name: $v
      Entry: 7
      Data: 93|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: PlayerInProximity
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 66
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 66
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 94|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
