using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class EnemySpawnRateSystem : UdonSharpBehaviour
{

    private VRCPlayerApi localPlayer; 

    public int CurrentLayerID;
    public int[] Layers; // [0] = ocean/land threshold, [1] = land/island threshold, [2] = island/space threshold
    public MeshRenderer MainRenderer;
    public Color[] LayerColors; // [0] = ocean, [1] = land, [2] = island, [3] = space
    public bool HasStarted = false;
    public bool PlayerOnTrigger = false;

    [Header("Enemy Spawn Configuration")]
    public GameObject[] OceanEnemies;    // Layer 0 enemies
    public GameObject[] LandEnemies;     // Layer 1 enemies
    public GameObject[] IslandEnemies;   // Layer 2 enemies
    public GameObject[] SpaceEnemies;    // Layer 3 enemies

    public int MaxEnemiesPerLayer = 10;
    public float SpawnRadius = 50f;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        StartSystem();
    }

    public void StartSystem(){
        HasStarted = true;
        PlayerOnTrigger = true;
        SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 60f);
    }

    public void UpdateSpawnRate(){
        if (Networking.LocalPlayer == null) {return;}
        if(PlayerOnTrigger){

            // Calculate Y difference between player and object pivot
            float yDifference = localPlayer.GetPosition().y - transform.position.y;
            int newLayerID = GetLayerFromYDifference(yDifference);

            // Update layer if changed
            if(newLayerID != CurrentLayerID){
                CurrentLayerID = newLayerID;
                UpdateRendererColor();
                SpawnEnemiesForLayer(CurrentLayerID);
            }

            SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 10f);
        }
        else{
            SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 60f);
        }
    }

    private int GetLayerFromYDifference(float yDiff){
        // Layer 0: Ocean (below Layers[0])
        if(yDiff < Layers[0]) return 0;

        // Layer 1: Land (between Layers[0] and Layers[1])
        if(yDiff < Layers[1]) return 1;

        // Layer 2: Island (between Layers[1] and Layers[2])
        if(yDiff < Layers[2]) return 2;

        // Layer 3: Space (above Layers[2])
        return 3;
    }

    private void UpdateRendererColor(){
        if(MainRenderer != null && LayerColors.Length > CurrentLayerID){
            MainRenderer.material.color = LayerColors[CurrentLayerID];
        }
    }

    private void SpawnEnemiesForLayer(int layerID){
        GameObject[] enemiesToSpawn = null;

        switch(layerID){
            case 0: enemiesToSpawn = OceanEnemies; break;
            case 1: enemiesToSpawn = LandEnemies; break;
            case 2: enemiesToSpawn = IslandEnemies; break;
            case 3: enemiesToSpawn = SpaceEnemies; break;
        }

        if(enemiesToSpawn != null && enemiesToSpawn.Length > 0){
            for(int i = 0; i < MaxEnemiesPerLayer; i++){
                GameObject enemyPrefab = enemiesToSpawn[Random.Range(0, enemiesToSpawn.Length)];
                if(enemyPrefab != null){
                    Vector3 spawnPos = GetRandomSpawnPosition();
                    Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
                }
            }
        }
    }

    private Vector3 GetRandomSpawnPosition(){
        Vector2 randomCircle = Random.insideUnitCircle * SpawnRadius;
        return localPlayer.GetPosition() + new Vector3(randomCircle.x, 0, randomCircle.y);
    }


    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;
        PlayerOnTrigger = true;
    }

    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if (player != localPlayer) return;
        PlayerOnTrigger = false;
    }
}
