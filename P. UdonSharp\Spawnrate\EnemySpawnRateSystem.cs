using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class EnemySpawnRateSystem : UdonSharpBehaviour
{

    private VRCPlayerApi localPlayer; 

    public int CurrentLayerID;
    public int[] Layers;
    public MeshRenderer MainRenderer;
    public Color[] LayerColors;
    public bool HasStarted = false;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        StartSystem();
    }

    public void StartSystem(){
        HasStarted = true;
        PlayerOnTrigger = true;
        SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 60f);
    }

    public void UpdateSpawnRate(){
        if (Networking.LocalPlayer == null) {return;}
        if(PlayerOnTrigger){

            //Change LayerID based on the difference in Y axis of the main object and the player is on the hitbox. 4 layers. sea, land, mountain, space

            SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 10f);
        }
        else{
            SendCustomEventDelayedSeconds(nameof(UpdateSpawnRate), 60f);
        }
    }


    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;
        PlayerOnTrigger = true;
    }

    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if (player != localPlayer) return;
        PlayerOnTrigger = false;
    }
}
