
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class NightmareBotAI : UdonSharpBehaviour
{
    public GameObject MainBody;
    public float SightRange = 50f;

    private Vector3 SpawnedPoint;
    public VRCPlayerApi localPlayer;

    private int Attacks;

    void Start(){
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
    }

    public void EnemyAISystem()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
            Attacks = Random.Range(1, 2);

            if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(Attack), 0.1f);}
        }
        else{
            Attacks = Random.Range(1, 2);

            if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(Idle), 0.1f);}
        }
    }


    public void Attack()
    {
        //Which X position
        int RandomPositionDirection = Random.Range(0, 4);
        if(RandomPositionDirection == 0){MainBody.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(7f,15f),0.3f,Random.Range(7f,15f));}
        else if(RandomPositionDirection == 1){MainBody.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(7f,15f),0.3f,Random.Range(-15f,-7f));}
        else if(RandomPositionDirection == 2){MainBody.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-15f,-7f),0.3f,Random.Range(7f,15f));}
        else if(RandomPositionDirection == 3){MainBody.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-15f,-7f),0.3f,Random.Range(-15f,-7f));}
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 5f);
    }

    public void Idle()
    {
        MainBody.transform.position = SpawnedPoint;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 5f);
    }
}
