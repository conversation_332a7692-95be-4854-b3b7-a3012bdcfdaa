﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicPlayerSentrySystem : UdonSharpBehaviour
{
    public int TurretType; //0 is Normal, 1 is Flamethrower, 2 is Rocket Launcher
    public float SightRange = 10f;
    public LayerMask WhatIsEnemy;
    public GameObject Head;
    public GameObject closestEnemy;
    //Bullet
    public float bulletSpeed = 10;
    public GameObject bullet;
    public Rigidbody bulletrb;
    public Transform bulletSpawnPoint;
    public int BulletDelay,CanFireAgain = 3;

    private VRCPlayerApi localPlayer;  

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        bulletrb = bullet.GetComponent<Rigidbody>();
    
        //Timer for update
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    public void UpdateCustom()
    {
        //Find enemieswithinrange
        Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, SightRange, WhatIsEnemy);
        float closestDistance = Mathf.Infinity;

        foreach (Collider enemy in enemiesInRange)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestEnemy = enemy.gameObject;
            }
        }

        //Timer Stuff
        if (enemiesInRange.Length == 0)
        {
            bullet.SetActive(false);
            closestEnemy = null;
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
            return;
        }

        if (closestEnemy != null)
        {
            TargetAndFire();
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.05f);
        }
    }
    void TargetAndFire()
    {
        if(gameObject.activeInHierarchy){
            BulletDelay++;
            //Aimtotarget
            Vector3 HeadDirection = closestEnemy.transform.position - Head.transform.position;
            Head.transform.rotation = Quaternion.LookRotation(HeadDirection);

            if(BulletDelay > CanFireAgain && TurretType == 0){
                //Bullet Shoot
                bullet.SetActive(true);
                bullet.transform.position = bulletSpawnPoint.position;
                bullet.transform.rotation = bulletSpawnPoint.rotation;
                bulletrb.velocity = bulletSpawnPoint.forward * bulletSpeed;
                BulletDelay = 0;
            }
            else if(BulletDelay > CanFireAgain && TurretType == 1){
                //Bullet Shoot
                bullet.SetActive(true);
                BulletDelay = 0;
            }
        }
        else{
            bullet.SetActive(false);
        }
    }
}
