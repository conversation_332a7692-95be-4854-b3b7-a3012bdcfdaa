﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalSlimeAISystem : UdonSharpBehaviour
{
    public int SlimeType = 0; //0 is small, 1 is Medium, 2 is Large
    public GameObject MainBody;
    public Rigidbody rb;
    public float SightRange = 25f;
    public float ForceJump = 10f, ForceForward = 2f;
    public int JumpCooldown = 0, JumpCooldownMax = 3;
    public float EnemyAIUpdateInterval = 1f;

    public Vector3 SpawnedPoint;
    public VRCPlayerApi localPlayer;

    public float SlimyTransitionValue = 0f;
    public int SlimyTransitionState = 0;
    public float SlimyTransitionSpeedValue, SlimyTransitionSpeedSlow = 0.1f, SlimyTransitionSpeedFast = 0.2f;

    public Animator Animator;

    //Teleport
    public GameObject FlashIndicator;
    public AudioSource FlashIndicatorSound;
    public AudioClip TeleportIndicatorSound,SuccessfulTeleportSound;
    public GameObject TeleporterSpawner;
    public bool CanTeleport = false, TeleporterSpawnerDropped = false;
    public int TeleportCooldown = 0, TeleportCooldownMax = 3;

    //Extras
    public float TargetInnacuracy = 5f;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;

        SlimyTransitionSpeedValue = SlimyTransitionSpeedSlow;

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
    }

    void Update()
    {
        if (SlimyTransitionValue < 1f && SlimyTransitionState == 0){
            SlimyTransitionValue += SlimyTransitionSpeedValue;
            if(SlimyTransitionValue >= 1f){SlimyTransitionState = 1;}
        }
        if (SlimyTransitionValue > 0f && SlimyTransitionState == 1){
            SlimyTransitionValue -= SlimyTransitionSpeedValue;
            if(SlimyTransitionValue <= 0f){SlimyTransitionState = 0;}
        }

        Animator.SetFloat("SlimyTransition", SlimyTransitionValue);
    }

    public void EnemyAISystem()
    {
        if (localPlayer == null){return;}

        if (SlimeType == 0){ //Small
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
                JumpCooldown++;

                if(JumpCooldown == JumpCooldownMax-1){SlimyTransitionSpeedValue = SlimyTransitionSpeedFast;}

                if(JumpCooldown >= JumpCooldownMax)
                {
                    int RandomNumber = Random.Range(0,3);
                    Vector3 directionToPlayer = (localPlayer.GetPosition() + new Vector3(Random.Range(-TargetInnacuracy,TargetInnacuracy),2f,Random.Range(-TargetInnacuracy,TargetInnacuracy))) - transform.position;

                    rb.AddForce(0, (ForceJump*100f), 0, ForceMode.Impulse);
                    rb.AddForce(directionToPlayer.normalized * (ForceForward*100f), ForceMode.Impulse);
                    if(RandomNumber == 0){JumpCooldown = 0;}
                    else if(RandomNumber == 1){JumpCooldown = -1;}
                    else if(RandomNumber == 2){JumpCooldown = 1;}
                    SlimyTransitionSpeedValue = SlimyTransitionSpeedSlow;
                    SlimyTransitionValue = 0f;
                    SlimyTransitionState = 0;
                    Animator.SetFloat("SlimyTransition", SlimyTransitionValue);
                }
            }
            else
            {
                if(!CanTeleport){
                    JumpCooldown++;

                    if(JumpCooldown == JumpCooldownMax-1){SlimyTransitionSpeedValue = SlimyTransitionSpeedFast;}

                    if(JumpCooldown >= JumpCooldownMax)
                    {
                        Vector3 GoToSpawnPoint = SpawnedPoint - transform.position + new Vector3(Random.Range(-1f,1f),2f,Random.Range(-1f,1f));

                        rb.AddForce(0, (ForceJump*100f), 0, ForceMode.Impulse);
                        rb.AddForce(GoToSpawnPoint.normalized * (ForceForward*100f), ForceMode.Impulse);
                        JumpCooldown = 0;
                        SlimyTransitionSpeedValue = SlimyTransitionSpeedSlow;
                        SlimyTransitionValue = 0f;
                        SlimyTransitionState = 0;
                        Animator.SetFloat("SlimyTransition", SlimyTransitionValue);
                        CanTeleport = true;
                    }
                }
                else if(CanTeleport){
                    TeleportCooldown++;
                    if(TeleportCooldown < TeleportCooldownMax){
                        if(!TeleporterSpawnerDropped){
                            if(gameObject.activeInHierarchy == true){
                                TeleporterSpawner.transform.parent = null;
                                TeleporterSpawner.SetActive(true);
                            }
                            TeleporterSpawner.transform.position = SpawnedPoint + new Vector3(Random.Range(-1f,1f),5f,Random.Range(-1f,1f));
                            TeleporterSpawnerDropped = true;
                            FlashIndicatorSound.PlayOneShot(TeleportIndicatorSound);
                        }
                    }
                    if(TeleportCooldown >= TeleportCooldownMax){
                        gameObject.transform.position = TeleporterSpawner.transform.position;
                        TeleporterSpawner.SetActive(false);
                        TeleporterSpawner.transform.parent = transform;
                        TeleporterSpawner.transform.position = transform.position;
                        TeleporterSpawnerDropped = false;
                        TeleportCooldown = 0;
                        CanTeleport = false;
                        FlashIndicatorSound.PlayOneShot(SuccessfulTeleportSound);
                    }
                }
            }
        }
        else if (SlimeType == 1){ //Medium
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){

                if(!CanTeleport){
                    JumpCooldown++;
                    if(JumpCooldown == JumpCooldownMax-1){SlimyTransitionSpeedValue = SlimyTransitionSpeedFast;}

                    if(JumpCooldown >= JumpCooldownMax)
                    {
                        Vector3 directionToPlayer = (localPlayer.GetPosition() + new Vector3(Random.Range(-TargetInnacuracy,TargetInnacuracy),1.5f,Random.Range(-TargetInnacuracy,TargetInnacuracy))) - transform.position;

                        rb.AddForce(0, (ForceJump*100f), 0, ForceMode.Impulse);
                        rb.AddForce(directionToPlayer.normalized * (ForceForward*100f), ForceMode.Impulse);
                        JumpCooldown = 0;
                        SlimyTransitionSpeedValue = SlimyTransitionSpeedSlow;
                        SlimyTransitionValue = 0f;
                        SlimyTransitionState = 0;
                        Animator.SetFloat("SlimyTransition", SlimyTransitionValue);
                        int RandomNumber = Random.Range(0,2);
                        if(RandomNumber == 0){CanTeleport = true;}
                        else if(RandomNumber == 1){CanTeleport = false;}
                    }
                }
                else if(CanTeleport){
                    TeleportCooldown++;
                    if(TeleportCooldown < TeleportCooldownMax){
                        if(!TeleporterSpawnerDropped){
                            if(gameObject.activeInHierarchy == true){
                                TeleporterSpawner.transform.parent = null;
                                TeleporterSpawner.SetActive(true);
                            }
                            FlashIndicator.SetActive(true);
                            TeleporterSpawner.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-TargetInnacuracy*3,TargetInnacuracy*3),7.5f,Random.Range(-TargetInnacuracy*3,TargetInnacuracy*3));
                            TeleporterSpawnerDropped = true;
                            FlashIndicatorSound.PlayOneShot(TeleportIndicatorSound);
                        }
                    }
                    if(TeleportCooldown >= TeleportCooldownMax){
                        gameObject.transform.position = TeleporterSpawner.transform.position;
                        FlashIndicator.SetActive(false);
                        TeleporterSpawner.SetActive(false);
                        TeleporterSpawner.transform.parent = transform;
                        TeleporterSpawner.transform.position = transform.position;
                        TeleporterSpawnerDropped = false;
                        TeleportCooldown = 0; 
                        CanTeleport = false;
                        FlashIndicatorSound.PlayOneShot(SuccessfulTeleportSound);
                    }
                }
            }
            else
            {
                if(!CanTeleport){
                    JumpCooldown++;

                    if(JumpCooldown == JumpCooldownMax-1){SlimyTransitionSpeedValue = SlimyTransitionSpeedFast;}

                    if(JumpCooldown >= JumpCooldownMax)
                    {
                        Vector3 GoToSpawnPoint = SpawnedPoint - transform.position + new Vector3(Random.Range(-5f,5f),2f,Random.Range(-5f,5f));

                        rb.AddForce(0, (ForceJump*100f), 0, ForceMode.Impulse);
                        rb.AddForce(GoToSpawnPoint.normalized * (ForceForward*100f), ForceMode.Impulse);
                        JumpCooldown = 0;
                        SlimyTransitionSpeedValue = SlimyTransitionSpeedSlow;
                        SlimyTransitionValue = 0f;
                        SlimyTransitionState = 0;
                        Animator.SetFloat("SlimyTransition", SlimyTransitionValue);
                        CanTeleport = true;
                    }
                }
                else if(CanTeleport){
                    TeleportCooldown++;
                    if(TeleportCooldown < TeleportCooldownMax){
                        if(!TeleporterSpawnerDropped){
                            if(gameObject.activeInHierarchy == true){
                                TeleporterSpawner.transform.parent = null;
                                TeleporterSpawner.SetActive(true);
                            }
                            TeleporterSpawner.transform.position = SpawnedPoint + new Vector3(Random.Range(-5f,5f),2f,Random.Range(-5f,5f));
                            TeleporterSpawnerDropped = true;
                            FlashIndicatorSound.PlayOneShot(TeleportIndicatorSound);
                        }
                    }
                    if(TeleportCooldown >= TeleportCooldownMax){
                        gameObject.transform.position = TeleporterSpawner.transform.position;
                        TeleporterSpawner.SetActive(false);
                        TeleporterSpawner.transform.parent = transform;
                        TeleporterSpawner.transform.position = transform.position;
                        TeleporterSpawnerDropped = false;
                        TeleportCooldown = 0; 
                        CanTeleport = false;
                        FlashIndicatorSound.PlayOneShot(SuccessfulTeleportSound);
                    }
                }
            }
        }

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), EnemyAIUpdateInterval);
    }


    void OnDestroy(){Destroy(TeleporterSpawner);}
}
