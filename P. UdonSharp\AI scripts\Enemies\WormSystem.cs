﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class WormSystem : UdonSharpBehaviour
{
    public GameObject MainObject;
    public GameObject wormHead;
    public GameObject[] wormSegments = new GameObject[19];
    public float segmentDistance = 10f;

    private Vector3 oldHeadPosition;

    // Simple position history for the worm head
    private Vector3[] positionHistory;
    private int historySize = 20; // Keep this relatively small for performance
    private int currentIndex = 0;

    void Start()
    {
        oldHeadPosition = wormHead.transform.position;
        positionHistory = new Vector3[historySize];

        for (int i = 0; i < historySize; i++){positionHistory[i] = wormHead.transform.position;}

        FreezeStartWorm();
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    public void UpdateCustom()
    {
        RecordHeadPosition();
        MoveBody();

        if(MainObject != null){
            if(MainObject.activeSelf == false && wormSegments[0].activeSelf == true){for (int i = 0; i < wormSegments.Length; i++){wormSegments[i].SetActive(false);}}
            else if(MainObject.activeSelf == true && wormSegments[0].activeSelf == false){for (int i = 0; i < wormSegments.Length; i++){wormSegments[i].SetActive(true);}}
        }

        oldHeadPosition = wormHead.transform.position;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.03f);
    }

    private void RecordHeadPosition()
    {
        if(wormHead == null){return;}
        positionHistory[currentIndex] = wormHead.transform.position;
        currentIndex = (currentIndex + 1) % historySize;
    }

    public void FreezeStartWorm()
    {
        for (int i = 0; i < wormSegments.Length; i++){wormSegments[i].transform.parent = null;}
    }

    public void MoveBody()
    {
        int spacing = Mathf.Max(1, historySize / (wormSegments.Length + 1));

        for (int i = 0; i < wormSegments.Length; i++)
        {
            int historyIndex = (currentIndex - (i + 1) * spacing);
            if (historyIndex < 0) historyIndex += historySize;

            if (i == 0)
            {
                Vector3 targetPosition = positionHistory[historyIndex];
                Vector3 direction = targetPosition - wormSegments[i].transform.position;

                if (direction != Vector3.zero)
                {
                    wormSegments[i].transform.rotation = Quaternion.LookRotation(direction);
                    wormSegments[i].transform.position = targetPosition;
                }
            }
            else
            {
                Vector3 direction = wormSegments[i-1].transform.position - wormSegments[i].transform.position;

                if (direction != Vector3.zero)
                {
                    wormSegments[i].transform.rotation = Quaternion.LookRotation(direction);
                    wormSegments[i].transform.position = wormSegments[i-1].transform.position -wormSegments[i-1].transform.forward * segmentDistance;
                }
            }
        }
    }

    void OnDestroy(){for (int i = 0; i < wormSegments.Length; i++){Destroy(wormSegments[i]);}}
}
