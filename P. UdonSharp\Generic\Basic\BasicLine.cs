﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicLine : UdonSharpBehaviour
{
    public LineRenderer LineRenderer;
    public Transform[] Transforms;

    void Start()
    {
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.05f);
    }
    public void UpdateCustom()
    {
        if(LineRenderer != null || Transforms[0] != null || Transforms[1] != null){
            LineRenderer lineRenderer = GetComponent<LineRenderer>();
            lineRenderer.SetPosition(0, Transforms[0].position);
            lineRenderer.SetPosition(1, Transforms[1].position);
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.05f);
        }
    }
}
