using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicBonePositioner : UdonSharpBehaviour
{
    public int BoneID; //0 is Head, 1 is LeftHand, 2 is RightHand, 3 is LeftFoot, 4 is RightFoot
    private VRCPlayerApi localPlayer;

    [Header("Fallback Options for Incompatible Avatars")]
    public bool useHeadFallbackForHands = true;
    public bool usePlayerPositionFallbackForFeet = true;
    public Vector3 handOffsetFromHead = new Vector3(0.3f, -0.2f, 0.1f); // Default hand offset from head
    public Vector3 footOffsetFromPlayer = new Vector3(0.2f, -1.0f, 0.0f); // Default foot offset from player position

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        gameObject.transform.parent = null;
    }

    public override void PostLateUpdate()
    {
        if (localPlayer == null) return;

        if (BoneID == 0){ // Head
            VRCPlayerApi.TrackingData headTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
            if (headTracking.position != Vector3.zero){
                transform.position = headTracking.position;
                transform.rotation = headTracking.rotation;
            }
        }
        if (BoneID == 1){ // Left Hand
            VRCPlayerApi.TrackingData leftHandTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.LeftHand);
            if (leftHandTracking.position != Vector3.zero){
                transform.position = leftHandTracking.position;
                transform.rotation = leftHandTracking.rotation;
            }
            else if (useHeadFallbackForHands){ // Fallback for avatars without hands
                VRCPlayerApi.TrackingData headTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
                if (headTracking.position != Vector3.zero){
                    Vector3 leftHandOffset = new Vector3(-handOffsetFromHead.x, handOffsetFromHead.y, handOffsetFromHead.z);
                    Vector3 fallbackPosition = headTracking.position + (headTracking.rotation * leftHandOffset);
                    transform.position = fallbackPosition;
                    transform.rotation = headTracking.rotation;
                }
            }
        }
        if (BoneID == 2){ // Right Hand
            VRCPlayerApi.TrackingData rightHandTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.RightHand);
            if (rightHandTracking.position != Vector3.zero){
                transform.position = rightHandTracking.position;
                transform.rotation = rightHandTracking.rotation;
            }
            else if (useHeadFallbackForHands){ // Fallback for avatars without hands
                VRCPlayerApi.TrackingData headTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
                if (headTracking.position != Vector3.zero){
                    Vector3 rightHandOffset = handOffsetFromHead; // Use positive X for right hand
                    Vector3 fallbackPosition = headTracking.position + (headTracking.rotation * rightHandOffset);
                    transform.position = fallbackPosition;
                    transform.rotation = headTracking.rotation;
                }
            }
        }
        if (BoneID == 3){ // Left Foot
            Vector3 leftFootPos = localPlayer.GetBonePosition(HumanBodyBones.LeftFoot);
            if (leftFootPos != Vector3.zero){transform.position = leftFootPos;}
            else if (usePlayerPositionFallbackForFeet){ // Fallback for avatars without feet
                Vector3 leftFootOffset = new Vector3(-footOffsetFromPlayer.x, footOffsetFromPlayer.y, footOffsetFromPlayer.z);
                Vector3 fallbackPosition = localPlayer.GetPosition() + (localPlayer.GetRotation() * leftFootOffset);
                transform.position = fallbackPosition;
            }
        }
        if (BoneID == 4){ // Right Foot
            Vector3 rightFootPos = localPlayer.GetBonePosition(HumanBodyBones.RightFoot);
            if (rightFootPos != Vector3.zero){transform.position = rightFootPos;}
            else if (usePlayerPositionFallbackForFeet){ // Fallback for avatars without feet
                Vector3 rightFootOffset = footOffsetFromPlayer; // Use positive X for right foot
                Vector3 fallbackPosition = localPlayer.GetPosition() + (localPlayer.GetRotation() * rightFootOffset);
                transform.position = fallbackPosition;
            }
        }
    }
}