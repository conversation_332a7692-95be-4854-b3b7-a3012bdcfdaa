using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicBonePositioner : UdonSharpBehaviour
{
    public int BoneID; //0 is Head, 1 is LeftHand, 2 is RightHand, 3 is LeftFoot, 4 is RightFoot
    private VRCPlayerApi localPlayer;

    [Header("Fallback Options for Incompatible Avatars")]
    public bool useHeadFallbackForHands = true;
    public bool usePlayerPositionFallbackForFeet = true;
    public Vector3 handOffsetFromHead = new Vector3(0.3f, -0.2f, 0.1f); // Default hand offset from head
    public Vector3 footOffsetFromPlayer = new Vector3(0.2f, -1.0f, 0.0f); // Default foot offset from player position

    // Cached values for optimization
    private VRCPlayerApi.TrackingDataType trackingType;
    private HumanBodyBones boneType;
    private bool isHandBone;
    private bool isFootBone;
    private bool isLeftSide;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        gameObject.transform.parent = null;

        // Cache bone configuration for optimization
        switch (BoneID)
        {
            case 0: // Head
                trackingType = VRCPlayerApi.TrackingDataType.Head;
                isHandBone = false;
                isFootBone = false;
                break;
            case 1: // Left Hand
                trackingType = VRCPlayerApi.TrackingDataType.LeftHand;
                isHandBone = true;
                isFootBone = false;
                isLeftSide = true;
                break;
            case 2: // Right Hand
                trackingType = VRCPlayerApi.TrackingDataType.RightHand;
                isHandBone = true;
                isFootBone = false;
                isLeftSide = false;
                break;
            case 3: // Left Foot
                boneType = HumanBodyBones.LeftFoot;
                isHandBone = false;
                isFootBone = true;
                isLeftSide = true;
                break;
            case 4: // Right Foot
                boneType = HumanBodyBones.RightFoot;
                isHandBone = false;
                isFootBone = true;
                isLeftSide = false;
                break;
        }
    }

    public override void PostLateUpdate()
    {
        if (localPlayer == null) return;

        if (isFootBone)
        {
            // Handle feet using bone position
            Vector3 footPos = localPlayer.GetBonePosition(boneType);
            if (footPos != Vector3.zero){transform.position = footPos;}
            else if (usePlayerPositionFallbackForFeet)
            {
                // Calculate foot offset based on side
                Vector3 footOffset = isLeftSide ?
                    new Vector3(-footOffsetFromPlayer.x, footOffsetFromPlayer.y, footOffsetFromPlayer.z) :
                    footOffsetFromPlayer;
                Vector3 fallbackPosition = localPlayer.GetPosition() + (localPlayer.GetRotation() * footOffset);
                transform.position = fallbackPosition;
            }
        }
        else
        {
            // Handle head and hands using tracking data
            VRCPlayerApi.TrackingData tracking = localPlayer.GetTrackingData(trackingType);
            if (tracking.position != Vector3.zero)
            {
                transform.position = tracking.position;
                transform.rotation = tracking.rotation;
            }
            else if (isHandBone && useHeadFallbackForHands)
            {
                // Hand fallback to head position
                VRCPlayerApi.TrackingData headTracking = localPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
                if (headTracking.position != Vector3.zero)
                {
                    // Calculate hand offset based on side
                    Vector3 handOffset = isLeftSide ?
                        new Vector3(-handOffsetFromHead.x, handOffsetFromHead.y, handOffsetFromHead.z) :
                        handOffsetFromHead;
                    Vector3 fallbackPosition = headTracking.position + (headTracking.rotation * handOffset);
                    transform.position = fallbackPosition;
                    transform.rotation = headTracking.rotation;
                }
            }
        }
    }
}