%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c333ccfdd0cbdbc4ca30cef2dd6e6b9b, type: 3}
  m_Name: Dummy2
  m_EditorClassIdentifier: 
  serializedUdonProgramAsset: {fileID: 11400000, guid: ace54a0dd8bfb55498bb7b155f1593e2,
    type: 2}
  udonAssembly: 
  assemblyError: 
  sourceCsScript: {fileID: 11500000, guid: de7a990826143aa40b4cd5eb5b0777de, type: 3}
  scriptVersion: 2
  compiledVersion: 2
  behaviourSyncMode: 1
  hasInteractEvent: 0
  scriptID: -4883744275835536777
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: fieldDefinitions
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UdonSharp.Compiler.FieldDefinition,
        UdonSharp.Editor]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 10
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: HasInterstellarArmor
    - Name: $v
      Entry: 7
      Data: 2|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: HasInterstellarArmor
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 3|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Boolean, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: DamageText
    - Name: $v
      Entry: 7
      Data: 5|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: DamageText
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 6|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: TMPro.TextMeshPro, Unity.TextMeshPro
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 7|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: DamageTextDPS
    - Name: $v
      Entry: 7
      Data: 8|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: DamageTextDPS
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 6
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 9|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: CanBeHit
    - Name: $v
      Entry: 7
      Data: 10|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: CanBeHit
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 11|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ImmunityFramesDoneDelay
    - Name: $v
      Entry: 7
      Data: 12|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ImmunityFramesDoneDelay
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 13|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Single, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 13
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 14|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: DPSDamage
    - Name: $v
      Entry: 7
      Data: 15|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: DPSDamage
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 16|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 16
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 17|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: Particle
    - Name: $v
      Entry: 7
      Data: 18|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: Particle
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 19|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.ParticleSystem, UnityEngine.ParticleSystemModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 19
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 20|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AudioSource
    - Name: $v
      Entry: 7
      Data: 21|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: AudioSource
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 22|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.AudioSource, UnityEngine.AudioModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 22
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 23|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: damageLayer
    - Name: $v
      Entry: 7
      Data: 24|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: damageLayer
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 25|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.LayerMask, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 26|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: Damage
    - Name: $v
      Entry: 7
      Data: 27|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: Damage
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 16
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 16
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 28|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
