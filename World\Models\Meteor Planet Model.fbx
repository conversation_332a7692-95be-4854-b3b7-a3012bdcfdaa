; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 11
		Day: 21
		Hour: 21
		Minute: 35
		Second: 44
		Millisecond: 888
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\ev0x2naq_Meteor Planet Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\ev0x2naq_Meteor Planet Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2875304658880, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2868786653136, "Geometry::Scene", "Mesh" {
		Vertices: *168 {
			a: 5.96590042114258,-477.480506896973,133.587551116943,-261.209201812744,-477.480506896973,133.587551116943,-261.209201812744,-388.150429725647,133.587551116943,5.96590042114258,-388.150429725647,133.587551116943,-261.209201812744,-477.480506896973,-133.587551116943,-261.209201812744,-388.150429725647,-133.587551116943,5.96590042114258,-477.480506896973,-133.587551116943,5.96590042114258,-388.150429725647,-133.587551116943,-42.4451172351837,-388.150429725647,85.1765334606171,-212.79821395874,-388.150429725647,85.1765334606171,-256.31251335144,-199.701154232025,85.1765394210815,-85.9594404697418,-199.701154232025,85.1765394210815,-42.4451172351837,-388.150429725647,-85.1765394210815,-85.9594404697418,-199.701154232025,-85.1765394210815,-212.79821395874,-388.150429725647,-85.1765394210815,-256.31251335144,-199.701154232025,-85.1765394210815,-98.0052351951599,-199.701154232025,73.1307506561279,-244.266748428345,-199.701154232025,73.1307506561279,-299.817895889282,-1.29973888397217,73.1307506561279,-153.556370735168,-1.29973888397217,73.1307506561279,-98.0052351951599,-199.701154232025,-73.1307506561279,-153.556370735168,-1.29973888397217,-73.1307566165924,-244.266748428345,-199.701154232025,-73.1307506561279,-299.817895889282,-1.29973888397217,-73.1307566165924,-168.423342704773,-1.29973888397217,58.2637667655945,-284.95090007782,-1.29973888397217,58.2637667655945,-272.315049171448,170.570039749146,58.2637667655945,-155.787467956543,170.570039749146,58.2637667655945,-168.423342704773,-1.29973888397217,-58.2637906074524,-155.787467956543,170.570039749146,-58.2637906074524,-284.95090007782,-1.29973888397217,-58.2637906074524,-272.315049171448,170.570039749146,-58.2637906074524,-164.027214050293,170.570039749146,50.0240206718445,-264.07527923584,170.570039749146,50.0240206718445,-177.503442764282,374.513578414917,50.0240206718445,-90.8592581748962,324.489545822144,50.0240206718445,-164.027214050293,170.570039749146,-50.0240445137024,-90.8592581748962,324.489545822144,-50.0240385532379,-264.07527923584,170.570039749146,-50.0240445137024,
-177.503442764282,374.513578414917,-50.0240385532379,199.769830703735,286.637592315674,50.0240206718445,199.769830703735,286.637592315674,-50.0240445137024,299.817895889282,286.637592315674,-50.0240445137024,299.817895889282,286.637592315674,50.0240206718445,12.4335885047913,477.480506896973,-50.0240445137024,12.4335885047913,377.43239402771,-50.0240445137024,12.4335885047913,377.43239402771,50.0240206718445,12.4335885047913,477.480506896973,50.0240206718445,158.011627197266,477.480506896973,-50.0240445137024,158.011627197266,377.43239402771,-50.0240445137024,158.011627197266,377.43239402771,50.0240206718445,158.011627197266,477.480506896973,50.0240206718445,256.245040893555,423.686408996582,-50.0240445137024,189.540195465088,354.590177536011,-50.0240445137024,189.540195465088,354.590177536011,50.0240206718445,256.245040893555,423.686408996582,50.0240206718445
		} 
		PolygonVertexIndex: *216 {
			a: 0,3,2,-2,1,2,5,-5,4,5,7,-7,6,7,3,-1,6,0,1,-5,8,11,10,-10,12,13,11,-9,9,10,15,-15,14,15,13,-13,16,19,18,-18,20,21,19,-17,17,18,23,-23,22,23,21,-21,24,27,26,-26,28,29,27,-25,25,26,31,-31,30,31,29,-29,32,35,34,-34,36,37,35,-33,33,34,39,-39,38,39,37,-37,9,2,3,-9,7,12,8,-4,14,5,2,-10,5,14,12,-8,17,10,11,-17,13,20,16,-12,22,15,10,-18,15,22,20,-14,25,18,19,-25,21,28,24,-20,30,23,18,-26,23,30,28,-22,33,26,27,-33,29,36,32,-28,38,31,26,-34,31,38,36,-30,42,43,40,-42,39,44,45,-38,37,45,46,-36,34,47,44,-40,35,46,47,-35,44,48,49,-46,45,49,50,-47,47,51,48,-45,46,50,51,-48,48,52,53,-50,49,53,54,-51,51,55,52,-49,50,54,55,-52,52,42,41,-54,53,41,40,-55,55,43,42,-53,54,40,43,-56
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *648 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-3.16290140744968e-08,1,-0,-3.16290140744968e-08,1,-0,-3.16290140744968e-08,1,-0,-3.16290140744968e-08,1,0.974361717700958,0.224987298250198,0,0.974361717700958,0.224987298250198,0,0.974361717700958,0.224987298250198,0,0.974361717700958,0.224987298250198,0,-0.974361777305603,-0.224987179040909,0,-0.974361777305603,-0.224987179040909,0,-0.974361777305603,-0.224987179040909,0,-0.974361777305603,-0.224987179040909,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0.962965607643127,0.26962423324585,0,0.962965607643127,0.26962423324585,0,0.962965607643127,0.26962423324585,0,0.962965607643127,0.26962423324585,0,-0.962965607643127,-0.269624292850494,0,-0.962965607643127,-0.269624292850494,0,-0.962965607643127,-0.269624292850494,0,-0.962965607643127,-0.269624292850494,0,-0,-3.00424503052454e-08,-1,-0,-3.00424503052454e-08,-1,-0,-3.00424503052454e-08,-1,-0,-3.00424503052454e-08,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0.997308313846588,-0.0733221545815468,0,0.997308313846588,-0.0733221545815468,0,0.997308313846588,-0.0733221545815468,0,0.997308313846588,-0.0733221545815468,0,-0.997308313846588,0.073322020471096,0,-0.997308313846588,0.073322020471096,0,-0.997308313846588,0.073322020471096,0,-0.997308313846588,0.073322020471096,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0.903149724006653,-0.429325819015503,0,0.903149664402008,-0.429325848817825,0,0.903149724006653,-0.429325819015503,0,0.903149724006653,-0.429325819015503,0,-0.920500099658966,0.390742361545563,0,-0.920500099658966,0.390742361545563,0,-0.920500099658966,0.390742361545563,0,-0.920500099658966,0.390742361545563,0,6.77614275801375e-09,3.10989030083419e-08,-1,1.35522855160275e-08,2.34732517867542e-08,-1,6.77614275801375e-09,3.10989030083419e-08,-1,-0,3.87245542299297e-08,-1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1.61845559176754e-08,-2.80324776724683e-08,-1,-3.69445380954403e-08,-1.40162388362342e-08,-1,-5.7704522049562e-08,0,-1,-3.69445380954403e-08,-1.40162388362342e-08,-1,0.456126689910889,-0.889914870262146,0,0.456126689910889,-0.889914870262146,0,0.456126689910889,-0.889914870262146,0,0.456126689910889,-0.889914870262146,0,-0.476585060358047,0.879128396511078,0,-0.476585030555725,0.879128396511078,0,-0.476585030555725,0.879128396511078,0,-0.476585030555725,0.879128396511078,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0.586698234081268,-0.809805631637573,0,-0.586698234081268,-0.809805631637573,0,-0.586698234081268,-0.809805631637573,0,-0.586698234081268,-0.809805631637573,0,0.480311959981918,0.87709778547287,0,0.480311959981918,0.87709778547287,0,0.480311959981918,0.87709778547287,0,0.480311959981918,0.87709778547287,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0.988857746124268,-0.148863404989243,0,-0.988857746124268,-0.148863404989243,0,-0.988857746124268,-0.148863404989243,0,-0.988857746124268,-0.148863404989243,0,0.952993273735046,0.302991569042206,0,0.952993273735046,0.302991569042206,0,0.952993273735046,0.302991569042206,0,0.952993273735046,0.302991569042206,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1
			} 
			NormalsW: *216 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *648 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,3.16290140744968e-08,-0,1,3.16290140744968e-08,-0,1,3.16290140744968e-08,-0,1,3.16290140744968e-08,-0.224987298250198,0.974361717700958,0,-0.224987298250198,0.974361717700958,0,-0.224987298250198,0.974361717700958,0,-0.224987298250198,0.974361717700958,0,-0.224987179040909,0.974361777305603,-0,-0.224987179040909,0.974361777305603,-0,-0.224987179040909,0.974361777305603,-0,-0.224987179040909,0.974361777305603,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0.26962423324585,0.962965607643127,0,-0.26962423324585,0.962965607643127,0,-0.26962423324585,0.962965607643127,0,-0.26962423324585,0.962965607643127,0,-0.269624292850494,0.962965607643127,-0,-0.269624292850494,0.962965607643127,-0,-0.269624292850494,0.962965607643127,-0,-0.269624292850494,0.962965607643127,-0,0,1,-3.00424503052454e-08,0,1,-3.00424503052454e-08,0,1,-3.00424503052454e-08,0,1,-3.00424503052454e-08,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0.0733221545815468,0.997308313846588,-0,0.0733221545815468,0.997308313846588,-0,0.0733221545815468,0.997308313846588,-0,0.0733221545815468,0.997308313846588,-0,0.073322020471096,0.997308313846588,-0,0.073322020471096,0.997308313846588,-0,0.073322020471096,0.997308313846588,-0,0.073322020471096,0.997308313846588,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0.258819013834,0.965925872325897,-0,0.499999910593033,0.866025447845459,-0,0.258819013834,0.965925872325897,-0,0,1,-0,0.429325819015503,0.903149724006653,-0,0.429325848817825,0.903149664402008,-0,0.429325819015503,0.903149724006653,-0,0.429325819015503,0.903149724006653,-0,0.390742361545563,0.920500099658966,-0,0.390742361545563,0.920500099658966,-0,0.390742361545563,0.920500099658966,-0,0.390742361545563,0.920500099658966,-0,0.258819013834,0.965925872325897,3.17930286541923e-08,0.499999910593033,0.866025447845459,2.71045745847687e-08,0.258819013834,0.965925872325897,3.17930286541923e-08,0,1,3.87245542299297e-08,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,7.90554679497291e-08,-0,1,1.58110935899458e-07,-0,1,7.90554679497291e-08,-0,1,0,-0,1,7.90555247931479e-08,-0,1,0,-0,1,7.90555247931479e-08,-0,1,1.58111049586296e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,4.52069798484445e-07,-0,1,0,-0,1,4.52069798484445e-07,-0,1,9.04139596968889e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-3.83052508823312e-07,-0,1,-6.63803632505733e-07,-0,1,-3.83052508823312e-07,-0,1,-1.02301363824608e-07,-0,1,6.72226974529622e-07,-0,1,1.34445394905924e-06,-0,1,6.72226974529622e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-4.53693187043387e-16,1,-2.80324776724683e-08,-5.17823480848109e-16,1,-1.40162388362342e-08,0,1,-0,-5.17823480848109e-16,1,-1.40162388362342e-08,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,0,1,0,0,1,0,0,1,0,0,1,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0.148863404989243,0.988857746124268,-0,-0.148863404989243,0.988857746124268,-0,-0.148863404989243,0.988857746124268,-0,-0.148863404989243,0.988857746124268,-0,-0.302991569042206,0.952993273735046,0,-0.302991569042206,0.952993273735046,0,-0.302991569042206,0.952993273735046,0,-0.302991569042206,0.952993273735046,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *216 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *648 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.965925872325897,-0.258819013834,0,0.866025447845459,-0.499999910593033,0,0.965925872325897,-0.258819013834,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0.965925872325897,0.258819013834,1.50373580254382e-09,-0.866025447845459,0.499999910593033,0,-0.965925872325897,0.258819013834,1.50373580254382e-09,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,7.90554679497291e-08,-1,0,1.58110935899458e-07,-1,0,7.90554679497291e-08,-1,0,0,-1,0,7.90555247931479e-08,-1,0,0,-1,0,7.90555247931479e-08,-1,0,1.58111049586296e-07,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,4.52069798484445e-07,-1,0,0,-1,0,4.52069798484445e-07,-1,0,9.04139596968889e-07,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-3.83052508823312e-07,-1,0,-6.63803632505733e-07,-1,0,-3.83052508823312e-07,-1,0,-1.02301363824608e-07,-1,0,6.72226974529622e-07,-1,0,1.34445394905924e-06,-1,0,6.72226974529622e-07,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,1.61845559176754e-08,-1,0,3.69445380954403e-08,-1,0,5.7704522049562e-08,-1,0,3.69445380954403e-08,0.889914870262146,0.456126689910889,0,0.889914870262146,0.456126689910889,0,0.889914870262146,0.456126689910889,0,0.889914870262146,0.456126689910889,0,-0.879128396511078,-0.476585060358047,0,-0.879128396511078,-0.476585030555725,0,-0.879128396511078,-0.476585030555725,0,-0.879128396511078,-0.476585030555725,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.809805691242218,-0.586698234081268,0,0.809805691242218,-0.586698234081268,0,0.809805691242218,-0.586698234081268,0,0.809805691242218,-0.586698234081268,0,-0.87709778547287,0.480311959981918,0,-0.87709778547287,0.480311959981918,0,-0.87709778547287,0.480311959981918,0,-0.87709778547287,0.480311959981918,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *216 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *432 {
				a: 2.72692680358887,0.00860500335693359,0.05517578125,0.00860500335693359,0.05517578125,0.901905298233032,2.72692680358887,0.901905298233032,2.61792755126953,0.00860500335693359,-0.0538234710693359,0.00860500335693359,-0.0538234710693359,0.901905298233032,2.61792755126953,0.901905298233032,-0.05517578125,0.00860500335693359,-2.72692680358887,0.00860500335693359,-2.72692680358887,0.901905298233032,-0.05517578125,0.901905298233032,0.0538234710693359,0.00860500335693359,-2.61792755126953,0.00860500335693359,-2.61792755126953,0.901905298233032,0.0538234710693359,0.901905298233032,2.72692680358887,-0.0538234710693359,0.05517578125,-0.0538234710693359,0.05517578125,2.61792755126953,2.72692680358887,2.61792755126953,2.24281668663025,0.901905298233032,0.539285778999329,0.901905298233032,0.656898021697998,2.78639817237854,2.36042881011963,2.78639817237854,-0.430286586284637,1.03985738754272,-2.13381743431091,1.03985738754272,-2.13381743431091,2.92801690101624,-0.430286586284637,2.92801690101624,2.13381743431091,0.933745682239532,0.430286586284637,0.933745682239532,0.430286586284637,2.8219051361084,2.13381743431091,2.8219051361084,-0.539285778999329,0.901905298233032,-2.24281668663025,0.901905298233032,-2.36042881011963,2.78639817237854,-0.656898021697998,2.78639817237854,2.23997092247009,2.78639817237854,0.777355909347534,2.78639817237854,0.494156002998352,4.77041244506836,1.95677101612091,4.77041244506836,-0.550744473934174,2.44191145896912,-2.01335954666138,2.44191145896912,-2.01335954666138,4.44603586196899,-0.550744473934174,4.44603586196899,2.01335954666138,2.64859127998352,0.550744473934174,2.64859127998352,0.550744473934174,4.6527156829834,2.01335954666138,4.6527156829834,-0.777355909347534,2.78639817237854,-2.23997092247009,2.78639817237854,-1.95677101612091,4.77041244506836,-0.494156002998352,4.77041244506836,1.80810141563416,4.77041244506836,0.642825663089752,4.77041244506836,0.642825663089752,6.48910999298096,1.80810141563416,6.48910999298096,-0.699414134025574,4.77041244506836,-1.86468982696533,
4.77041244506836,-1.86468982696533,6.48910999298096,-0.699414134025574,6.48910999298096,1.86468982696533,4.77041244506836,0.699414134025574,4.77041244506836,0.699414134025574,6.48910999298096,1.86468982696533,6.48910999298096,-0.642825663089752,4.77041244506836,-1.80810141563416,4.77041244506836,-1.80810141563416,6.48910999298096,-0.642825663089752,6.48910999298096,1.72570395469666,6.48910999298096,0.725223183631897,6.48910999298096,0.860182583332062,8.2784252166748,1.86066341400146,8.2784252166748,-0.781811654567719,6.60052299499512,-1.78229236602783,6.60052299499512,-1.78229236602783,8.39492034912109,-0.781811654567719,8.39492034912109,1.78229236602783,6.52527523040771,0.781811654567719,6.52527523040771,0.781811654567719,8.31967258453369,1.78229236602783,8.31967258453369,-0.725223183631897,6.48910999298096,-1.72570395469666,6.48910999298096,-1.86066341400146,8.2784252166748,-0.860182583332062,8.2784252166748,-2.72692680358887,2.61792755126953,-0.539285778999329,2.13381743431091,-2.24281668663025,2.13381743431091,-0.05517578125,2.61792755126953,-2.72692680358887,-0.0538234710693359,-2.72692680358887,2.61792755126953,-2.24281668663025,2.13381743431091,-0.05517578125,2.61792755126953,-0.539285778999329,0.430286586284637,-0.539285778999329,2.13381743431091,-2.24281668663025,0.430286586284637,-0.05517578125,-0.0538234710693359,-2.24281668663025,0.430286586284637,-0.05517578125,-0.0538234710693359,-2.72692680358887,-0.0538234710693359,-0.539285778999329,0.430286586284637,-2.36042881011963,2.13381743431091,-0.777355909347534,2.01335954666138,-2.23997092247009,2.01335954666138,-0.656898021697998,2.13381743431091,-2.36042881011963,0.430286586284637,-2.36042881011963,2.13381743431091,-2.23997092247009,2.01335954666138,-0.656898021697998,2.13381743431091,-0.777355909347534,0.550744473934174,-0.777355909347534,2.01335954666138,-2.23997092247009,0.550744473934174,-0.656898021697998,0.430286586284637,-2.23997092247009,0.550744473934174,-0.656898021697998,0.430286586284637,-2.36042881011963,0.430286586284637,-0.777355909347534,
0.550744473934174,-1.95677101612091,2.01335954666138,-0.642825663089752,1.86468982696533,-1.80810141563416,1.86468982696533,-0.494156002998352,2.01335954666138,-1.95677101612091,0.550744473934174,-1.95677101612091,2.01335954666138,-1.80810141563416,1.86468982696533,-0.494156002998352,2.01335954666138,-0.642825663089752,0.699414134025574,-0.642825663089752,1.86468982696533,-1.80810141563416,0.699414134025574,-0.494156002998352,0.550744473934174,-1.80810141563416,0.699414134025574,-0.494156002998352,0.550744473934174,-1.95677101612091,0.550744473934174,-0.642825663089752,0.699414134025574,-1.80810141563416,1.86468982696533,-0.725223183631897,1.78229236602783,-1.72570395469666,1.78229236602783,-0.642825663089752,1.86468982696533,-1.80810141563416,0.699414134025574,-1.80810141563416,1.86468982696533,-1.72570395469666,1.78229236602783,-0.642825663089752,1.86468982696533,-0.725223183631897,0.781811654567719,-0.725223183631897,1.78229236602783,-1.72570395469666,0.781811654567719,-0.642825663089752,0.699414134025574,-1.72570395469666,0.781811654567719,-0.642825663089752,0.699414134025574,-1.80810141563416,0.699414134025574,-0.725223183631897,0.781811654567719,1.99769830703735,0.500240206718445,1.99769830703735,-0.500240445137024,2.99817895889282,-0.500240445137024,2.99817895889282,0.500240206718445,1.77503442764282,3.74513578414917,0.908592581748962,3.24489545822144,-0.124335892498493,4.77480506896973,-0.124335892498493,3.7743239402771,0.671513378620148,-0.500240385532379,0.671513378620148,0.500240206718445,1.83221828937531,-0.500240445137024,1.83221828937531,0.500240206718445,-0.224392592906952,0.500240206718445,-0.224392592906952,-0.500240385532379,-2.38490796089172,0.500240206718445,-2.38490796089172,-0.500240445137024,-0.908592581748962,3.24489545822144,-1.77503442764282,3.74513578414917,0.124335885047913,3.7743239402771,0.124335885047913,4.77480506896973,-0.124335885047913,4.77480506896973,-0.124335885047913,3.7743239402771,-1.58011627197266,4.77480506896973,-1.58011627197266,3.7743239402771,0.124335885047913,-0.500240445137024,
0.124335885047913,0.500240206718445,1.58011627197266,-0.500240445137024,1.58011627197266,0.500240206718445,-0.124335885047913,0.500240206718445,-0.124335885047913,-0.500240445137024,-1.58011627197266,0.500240206718445,-1.58011627197266,-0.500240445137024,0.124335885047913,3.7743239402771,0.124335885047913,4.77480506896973,1.58011627197266,3.7743239402771,1.58011627197266,4.77480506896973,-1.58011627197266,4.77480506896973,-1.58011627197266,3.7743239402771,-2.56245040893555,4.23686408996582,-1.89540195465088,3.54590177536011,-0.934802114963531,-0.500240445137024,-0.934802114963531,0.500240206718445,-0.545467138290405,-0.500240445137024,-0.545467138290405,0.500240206718445,0.907479524612427,0.500240206718445,0.907479524612427,-0.500240445137024,-0.212503090500832,0.500240206718445,-0.212503090500832,-0.500240445137024,1.58011627197266,3.7743239402771,1.58011627197266,4.77480506896973,1.89540195465088,3.54590177536011,2.56245040893555,4.23686408996582,-2.56245040893555,4.23686408996582,-1.89540195465088,3.54590177536011,-2.99817895889282,2.86637592315674,-1.99769830703735,2.86637592315674,-0.500240445137024,3.22423648834229,0.500240206718445,3.22423648834229,-0.500240445137024,2.53705382347107,0.500240206718445,2.53705382347107,-0.500240206718445,3.26130175590515,0.500240445137024,3.26130175590515,-0.500240206718445,1.82321381568909,0.500240445137024,1.82321381568909,1.89540195465088,3.54590177536011,2.56245040893555,4.23686408996582,1.99769830703735,2.86637592315674,2.99817895889282,2.86637592315674
			} 
			UVIndex: *216 {
				a: 0,3,2,1,4,7,6,5,8,11,10,9,12,15,14,13,16,19,18,17,20,23,22,21,24,27,26,25,28,31,30,29,32,35,34,33,36,39,38,37,40,43,42,41,44,47,46,45,48,51,50,49,52,55,54,53,56,59,58,57,60,63,62,61,64,67,66,65,68,71,70,69,72,75,74,73,76,79,78,77,80,83,82,81,85,87,84,86,88,94,90,89,92,95,91,93,97,99,96,98,101,103,100,102,104,110,106,105,108,111,107,109,113,115,112,114,117,119,116,118,120,126,122,121,124,127,123,125,129,131,128,130,133,135,132,134,136,142,138,137,140,143,139,141,145,147,144,146,150,151,148,149,152,154,155,153,156,158,159,157,160,162,163,161,164,166,167,165,168,170,171,169,172,174,175,173,176,178,179,177,180,182,183,181,184,186,187,185,188,190,191,189,192,194,195,193,196,198,199,197,200,202,203,201,204,206,207,205,208,210,211,209,212,214,215,213
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2868774805504, "Model::Meteor_Planet_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2875343101472, "Material::Meteor_Texture", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2873268608432, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Materials\Galaxy Related\space texture.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Materials\Galaxy Related\space texture.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Materials\Galaxy Related\space texture.png"
		RelativeFilename: "..\Materials\Galaxy Related\space texture.png"
	}
	Texture: 2874044247472, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Materials\Galaxy Related\space texture.png"
		RelativeFilename: "..\Materials\Galaxy Related\space texture.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Meteor_Planet_Model, Model::RootNode
	C: "OO",2868774805504,0
	
	;Material::Meteor_Texture, Model::Meteor_Planet_Model
	C: "OO",2875343101472,2868774805504
	
	;Geometry::Scene, Model::Meteor_Planet_Model
	C: "OO",2868786653136,2868774805504
	
	;Texture::DiffuseColor_Texture, Material::Meteor_Texture
	C: "OO",2874044247472,2875343101472
	
	;Texture::DiffuseColor_Texture, Material::Meteor_Texture
	C: "OP",2874044247472,2875343101472, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2873268608432,2874044247472
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
