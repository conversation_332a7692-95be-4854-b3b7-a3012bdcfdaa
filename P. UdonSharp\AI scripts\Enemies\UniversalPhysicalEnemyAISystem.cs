﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalPhysicalEnemyAISystem : UdonSharpBehaviour
{
    public GameObject Body;
    public Rigidbody rb;
    public AudioSource GlobalAudioSource;

    public int Attacks;

    public float Force = 250f;
    public float SightRange = 50f;
    public Vector3 SpawnedPoint;

    public VRCPlayerApi localPlayer;

    //Flash indicator effect
    public ParticleSystem FlashIndicator;
    public AudioSource FlashIndicatorSound;

    //Teleport Indicator effect
    public GameObject TeleportIndicator;
    public ParticleSystem TeleportIndicatorEffect;
    public AudioClip TeleportIndicatorSound,SuccessfulTeleportSound;

    //Cooldowns
    public bool HasRan, HasDashed, IsIdle;

    //Optional Animations
    public Animator BodyAnimator;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        TeleportIndicator.SetActive(false);
        TeleportIndicator.transform.position = transform.position;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
    }

    public void OnEnable()
    {
        SpawnedPoint = transform.position;
    }

    public void EnemyAISystem()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
            if(HasRan == true && HasDashed == false){
                int Attack = Random.Range(1, 3);
                if(Attack == 1){Attacks = 2;}
                else if(Attack == 2){Attacks = 3;}
            }
            else if(HasRan == false && HasDashed == true){
                int Attack = Random.Range(1, 3);
                if(Attack == 1){Attacks = 1;}
                else if(Attack == 2){Attacks = 2;}
            }
            else if(HasRan == true && HasDashed == true){Attacks = 2;}
            else{Attacks = Random.Range(1, 4);}

            IsIdle = false;

            if(Attacks == 1)
            {
                if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 1);}
                HasRan = true;
                HasDashed = false;
                SendCustomEventDelayedSeconds(nameof(AttackOne), 1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(5f, 10f));
            }
            else if(Attacks == 2)
            {
                if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 2);}
                HasRan = false;
                HasDashed = false;
                SendCustomEventDelayedSeconds(nameof(AttackTwo), 0.2f);
            }
            else if(Attacks == 3){
                if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 3);}
                HasRan = false;
                HasDashed = true;
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(AttackThree), 0.2f);
            }
        }
        else{
            Attacks = Random.Range(1, 3);

            IsIdle = true;

            if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 0);}

            if(Attacks == 1)
            {
                SendCustomEventDelayedSeconds(nameof(IdleOne), 1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(1f, 4f));
            }
            else if(Attacks == 2)
            {
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(IdleTwo), 0.1f);
            }
        }
    }

    public void AttackOne()
    {
        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        rb.AddForce((Body.transform.forward)*Force);
        if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(AttackOne), 0.05f);}
    }

    public void AttackTwo()
    {
        if(gameObject.activeInHierarchy == true){TeleportIndicator.SetActive(true);}
        TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f));
        rb.velocity = Vector3.zero;
        rb.isKinematic = true;
    
        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(AttackTwo2), 1.5f);
    }

    public void AttackTwo2()
    {
        Body.transform.position = TeleportIndicator.transform.position;
        rb.velocity = Vector3.zero;
        rb.isKinematic = false;
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }


    public void AttackThree()
    {
        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        //Body goes up in the air and waits a second before dashing at the player
        rb.AddForce(Vector3.up*Force*30);

        FlashIndicator.Play();
        FlashIndicatorSound.PlayOneShot(FlashIndicatorSound.clip);
        SendCustomEventDelayedSeconds(nameof(AttackThree2), 1f);
    }

    public void AttackThree2()
    {
        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);

        //Dashes at the player
        rb.AddForce((playerPos - transform.position).normalized*Force*50);

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.5f);
    }


    //Idle Attacks
    public void IdleOne()
    {
        // Get player position
        Vector3 playerPos = SpawnedPoint + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f));
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        rb.AddForce((Body.transform.forward)*Force);
        if(Attacks == 1 && IsIdle == true){SendCustomEventDelayedSeconds(nameof(IdleOne), 0.5f);}
    }

    public void IdleTwo()
    {
        if(gameObject.activeInHierarchy == true){TeleportIndicator.SetActive(true);}
        TeleportIndicator.transform.position = SpawnedPoint + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f));
        rb.isKinematic = true;
        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(IdleTwo2), 2f);
    }

    public void IdleTwo2()
    {
        Body.transform.position = TeleportIndicator.transform.position;
        rb.isKinematic = false;
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }

    void OnDestroy(){Destroy(TeleportIndicator);}
}
