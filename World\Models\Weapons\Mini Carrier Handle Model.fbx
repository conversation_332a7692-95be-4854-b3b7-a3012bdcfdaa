; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 11
		Day: 22
		Hour: 15
		Minute: 51
		Second: 37
		Millisecond: 876
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\Weapons\fb8u3y80_Mini Carrier Handle Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\Weapons\fb8u3y80_Mini Carrier Handle Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2038720799728, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2037942373296, "Geometry::Scene", "Mesh" {
		Vertices: *48 {
			a: 1.99999809265137,-28.5124123096466,1.99997425079346,1.99999809265137,-26.1033594608307,1.99997425079346,4.49999570846558,-27.3078888654709,-2.38418579101562e-05,-1.99999809265137,-26.1033594608307,1.99997425079346,-1.99999809265137,-28.5124123096466,1.99997425079346,-4.49999570846558,-27.3078888654709,-2.38418579101562e-05,-0,-27.3078858852386,4.49997186660767,-1.99999809265137,-26.1033594608307,-2.00002193450928,-1.99999809265137,-28.5124242305756,-2.00002193450928,-0,-27.3078918457031,-4.50001955032349,1.99999809265137,-26.1033594608307,-2.00002193450928,1.99999809265137,-28.5124242305756,-2.00002193450928,-1.99999809265137,-10.6702782213688,1.99999809265137,1.99999809265137,-10.6702782213688,1.99999809265137,-1.99999809265137,-10.6702066957951,-1.99999809265137,1.99999809265137,-10.6702066957951,-1.99999809265137
		} 
		PolygonVertexIndex: *84 {
			a: 1,2,-11,10,9,-8,7,5,-4,3,6,-2,0,6,-5,11,2,-1,8,9,-12,4,5,-9,1,0,-3,0,1,-7,4,3,-6,3,4,-7,8,7,-10,7,8,-6,11,10,-3,10,11,-10,0,8,-12,8,0,-5,12,1,-14,1,12,-4,14,3,-13,3,14,-8,10,14,-16,14,10,-8,1,15,-14,15,1,-11,14,13,-16,13,14,-13
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *252 {
				a: 0.434057503938675,0.900885224342346,0,0.434057503938675,0.900885224342346,0,0.434057503938675,0.900885224342346,0,-0,0.900884747505188,-0.434058368206024,-0,0.900884747505188,-0.434058368206024,-0,0.900884747505188,-0.434058368206024,-0.434057503938675,0.900885224342346,0,-0.434057503938675,0.900885224342346,0,-0.434057503938675,0.900885224342346,0,-0,0.900885581970215,0.434056609869003,-0,0.900885581970215,0.434056609869003,-0,0.900885581970215,0.434056609869003,0.151332959532738,-0.976829648017883,0.151334628462791,-0,-0.900885581970215,0.434056609869003,-0.151332959532738,-0.976829648017883,0.151334628462791,0.151333078742027,-0.976830124855042,-0.151331409811974,0.434057503938675,-0.900885224342346,2.68484973275918e-06,0.151332959532738,-0.976829648017883,0.151334628462791,-0.151333078742027,-0.976830124855042,-0.151331409811974,-0,-0.900884747505188,-0.434058368206024,0.151333078742027,-0.976830124855042,-0.151331409811974,-0.151332959532738,-0.976829648017883,0.151334628462791,-0.434057503938675,-0.900885224342346,2.68484973275918e-06,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.702781915664673,0,0.702781915664673,0.702781915664673,0,0.702781915664673,0.624695062637329,0,0.780868828296661,0.702781915664673,0,0.702781915664673,0.702781915664673,0,0.702781915664673,0.780868828296661,0,0.624695062637329,-0.702781915664673,0,0.702781915664673,-0.702781915664673,0,0.702781915664673,-0.624695062637329,0,0.780868828296661,-0.702781915664673,0,0.702781915664673,-0.702781915664673,0,0.702781915664673,-0.780868828296661,0,0.624695062637329,-0.702781915664673,0,-0.702781915664673,-0.702781915664673,0,-0.702781915664673,-0.780868828296661,0,-0.624695062637329,-0.702781915664673,0,-0.702781915664673,-0.702781915664673,0,-0.702781915664673,-0.624695062637329,0,-0.780868828296661,0.702781915664673,0,-0.702781915664673,0.702781915664673,0,-0.702781915664673,0.624695062637329,0,-0.780868828296661,0.702781915664673,0,-0.702781915664673,0.702781915664673,0,-0.702781915664673,0.780868828296661,0,-0.624695062637329,
0.151332959532738,-0.976829648017883,0.151334628462791,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.151333078742027,-0.976830124855042,-0.151331409811974,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.151332959532738,-0.976829648017883,0.151334628462791,-0.151332959532738,-0.976829648017883,0.151334628462791,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05
			} 
			NormalsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *252 {
				a: 4.31557118929504e-08,-2.07929495843473e-08,1.00000011920929,4.31557118929504e-08,-2.07929495843473e-08,1.00000011920929,4.31557118929504e-08,-2.07929495843473e-08,1.00000011920929,0,0.434058368206024,0.900884747505188,0,0.434058368206024,0.900884747505188,0,0.434058368206024,0.900884747505188,-4.31557154456641e-08,-2.07929513607041e-08,1.00000011920929,-4.31557154456641e-08,-2.07929513607041e-08,1.00000011920929,-4.31557154456641e-08,-2.07929513607041e-08,1.00000011920929,0,-0.434056609869003,0.900885581970215,0,-0.434056609869003,0.900885581970215,0,-0.434056609869003,0.900885581970215,-2.18035367538505e-10,0.153097882866859,0.988211095333099,-0,0.434056609869003,0.900885581970215,2.18035367538505e-10,0.153097882866859,0.988211095333099,0.0685856565833092,-0.142349228262901,0.987437427043915,-1.12222346615454e-06,2.43953445533407e-06,1.00000011920929,-0.0685870125889778,0.142352268099785,0.987436890602112,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,0,-0.434058368206024,0.900884747505188,1.54472967750507e-09,-0.153094634413719,0.988211572170258,0.0685870125889778,0.142352268099785,0.987436890602112,1.12222346615454e-06,2.43953445533407e-06,1.00000011920929,-0.0685856565833092,-0.142349228262901,0.987437427043915,-7.58493889918554e-09,0.993883669376373,7.58493889918554e-09,-7.58493889918554e-09,0.993883669376373,7.58493889918554e-09,-1.88463715744547e-08,1,1.50770969042924e-08,-7.58493889918554e-09,0.993883669376373,7.58493889918554e-09,-7.58493889918554e-09,0.993883669376373,7.58493889918554e-09,-0,1,-0,7.58494067554238e-09,0.993883728981018,7.58494067554238e-09,7.58494067554238e-09,0.993883728981018,7.58494067554238e-09,1.88463733508115e-08,1,1.50770986806492e-08,7.58494067554238e-09,0.993883728981018,7.58494067554238e-09,7.58494067554238e-09,0.993883728981018,7.58494067554238e-09,0,1,-0,6.83018175351435e-09,0.993883728981018,-6.83018175351435e-09,6.83018175351435e-09,0.993883728981018,-6.83018175351435e-09,0,1,-0,6.83018175351435e-09,0.993883728981018,-6.83018175351435e-09,6.83018175351435e-09,0.993883728981018,-6.83018175351435e-09,
1.35768152276228e-08,1,-1.08614521820982e-08,-5.46414558044717e-09,0.993883728981018,-5.46414558044717e-09,-5.46414558044717e-09,0.993883728981018,-5.46414558044717e-09,-1.35768152276228e-08,1,-1.08614521820982e-08,-5.46414558044717e-09,0.993883728981018,-5.46414558044717e-09,-5.46414558044717e-09,0.993883728981018,-5.46414558044717e-09,0,1,0,-2.18035367538505e-10,0.153097882866859,0.988211095333099,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,1.54472967750507e-09,-0.153094634413719,0.988211572170258,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,-2.18035367538505e-10,0.153097882866859,0.988211095333099,2.18035367538505e-10,0.153097882866859,0.988211095333099,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,0,1,3.40593793168864e-08,0,1,3.40593793168864e-08,0,1,6.81187586337728e-08,0,1,3.40593793168864e-08,0,1,3.40593793168864e-08,0,1,-0,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,-0,1,3.40593793168864e-08,-0,1,3.40593793168864e-08,-0,1,6.81187586337728e-08,-0,1,3.40593793168864e-08,-0,1,3.40593793168864e-08,-0,1,0,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1
			} 
			BinormalsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *252 {
				a: -0.900885224342346,0.434057503938675,4.79036756928508e-08,-0.900885224342346,0.434057503938675,4.79036756928508e-08,-0.900885224342346,0.434057503938675,4.79036756928508e-08,-1,0,0,-1,0,0,-1,0,0,-0.900885224342346,-0.434057503938675,-4.79036792455645e-08,-0.900885224342346,-0.434057503938675,-4.79036792455645e-08,-0.900885224342346,-0.434057503938675,-4.79036792455645e-08,-1,0,0,-1,0,0,-1,0,0,0.988482892513275,0.149548903107643,-0.0231687556952238,1,0,0,0.988482892513275,-0.149548903107643,0.0231687556952238,0.986100554466248,0.159811124205589,-0.0454543828964233,0.900885224342346,0.434057503938675,-4.79036792455645e-08,0.986100435256958,0.159811362624168,0.0454552322626114,0.988482892513275,-0.1495491117239,-0.0231682825833559,1,0,0,0.988482892513275,0.1495491117239,0.0231682825833559,0.986100435256958,-0.159811362624168,-0.0454552322626114,0.900885224342346,-0.434057503938675,4.79036756928508e-08,0.986100554466248,-0.159811124205589,0.0454543828964233,0.70710676908493,1.07927347059444e-08,-0.707106709480286,0.70710676908493,1.07927347059444e-08,-0.707106709480286,0.780868828296661,2.41351312268989e-08,-0.624695062637329,0.70710676908493,1.07927347059444e-08,-0.707106709480286,0.70710676908493,1.07927347059444e-08,-0.707106709480286,0.624695062637329,0,-0.780868828296661,0.70710676908493,-1.07927373704797e-08,0.70710676908493,0.70710676908493,-1.07927373704797e-08,0.70710676908493,0.780868768692017,-2.41351330032558e-08,0.624695062637329,0.70710676908493,-1.07927373704797e-08,0.70710676908493,0.70710676908493,-1.07927373704797e-08,0.70710676908493,0.624695062637329,0,0.780868828296661,-0.70710676908493,9.7187786707309e-09,0.707106828689575,-0.70710676908493,9.7187786707309e-09,0.707106828689575,-0.624695062637329,0,0.780868828296661,-0.70710676908493,9.7187786707309e-09,0.707106828689575,-0.70710676908493,9.7187786707309e-09,0.707106828689575,-0.780868828296661,1.73868066610794e-08,0.624695062637329,-0.707106828689575,-7.77502329185609e-09,-0.70710676908493,-0.707106828689575,-7.77502329185609e-09,-0.70710676908493,
-0.780868828296661,-1.73868066610794e-08,-0.624695062637329,-0.707106828689575,-7.77502329185609e-09,-0.70710676908493,-0.707106828689575,-7.77502329185609e-09,-0.70710676908493,-0.624695062637329,0,-0.780868828296661,0.988482892513275,0.149548903107643,-0.0231687556952238,0.988482892513275,-0.1495491117239,-0.0231682825833559,0.988482892513275,0.1495491117239,0.0231682825833559,0.988482892513275,-0.1495491117239,-0.0231682825833559,0.988482892513275,0.149548903107643,-0.0231687556952238,0.988482892513275,-0.149548903107643,0.0231687556952238,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,-3.40593793168864e-08,1,-0,-3.40593793168864e-08,1,-0,-6.81187586337728e-08,1,-0,-3.40593793168864e-08,1,-0,-3.40593793168864e-08,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,3.40593793168864e-08,-1,-0,3.40593793168864e-08,-1,-0,6.81187586337728e-08,-1,-0,3.40593793168864e-08,-1,-0,3.40593793168864e-08,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *128 {
				a: 0.36430412530899,0,0.36430412530899,0.135019809007645,0.50442111492157,0.0675097331404686,0.140116974711418,0.135019809007645,0.140116974711418,0,0,0.0675097331404686,0.252210557460785,0.0675099045038223,0.140116974711418,0.135020479559898,0.140116974711418,0,0,0.0675102397799492,0.140116974711418,0.36430412530899,0.140116974711418,0.140116974711418,0,0.252210557460785,0.140116974711418,0.140116974711418,0.36430412530899,0.140116974711418,0.252210557460785,0,0.36430412530899,0.140116974711418,0.36430412530899,0.36430412530899,0.50442111492157,0.252210557460785,0.36430412530899,0.36430412530899,0.140116974711418,0.36430412530899,0.252210557460785,0.50442111492157,0.140116974711418,0.135020479559898,0.140116974711418,0,0,0.0675104036927223,0.36430412530899,0.36430412530899,0.140116974711418,0.36430412530899,0.252210557460785,0.50442111492157,0.252210557460785,0.0675104036927223,0.252210557460785,0.0675102397799492,0.252210557460785,0.0675099045038223,0.36430412530899,0.140116974711418,0.36430412530899,0.36430412530899,0.50442111492157,0.252210557460785,0.36430412530899,0.36430412530899,0.36430412530899,0.140116974711418,0.140116974711418,0.140116974711418,0.140116974711418,0.140116974711418,0.36430412530899,0.140116974711418,0.252210557460785,0,0.140116974711418,0.36430412530899,0.140116974711418,0.36430412530899,0.140116974711418,0.140116974711418,0,0.252210557460785,0.140116974711418,0.999995350837708,0.36430412530899,0.999995350837708,0.36430412530899,0.135019809007645,0.140116974711418,0.135019809007645,0.140118315815926,1,0.364305466413498,0.9999960064888,0.36430412530899,0.135020479559898,0.140116974711418,0.135020479559898,0.140116974711418,0.135020479559898,0.140116974711418,1,0.36430412530899,1,0.36430412530899,0.135020479559898,1.33626087972516e-06,0,0,0.86497551202774,0.224187150597572,0.86497950553894,0.22418849170208,0,0.36430412530899,0.140118315815926,0.140116974711418,0.140118315815926,0.140116974711418,0.364305466413498,0.36430412530899,0.364305466413498
			} 
			UVIndex: *84 {
				a: 10,12,11,13,15,14,16,18,17,19,21,20,25,27,26,31,33,32,37,39,38,41,43,42,1,0,2,0,1,30,4,3,5,3,4,6,8,7,9,7,8,28,23,22,24,22,23,29,34,36,35,36,34,40,44,46,45,46,44,47,48,50,49,50,48,51,52,54,53,54,52,55,56,58,57,58,56,59,60,62,61,62,60,63
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2041061785024, "Model::Mini_Carrier_Handle_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2043218243280, "Material::Fence_Stone", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2033194996992, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Sprites\rocks 2.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\..\Sprites\rocks 2.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Sprites\rocks 2.png"
		RelativeFilename: "..\..\Sprites\rocks 2.png"
	}
	Texture: 2033194996512, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Sprites\rocks 2.png"
		RelativeFilename: "..\..\Sprites\rocks 2.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mini_Carrier_Handle_Model, Model::RootNode
	C: "OO",2041061785024,0
	
	;Material::Fence_Stone, Model::Mini_Carrier_Handle_Model
	C: "OO",2043218243280,2041061785024
	
	;Geometry::Scene, Model::Mini_Carrier_Handle_Model
	C: "OO",2037942373296,2041061785024
	
	;Texture::DiffuseColor_Texture, Material::Fence_Stone
	C: "OO",2033194996512,2043218243280
	
	;Texture::DiffuseColor_Texture, Material::Fence_Stone
	C: "OP",2033194996512,2043218243280, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2033194996992,2033194996512
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
