﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class NextFloorSystem : UdonSharpBehaviour
{
    public BattleTowerMainSystem BattleTowerMainSystem;
    public GameObject MainObject;
    public Transform teleportDestination;
    public VRCPlayerApi localPlayer;

    public void Start(){
        localPlayer = Networking.LocalPlayer;
    }

    public override void OnPlayerTriggerEnter(VRCPlayerApi player){
        localPlayer = Networking.LocalPlayer;
        if (player != localPlayer) return;

        // Increment floor before resetting the system
        if(BattleTowerMainSystem != null){
            if(BattleTowerMainSystem.FloorComplete == true){
                BattleTowerMainSystem.Floor += 1;
                BattleTowerMainSystem.SavingFloor();
                BattleTowerMainSystem.FloorComplete = false;
            }
        }

        if(MainObject != null){MainObject.SetActive(false);}
        if(MainObject != null){MainObject.SetActive(true);}
        localPlayer.TeleportTo(teleportDestination.position, teleportDestination.rotation);
    }
}
