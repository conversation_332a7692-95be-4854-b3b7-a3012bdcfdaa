﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GalaxySystem : UdonSharpBehaviour
{
    public Material[] Skybox;
    public Material ScreenEffectSkybox;
    public int dayOfMonth;
    public bool IsRandomizedByEvent, RandomizeStats, RandomGalaxyAlreadyStarted;

    public VRCPlayerApi localPlayer;

    void Start(){
        localPlayer = Networking.LocalPlayer;

        UpdateGalaxy();
    }

    void UpdateGalaxy()
    {

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        dayOfMonth = networkTime.Day-1; // Get the day of the month (1 - 31)

        RenderSettings.skybox = Skybox[dayOfMonth];

        localPlayer.SetWalkSpeed(4f);
        localPlayer.SetRunSpeed(7f);
        localPlayer.SetStrafeSpeed(8f);
        localPlayer.SetJumpImpulse(5f);
        localPlayer.SetGravityStrength(1f);

        RandomGalaxyAlreadyStarted = false;
    }
    public void ScreenEffect(){RenderSettings.skybox = ScreenEffectSkybox;}


    public void RandomGalaxyIntervalStart(){
        if(RandomGalaxyAlreadyStarted) return;
        RandomGalaxyInterval();
        RandomGalaxyAlreadyStarted = true;
    }

    public void RandomGalaxyInterval(){

        if(RandomizeStats){
            float SetWalkSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;     // Steps of 0.5
            float SetRunSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetStrafeSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetJumpImpulseValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
            float SetGravityStrengthValue = Mathf.Round(Random.Range(0.1f, 2f) * 10f) / 10f;  // Steps of 0.1

            localPlayer.SetWalkSpeed(SetWalkSpeedValue);
            localPlayer.SetRunSpeed(SetRunSpeedValue);
            localPlayer.SetStrafeSpeed(SetStrafeSpeedValue);
            localPlayer.SetJumpImpulse(SetJumpImpulseValue);
            localPlayer.SetGravityStrength(SetGravityStrengthValue);
        }

        int RandomGalaxy = Random.Range(0, Skybox.Length);
        RenderSettings.skybox = Skybox[RandomGalaxy];

        if(IsRandomizedByEvent){
            SendCustomEventDelayedSeconds(nameof(RandomGalaxyInterval), 30f);
            SendCustomEventDelayedSeconds(nameof(ScreenEffect), 25f);
        }
        else{
            UpdateGalaxy();
        }
    }
}

//UNIQUE CODE I WILL PROBABLY NEVER USE
//public int dayOfWeek;
//dayOfWeek = (int)networkTime.DayOfWeek; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday