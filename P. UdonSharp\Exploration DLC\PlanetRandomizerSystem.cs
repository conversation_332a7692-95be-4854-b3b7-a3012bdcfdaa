﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.UI;
using TMPro;
using VRC.SDK3.Persistence;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PlanetRandomizerSystem : UdonSharpBehaviour
{
    //System
    public GameObject PlayerSystemObject;
    public GameObject LoadingScreen, PlanetScreen, NotEnoughCoins;
    public GameObject[] ButtonUIs,ButtonUIsLoading;
    public GameObject[] DoorClosed, DoorOpen;
    public bool PlanetHasRings, PlanetHasMoon, UpdateResourcesAndEnemiesCooldown;
    public MeshGeneratorPlanet MeshGeneratorPlanet;
    public EntityGeneratorPlanet EntityGeneratorPlanet;
    public GameObject MainPlanetEnvironment, EventTargetMain;
    public int EnvironmentEvent, GeneratedResourcesType;

    //Visuals
    public GameObject[] Rings, Moons, WeatherState, WindyState, EnvironmentState;
    public MeshRenderer[] PlanetMesh<PERSON>enderer, MoonMesh<PERSON><PERSON><PERSON>, RingMeshRenderer;
    public MeshRenderer FloorRenderer;
    public Color PlanetColor, MoonColor, RingColor;
    public Image[] PlanetImage;

    //Extras
    public Image PlanetSizeScreen, TerrainNoiseScreen, EnvironmentScreen, ResourcesScreen, EnemiesScreen;
    public Sprite[] PlanetSizeSprites, TerrainNoiseSprites, EnvironmentSprites, ResourcesSprites, EnemiesSprites;
    public TextMeshPro PlanetSizeText, TerrainNoiseText, EnvironmentText, ResourcesText, EnemiesText;
    public TextMeshPro PlanetNameText;
    public GameObject DebugSizes;
    public int TravelPrice = 100;
    public Color[] ScreenIconColor;
    public TextMeshPro PriceText;
    public TextMeshPro TipText;
    public string[] tips; 


    private int MapSize, PlanetTerrainNoise, PlayerMoney;
    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");

        LoadingScreen.SetActive(true);
        PlanetScreen.SetActive(false);

        for (int i = 0; i < ButtonUIs.Length; i++){ButtonUIs[i].SetActive(true);}
        for (int i = 0; i < ButtonUIsLoading.Length; i++){ButtonUIsLoading[i].SetActive(false);}
        NotEnoughCoins.SetActive(false);

        for (int i = 0; i < Rings.Length; i++){Rings[i].SetActive(false);}
        for (int i = 0; i < Moons.Length; i++){Moons[i].SetActive(false);}
        for (int i = 0; i < WeatherState.Length; i++){WeatherState[i].SetActive(false);}
        for (int i = 0; i < WindyState.Length; i++){WindyState[i].SetActive(false);}
        for (int i = 0; i < DoorClosed.Length; i++){DoorClosed[i].SetActive(true);}
        for (int i = 0; i < DoorOpen.Length; i++){DoorOpen[i].SetActive(false);}

        PlanetSizeScreen.sprite = PlanetSizeSprites[0];
        PlanetSizeText.text = "Planet Size: ???";
        TerrainNoiseScreen.sprite = TerrainNoiseSprites[0];
        TerrainNoiseText.text = "Terrain: ???";
        EnvironmentScreen.sprite = EnvironmentSprites[0];
        EnvironmentText.text = "Envir. Event: ???";
        ResourcesScreen.sprite = ResourcesSprites[0];
        ResourcesText.text = "Resources Remaining: ???";
        EnemiesScreen.sprite = EnemiesSprites[0];
        EnemiesText.text = "Enemies Remaining: ???";
        PriceText.text = "Travel For: " + TravelPrice.ToString();

        DebugSizes.SetActive(false);
    }

    public void OnEnable(){TipText.text = "Advice:  " + tips[Random.Range(0, tips.Length)];}

    public void RandomizePlanet()
    {
        PlayerMoney = PlayerData.GetInt(localPlayer, "EmperCoins");

        if(PlayerSystemObject != null){
            MainPlayerCurrencySystem mainPlayerCurrencySystem = PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>();

            if(mainPlayerCurrencySystem != null && PlayerMoney >= TravelPrice){
                mainPlayerCurrencySystem.RemovePoints(TravelPrice);
                SendCustomEventDelayedSeconds(nameof(HidePlanetScreen), 0.1f);
            }
            else{
                NotEnoughCoins.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseNotEnoughCoins), 1f);
            }
        }
    }

    public void CloseNotEnoughCoins(){NotEnoughCoins.SetActive(false);}

    public void HidePlanetScreen()
    {
        LoadingScreen.SetActive(true);
        PlanetScreen.SetActive(false);
        MainPlanetEnvironment.SetActive(true);
        for (int i = 0; i < ButtonUIs.Length; i++){ButtonUIs[i].SetActive(false);}
        for (int i = 0; i < ButtonUIsLoading.Length; i++){ButtonUIsLoading[i].SetActive(true);}

        PlanetColor = new Color(Random.value, Random.value, Random.value);
        MoonColor = new Color(Random.value, Random.value, Random.value);
        RingColor = new Color(Random.value, Random.value, Random.value);

        for (int i = 0; i < PlanetMeshRenderer.Length; i++){PlanetMeshRenderer[i].material.color = PlanetColor;}
        for (int i = 0; i < MoonMeshRenderer.Length; i++){MoonMeshRenderer[i].material.color = MoonColor;}
        for (int i = 0; i < RingMeshRenderer.Length; i++){RingMeshRenderer[i].material.color = RingColor;}

        for (int i = 0; i < PlanetImage.Length; i++){PlanetImage[i].color = PlanetColor;}

        int Ring = Random.Range(0, 2);
        if (Ring == 0){PlanetHasRings = false;}
        else{PlanetHasRings = true;}

        int Moon = Random.Range(0, 2);
        if (Moon == 0){PlanetHasMoon = false;}
        else{PlanetHasMoon = true;}

        if (PlanetHasRings == true){for (int i = 0; i < Rings.Length; i++){Rings[i].SetActive(true);}}
        else{for (int i = 0; i < Rings.Length; i++){Rings[i].SetActive(false);}}
        if (PlanetHasMoon == true){for (int i = 0; i < Moons.Length; i++){Moons[i].SetActive(true);}}
        else{for (int i = 0; i < Moons.Length; i++){Moons[i].SetActive(false);}}

        for (int i = 0; i < DoorClosed.Length; i++){DoorClosed[i].SetActive(true);}
        for (int i = 0; i < DoorOpen.Length; i++){DoorOpen[i].SetActive(false);}

        FloorRenderer.material.color = PlanetColor;

        int PlanetTerrainNoiseDecider = Random.Range(1, 101);
        if (PlanetTerrainNoiseDecider >= 1 && PlanetTerrainNoiseDecider <= 25){PlanetTerrainNoise = 0;}
        else if (PlanetTerrainNoiseDecider >= 26 && PlanetTerrainNoiseDecider <= 75){PlanetTerrainNoise = 1;}
        else if (PlanetTerrainNoiseDecider >= 76 && PlanetTerrainNoiseDecider <= 100){PlanetTerrainNoise = 2;}

        if(PlanetTerrainNoise == 0){ //Flat
            MeshGeneratorPlanet.lacunarity = Random.Range(0, 2);
            MeshGeneratorPlanet.octaves = Random.Range(0, 2);
            MeshGeneratorPlanet.centerCraterPercent = Random.Range(10, 25);
        }
        else if(PlanetTerrainNoise == 1){ //Hard
            MeshGeneratorPlanet.lacunarity = Random.Range(2, 5);
            MeshGeneratorPlanet.octaves = Random.Range(2, 5);
            MeshGeneratorPlanet.centerCraterPercent = Random.Range(7, 20);
        }
        else if(PlanetTerrainNoise == 2){ //Spiky
            MeshGeneratorPlanet.lacunarity = Random.Range(5, 6);
            MeshGeneratorPlanet.octaves = Random.Range(5, 6);
            MeshGeneratorPlanet.centerCraterPercent = Random.Range(7, 20);
        }
        MeshGeneratorPlanet.borderPercent = Random.Range(80, 90);

        int MapSizeDecider = Random.Range(1, 101);

        if (MapSizeDecider >= 1 && MapSizeDecider <= 25){MapSize = 1;}
        else if (MapSizeDecider >= 26 && MapSizeDecider <= 75){MapSize = 2;}
        else if (MapSizeDecider >= 76 && MapSizeDecider <= 100){MapSize = 3;}

        if (MapSize == 1){MeshGeneratorPlanet.xSize = 100; MeshGeneratorPlanet.zSize = 100;}
        else if (MapSize == 2){MeshGeneratorPlanet.xSize = 150; MeshGeneratorPlanet.zSize = 150;}
        else if (MapSize == 3){MeshGeneratorPlanet.xSize = 200; MeshGeneratorPlanet.zSize = 200;}

        MeshGeneratorPlanet.CreateNewMap();
        PlanetSizeScreen.sprite = PlanetSizeSprites[0];
        PlanetSizeText.text = "Planet Size: ???";
        PlanetSizeScreen.color = ScreenIconColor[3];
        TerrainNoiseScreen.sprite = TerrainNoiseSprites[0];
        TerrainNoiseText.text = "Terrain: ???";
        TerrainNoiseScreen.color = ScreenIconColor[3];
        EnvironmentScreen.sprite = EnvironmentSprites[0];
        EnvironmentText.text = "Envir. Event: ???";
        EnvironmentScreen.color = ScreenIconColor[3];
        ResourcesScreen.sprite = ResourcesSprites[0];
        ResourcesText.text = "Resources Remaining: ???";
        ResourcesScreen.color = ScreenIconColor[3];
        EnemiesScreen.sprite = EnemiesSprites[0];
        EnemiesText.text = "Enemies Remaining: ???";
        EnemiesScreen.color = ScreenIconColor[3];

        UpdateResourcesAndEnemiesCooldown = true;

        //change the rotation of the environment ring
        int RingRotation = Random.Range(0, 4); // 0 is x 30, 1 is x -30, 2 is z 30, 3 is z -30
        if (RingRotation == 0){Rings[1].transform.rotation = Quaternion.Euler(0, 0, 30);}
        else if (RingRotation == 1){Rings[1].transform.rotation = Quaternion.Euler(0, 0, -30);}
        else if (RingRotation == 2){Rings[1].transform.rotation = Quaternion.Euler(30, 0, 0);}
        else if (RingRotation == 3){Rings[1].transform.rotation = Quaternion.Euler(-30, 0, 0);}

        //change the rotation of the moon
        int Moonrotation = Random.Range(0, 366);
        Moons[1].transform.rotation = Quaternion.Euler(0, Moonrotation, 0);

        //Randomize weather state
        int RandomWeather = Random.Range(0, 4);
        for (int i = 0; i < WeatherState.Length; i++){WeatherState[i].SetActive(i == RandomWeather);}
        int RandomWindy = Random.Range(0, 4);
        for (int i = 0; i < WindyState.Length; i++){WindyState[i].SetActive(i == RandomWindy);}

        //Randomize Environment Event
        EnvironmentEvent = Random.Range(0, 3);
        for (int i = 0; i < EnvironmentState.Length; i++){EnvironmentState[i].SetActive(i == EnvironmentEvent);}

        //EntityGenerator
        if (MapSize == 1){
            EntityGeneratorPlanet.MaximumEntities = 40; 
            EntityGeneratorPlanet.DeleteAllRemainingEntities();

            EventTargetMain.transform.localScale = new Vector3(1, 1, 1);
        }
        else if (MapSize == 2){
            EntityGeneratorPlanet.MaximumEntities = 60; 
            EntityGeneratorPlanet.DeleteAllRemainingEntities();

            EventTargetMain.transform.localScale = new Vector3(1.5f, 1.5f, 1.5f);
        }
        else if (MapSize == 3){
            EntityGeneratorPlanet.MaximumEntities = 80; 
            EntityGeneratorPlanet.DeleteAllRemainingEntities();

            EventTargetMain.transform.localScale = new Vector3(2, 2, 2);
        }

        SendCustomEventDelayedSeconds(nameof(StartGeneratingEntities), 1f);
        SendCustomEventDelayedSeconds(nameof(ShowPlanetScreen), 5f);
    }
    public void StartGeneratingEntities(){
        //generate dead entities (25% of the total)
        int GeneratedDeadEntitiesType = Random.Range(0, 3);
        int GeneratedObstaclesType = Random.Range(0, 10);
        GeneratedResourcesType = Random.Range(0, 3);
        int GeneratedEnemyType = Random.Range(1, 101);
        if(GeneratedDeadEntitiesType == 0){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(0, 3), MapSize);}}
        else if(GeneratedDeadEntitiesType == 1){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(3, 7), MapSize);}}
        else if(GeneratedDeadEntitiesType == 2){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(7, 10), MapSize);}}
        //generate obstacles (second 25% of the total)
        if(GeneratedObstaclesType == 0){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 10, MapSize);}}
        else if(GeneratedObstaclesType == 1){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 11, MapSize);}}
        else if(GeneratedObstaclesType == 2){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 12, MapSize);}}
        else if(GeneratedObstaclesType == 3){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 13, MapSize);}}
        else if(GeneratedObstaclesType == 4){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 14, MapSize);}}
        else if(GeneratedObstaclesType == 5){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 15, MapSize);}}
        else if(GeneratedObstaclesType == 6){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, 17, MapSize);}}
        else if(GeneratedObstaclesType == 7){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(10, 16), MapSize);}}
        else if(GeneratedObstaclesType == 8){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(16, 19), MapSize);}}
        else if(GeneratedObstaclesType == 9){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(15, 20), MapSize);}}
        //generate resources (second 25% of the total)
        for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
            int ChancesOfAchievement = Random.Range(1, 101);
            if(ChancesOfAchievement >= 6){
                if(GeneratedResourcesType == 0){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(20, 23), MapSize);}
                else if(GeneratedResourcesType == 1){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(23, 26), MapSize);}
                else if(GeneratedResourcesType == 2){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(26, 30), MapSize);}
            }
            else if(ChancesOfAchievement < 6){EntityGeneratorPlanet.GenerateEntities(0, Random.Range(30, 33), MapSize);}
        }
        //generate enemies (last 25% of the total)
        if(GeneratedEnemyType <= 1){for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
            EntityGeneratorPlanet.GenerateEntities(1, 0, MapSize);}
        }
        else if(GeneratedEnemyType > 1 && GeneratedEnemyType <= 15){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
                int RandomCubix = Random.Range(0, 4);
                if(RandomCubix == 0){EntityGeneratorPlanet.GenerateEntities(1, 1, MapSize);}
                else if(RandomCubix == 1){EntityGeneratorPlanet.GenerateEntities(1, 2, MapSize);}
                else if(RandomCubix == 2){EntityGeneratorPlanet.GenerateEntities(1, 3, MapSize);}
                else if(RandomCubix == 3){EntityGeneratorPlanet.GenerateEntities(1, 4, MapSize);}
            }
        }
        else if(GeneratedEnemyType > 15 && GeneratedEnemyType <= 30){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(1, Random.Range(5, 8), MapSize);}
        }
        else if(GeneratedEnemyType > 30 && GeneratedEnemyType <= 45){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){EntityGeneratorPlanet.GenerateEntities(1, Random.Range(8, 10), MapSize);}
        }
        else if(GeneratedEnemyType > 45 && GeneratedEnemyType <= 60){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
                int ChancesOfEnemySize = Random.Range(1, 101);
                if(ChancesOfEnemySize >= 1 && ChancesOfEnemySize <= 75){EntityGeneratorPlanet.GenerateEntities(1, 10, MapSize);}
                else if(ChancesOfEnemySize > 75 && ChancesOfEnemySize <= 98){EntityGeneratorPlanet.GenerateEntities(1, 11, MapSize);}
                else if(ChancesOfEnemySize > 98 && ChancesOfEnemySize <= 100){EntityGeneratorPlanet.GenerateEntities(1, 12, MapSize);}
            }
        }
        else if(GeneratedEnemyType > 60 && GeneratedEnemyType <= 75){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
                int ChancesOfEnemySize = Random.Range(1, 101);
                if(ChancesOfEnemySize >= 1 && ChancesOfEnemySize <= 75){EntityGeneratorPlanet.GenerateEntities(1, 13, MapSize);}
                else if(ChancesOfEnemySize > 75 && ChancesOfEnemySize <= 98){EntityGeneratorPlanet.GenerateEntities(1, 14, MapSize);}
                else if(ChancesOfEnemySize > 98 && ChancesOfEnemySize <= 100){EntityGeneratorPlanet.GenerateEntities(1, 15, MapSize);}
            }
        }
        else if(GeneratedEnemyType > 75 && GeneratedEnemyType <= 90){
            for (int i = 0; i < EntityGeneratorPlanet.MaximumEntities/4; i++){
                int ChancesOfEnemySize = Random.Range(1, 101);
                if(ChancesOfEnemySize >= 1 && ChancesOfEnemySize <= 75){EntityGeneratorPlanet.GenerateEntities(1, 16, MapSize);}
                else if(ChancesOfEnemySize > 75 && ChancesOfEnemySize <= 98){EntityGeneratorPlanet.GenerateEntities(1, 17, MapSize);}
                else if(ChancesOfEnemySize > 98 && ChancesOfEnemySize <= 100){EntityGeneratorPlanet.GenerateEntities(1, 18, MapSize);}
            }
        }
        // 91 and above nothing!

        Random.Range(0, 3);

        MainPlanetEnvironment.SetActive(false);
    }

    public void ShowPlanetScreen()
    {

        if (MapSize == 1){PlanetSizeScreen.sprite = PlanetSizeSprites[1]; PlanetSizeText.text = "Planet Size: Small"; PlanetSizeScreen.color = ScreenIconColor[0];}
        else if (MapSize == 2){PlanetSizeScreen.sprite = PlanetSizeSprites[2]; PlanetSizeText.text = "Planet Size: Medium"; PlanetSizeScreen.color = ScreenIconColor[1];}
        else if (MapSize == 3){PlanetSizeScreen.sprite = PlanetSizeSprites[3]; PlanetSizeText.text = "Planet Size: Large"; PlanetSizeScreen.color = ScreenIconColor[2];}

        if(PlanetTerrainNoise == 0){TerrainNoiseScreen.sprite = TerrainNoiseSprites[1]; TerrainNoiseText.text = "Terrain: Flat"; TerrainNoiseScreen.color = ScreenIconColor[0];}
        else if(PlanetTerrainNoise == 1){TerrainNoiseScreen.sprite = TerrainNoiseSprites[2]; TerrainNoiseText.text = "Terrain: Mountainous"; TerrainNoiseScreen.color = ScreenIconColor[1];}
        else if(PlanetTerrainNoise == 2){TerrainNoiseScreen.sprite = TerrainNoiseSprites[3]; TerrainNoiseText.text = "Terrain: Spiky"; TerrainNoiseScreen.color = ScreenIconColor[2];}

        if(EnvironmentEvent == 0){EnvironmentScreen.sprite = EnvironmentSprites[1]; EnvironmentText.text = "Envir. Event: Cubix"; EnvironmentScreen.color = ScreenIconColor[0];}
        else if(EnvironmentEvent == 1){EnvironmentScreen.sprite = EnvironmentSprites[2]; EnvironmentText.text = "Envir. Event: Tornadoes"; EnvironmentScreen.color = ScreenIconColor[1];}
        else if(EnvironmentEvent == 2){EnvironmentScreen.sprite = EnvironmentSprites[3]; EnvironmentText.text = "Envir. Event: Infection"; EnvironmentScreen.color = ScreenIconColor[2];}

        if(GeneratedResourcesType == 0){ResourcesScreen.sprite = ResourcesSprites[1];}
        else if(GeneratedResourcesType == 1){ResourcesScreen.sprite = ResourcesSprites[2];}
        else if(GeneratedResourcesType == 2){ResourcesScreen.sprite = ResourcesSprites[3];}

        UpdateResourcesAndEnemiesCooldown = false;
        UpdateResourceAndEnemyCount();

        //Assign Name to Planet with either a letter of the alphabet or a number
        string FirstLetter = GetRandomCharacter();
        string SecondLetter = GetRandomCharacter();
        string ThirdLetter = GetRandomCharacter();
        string FourthLetter = GetRandomCharacter();
        string FifthLetter = GetRandomCharacter();

        PlanetNameText.text = "Planet Name: " + FirstLetter + SecondLetter + ThirdLetter + FourthLetter + FifthLetter;

        LoadingScreen.SetActive(false);
        PlanetScreen.SetActive(true);
        for (int i = 0; i < ButtonUIs.Length; i++){ButtonUIs[i].SetActive(true);}
        for (int i = 0; i < ButtonUIsLoading.Length; i++){ButtonUIsLoading[i].SetActive(false);}

        for (int i = 0; i < DoorClosed.Length; i++){DoorClosed[i].SetActive(false);}
        for (int i = 0; i < DoorOpen.Length; i++){DoorOpen[i].SetActive(true);}
    }

    public void UpdateResourceAndEnemyCount(){
        if(UpdateResourcesAndEnemiesCooldown == false){
            int ResourceCount = 0;
            int EnemyCount = 0;

            // Count resources in 3rd quarter (50-75% of PlacedObjects array)
            int totalEntities = EntityGeneratorPlanet.MaximumEntities;
            int resourceStartIndex = totalEntities / 2; // 50%
            int resourceEndIndex = (totalEntities * 3) / 4; // 75%

            for (int i = resourceStartIndex; i < resourceEndIndex; i++)
            {
                if (i < EntityGeneratorPlanet.PlacedObjects.Length && EntityGeneratorPlanet.PlacedObjects[i] != null){ResourceCount++;}
            }

            // Count enemies in last quarter (75-100% of PlacedObjects array)
            int enemyStartIndex = (totalEntities * 3) / 4; // 75%
            int enemyEndIndex = totalEntities; // 100%

            for (int i = enemyStartIndex; i < enemyEndIndex; i++)
            {
                if (i < EntityGeneratorPlanet.PlacedObjects.Length && EntityGeneratorPlanet.PlacedObjects[i] != null){EnemyCount++;}
            }

            if(ResourceCount > 10 && ResourceCount <= 20){ResourcesScreen.color = ScreenIconColor[0];}
            else if(ResourceCount > 5 && ResourceCount <= 10){ResourcesScreen.color = ScreenIconColor[1];}
            else if(ResourceCount >= 0 && ResourceCount <= 5){ResourcesScreen.color = ScreenIconColor[2];}

            if(EnemyCount > 10 && EnemyCount <= 20){EnemiesScreen.color = ScreenIconColor[0];}
            else if(EnemyCount > 5 && EnemyCount <= 10){EnemiesScreen.color = ScreenIconColor[1];}
            else if(EnemyCount >= 0 && EnemyCount <= 5){EnemiesScreen.color = ScreenIconColor[2];}
            ResourcesText.text = "Resources Remaining: " + ResourceCount;
            EnemiesText.text = "Enemies Remaining: " + EnemyCount;

            SendCustomEventDelayedSeconds(nameof(UpdateResourceAndEnemyCount), 1f);
        }
    }

    private string GetRandomCharacter()
    {
        // Array of all uppercase letters
        string[] letters = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M","N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

        // Choose between letter (0) or number (1)
        int choice = Random.Range(0, 2);

        if (choice == 0){return letters[Random.Range(0, letters.Length)];}
        else{return Random.Range(0, 10).ToString();}
    }
}
