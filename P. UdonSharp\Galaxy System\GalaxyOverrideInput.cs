﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GalaxyOverrideInput : UdonSharpBehaviour
{
    public GalaxySystem GalaxySystem;
    public bool RandomizeStats;
    void OnEnable()
    {
        if (!RandomizeStats) {GalaxySystem.RandomizeStats = false;}
        else {GalaxySystem.RandomizeStats = true;}
        GalaxySystem.IsRandomizedByEvent = true;
        GalaxySystem.RandomGalaxyIntervalStart();
    }
    void OnDisable()
    {
        GalaxySystem.RandomizeStats = false;
        GalaxySystem.IsRandomizedByEvent = false;
    }
}
