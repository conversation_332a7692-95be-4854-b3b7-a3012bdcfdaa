﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicStaticObjectAnimator : UdonSharpBehaviour
{

    public GameObject[] objects, objects2;

    public float Interval = 0.2f;

    private int ID, ID2;

    void Start()
    {
        SendCustomEventDelayedSeconds(nameof(Animator), Interval);
    }

    public void Animator()
    {
        if (objects.Length != 0){
            ID++;

            if (ID >= objects.Length){ID = 0;}
            for (int i = 0; i < objects.Length; i++)
            {
                if (i == ID){objects[i].SetActive(true);}
                else{objects[i].SetActive(false);}
            }
        }
        if (objects2.Length != 0){
            ID2++;
            if (ID2 >= objects2.Length){ID2 = 0;}
            for (int i = 0; i < objects2.Length; i++)
            {
                if (i == ID2){objects2[i].SetActive(true);}
                else{objects2[i].SetActive(false);}
            }
        }

        SendCustomEventDelayedSeconds(nameof(Animator), Interval);
    }
}
