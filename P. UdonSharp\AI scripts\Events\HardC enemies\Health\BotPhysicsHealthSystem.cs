﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BotPhysicsHealthSystem : UdonSharpBehaviour
{
    public int Health,HealthMax = 100;
    public bool IsDead;
    public float DeathDelay = 1f;
    public int State; //-1 = Blue, 0 = White, 1 = Green
    public GameObject[] StateActiveObjects;
    public MeshRenderer[] MeshRenderers;
    public Material[] Materials;
    public int CanDie = 1;
    public BotPhysicsEnemyAI AISystem;

    //Effects
    public ParticleSystem Particle;
    public ParticleSystem Particle2;
    public AudioSource AudioSource;
    public GameObject[] Body;
    public Rigidbody rb;
    public GameObject[] DamageAreas;
    public LayerMask damageLayer;
    public GameObject EnemyMainObject;

    //HealthBar
    public GameObject HealthBar;
    public TextMeshPro DamageText;
    public Slider HealthSlider;
    public bool CanBeHit;
    public float ImmunityFramesDoneDelay = 0.1f;

    public int MoneyDropAmount = 0;

    public void Start()
    {
        if(MeshRenderers != null && MeshRenderers.Length > 0){
            for (int i = 0; i < MeshRenderers.Length; i++){
                if(MeshRenderers[i] != null){
                    if(MeshRenderers[i].material != Materials[0]){MeshRenderers[i].material = Materials[0];}
                }
            }
        }
        rb = GetComponent<Rigidbody>();
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.maxValue = HealthMax;
        HealthSlider.value = Health;
        CanBeHit = true;
        SendCustomEventDelayedSeconds(nameof(AIUpdate), 0.05f);
    }

    void OnEnable()
    {
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.value = Health;
    }

    public void AIUpdate()
    {
        if(State == 0 && !IsDead && StateActiveObjects != null){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material != Materials[0]){MeshRenderers[i].material = Materials[0];}
                    }
                }
                AISystem.Multiplier = 1f;
            }
            StateActiveObjects[0].SetActive(false);
            StateActiveObjects[1].SetActive(false);
        }
        else if(State == -1 && !IsDead && StateActiveObjects != null){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material != Materials[1]){MeshRenderers[i].material = Materials[1];}
                    }
                }
                AISystem.Multiplier = 2f;
            }
            StateActiveObjects[0].SetActive(true);
            StateActiveObjects[1].SetActive(false);
        }
        else if(State == 1 && !IsDead && StateActiveObjects != null){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material != Materials[2]){MeshRenderers[i].material = Materials[2];}
                    }
                }
                if(Health < HealthMax){Health++; DamageText.text = Health.ToString() + "/" + HealthMax.ToString(); HealthSlider.value = Health;}
                if(Health > HealthMax){Health = HealthMax;}
                AISystem.Multiplier = 1f;
            }
            StateActiveObjects[0].SetActive(false);
            StateActiveObjects[1].SetActive(true);
        }

        if(CanDie == 0 && !IsDead){HealthBar.SetActive(false); Health = HealthMax; DamageText.text = Health.ToString() + "/" + HealthMax.ToString(); HealthSlider.value = Health;}
        else if(CanDie == 1 && !IsDead){HealthBar.SetActive(true);}

        SendCustomEventDelayedSeconds(nameof(AIUpdate), 1f);
    }

    public void ImmunityFramesDone(){CanBeHit = true;}
    public void Dead()
    {
        IsDead = true;

        if(MoneyDropAmount != 0){
            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(MoneyDropAmount);
        }

        if(Particle != null){Particle.Play();}
        for (int i = 0; i < Body.Length; i++){
            Body[i].SetActive(false);
        }
        if(rb != null){rb.isKinematic = true;}
        HealthBar.SetActive(false);
        if(DamageAreas != null){
            for (int i = 0; i < DamageAreas.Length; i++){
                DamageAreas[i].SetActive(false);
            }
        }
        SendCustomEventDelayedSeconds(nameof(Delete), DeathDelay);
    }
    public void Delete(){
        if(EnemyMainObject != null){Destroy(EnemyMainObject);}
        else{Destroy(gameObject);}
    }

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && !IsDead && CanDie == 1)
        {
            int Damage = DamageInput.Damage;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(DamageInput == null && CanBeHit && !IsDead && CanDie == 1)
        {
            int Damage = 1;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }

        CanBeInfected CanBeInfected = collision.gameObject.GetComponent<CanBeInfected>();
        if(!IsDead && CanBeInfected != null)
        {
            if(CanBeInfected.InfectionType == -1){State = 0;}
            else if(CanBeInfected.InfectionType == 0){State = -1;}
            else if(CanBeInfected.InfectionType == 1){State = 1;}
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !IsDead && CanDie == 1)
        {
            int Damage = DamageInput.Damage;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}
