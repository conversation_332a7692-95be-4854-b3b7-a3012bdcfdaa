﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PortalSystem : UdonSharpBehaviour
{
    //Base System Required
    public Animator PortalSystemAnimator;
    public int AnimState;
    public bool IsActive;
    //Enemy System
    public GameObject[] EnemiesRangePool;
    public GameObject[] EnemySlot;
    public int CheckCurrentEnemy,EnemySlotLeft,EnemySlotMax = 10;
    //Spawn Point
    public Transform EnemySpawnPos;

    //For player check
    private VRCPlayerApi localPlayer;

    void OnEnable()
    {
        localPlayer = Networking.LocalPlayer;
        IsActive = true;
        AnimState = 1;

        //EnemySystemStart
        EnemySlotLeft = EnemySlotMax;
        CheckCurrentEnemy = 0;

        for (int i = 0; i < EnemySlot.Length; i++){EnemySlot[i] = null;}

        //Timer for update
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }
    void OnDisable()
    {
        for (int i = 0; i < EnemySlot.Length; i++){Destroy(EnemySlot[i]);}

        IsActive = false;
        AnimState = 0;
        CheckCurrentEnemy = 0;
    }


    public void UpdateCustom()
    {
        PortalSystemAnimator.SetFloat("Portal State", AnimState);

        Debug.Log("Portal Initiated Test");

        if(EnemySlot[CheckCurrentEnemy] == null && AnimState == 1 && EnemySlotLeft > 0)
        {
            var EnemyTemp = Instantiate(EnemiesRangePool[Random.Range(0, EnemiesRangePool.Length)], EnemySpawnPos.position, EnemySpawnPos.rotation);
            EnemySlot[CheckCurrentEnemy] = EnemyTemp;
            EnemySlotLeft--;
        }

        bool allSlotsFilled = true;
        for (int i = 0; i < EnemySlot.Length; i++)
        {
            if (EnemySlot[i] == null)
            {
                allSlotsFilled = false;
                break;
            }
        }

        if(allSlotsFilled || EnemySlotLeft < 1){AnimState = 0;}
        else{AnimState = 1;}

        if(CheckCurrentEnemy < EnemySlot.Length-1){CheckCurrentEnemy++;}
        else{CheckCurrentEnemy = 0;}

        //Timer for update
        if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 3f);}
    }

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

        if(AnimState == 1){
            Vector3 directionToPlayer = transform.position - localPlayer.GetPosition();
            localPlayer.SetVelocity(-directionToPlayer.normalized * 25);
        }
    }
}
