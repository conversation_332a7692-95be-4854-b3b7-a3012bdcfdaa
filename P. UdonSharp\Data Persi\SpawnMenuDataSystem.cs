﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpawnMenuDataSystem : UdonSharpBehaviour
{
    [Header("Item Management")]
    public GameObject[] Items;        // Items to activate when player has the data
    public GameObject[] ItemLockers;  // Lockers to activate when player doesn't have the data

    [Header("PlayerData Keys")]
    public string[] PlayerDataKeys;   // Array of PlayerData string keys

    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(LoadSaveFile), 3f);
    }

    public void LoadSaveFile()
    {
        if (localPlayer == null) return;

        // Ensure arrays are the same length
        int maxLength = Mathf.Max(Items.Length, ItemLockers.Length, PlayerDataKeys.Length);

        for (int i = 0; i < maxLength; i++)
        {
            if (i < PlayerDataKeys.Length && !string.IsNullOrEmpty(PlayerDataKeys[i]))
            {
                string playerDataValue = PlayerData.GetString(localPlayer, PlayerDataKeys[i]);
                bool hasItem = !string.IsNullOrEmpty(playerDataValue);

                if (i < Items.Length && Items[i] != null){Items[i].SetActive(hasItem);}

                if (i < ItemLockers.Length && ItemLockers[i] != null){ItemLockers[i].SetActive(!hasItem);}
            }
            else
            {
                if (i < Items.Length && Items[i] != null){Items[i].SetActive(false);}

                if (i < ItemLockers.Length && ItemLockers[i] != null){ItemLockers[i].SetActive(true);}
            }
        }
    }

    public void LastSaveFile() //Update SaveFile
    {
        if (localPlayer == null) return;

        for (int i = 0; i < PlayerDataKeys.Length; i++)
        {
            if (!string.IsNullOrEmpty(PlayerDataKeys[i]))
            {
                string playerDataValue = PlayerData.GetString(localPlayer, PlayerDataKeys[i]);
                bool hasItem = !string.IsNullOrEmpty(playerDataValue);

                if (i < Items.Length && Items[i] != null){Items[i].SetActive(hasItem);}

                if (i < ItemLockers.Length && ItemLockers[i] != null){ItemLockers[i].SetActive(!hasItem);}
            }
        }
    }

    public void UnlockItem(int itemIndex) //Unlock Item
    {
        if (localPlayer == null || itemIndex < 0 || itemIndex >= PlayerDataKeys.Length) return;

        if (!string.IsNullOrEmpty(PlayerDataKeys[itemIndex]))
        {
            PlayerData.SetString(PlayerDataKeys[itemIndex], "1");

            if (itemIndex < Items.Length && Items[itemIndex] != null){Items[itemIndex].SetActive(true);}

            if (itemIndex < ItemLockers.Length && ItemLockers[itemIndex] != null){ItemLockers[itemIndex].SetActive(false);}
        }
    }

    public void LockItem(int itemIndex) //Lock Item
    {
        if (localPlayer == null || itemIndex < 0 || itemIndex >= PlayerDataKeys.Length) return;

        if (!string.IsNullOrEmpty(PlayerDataKeys[itemIndex]))
        {
            PlayerData.SetString(PlayerDataKeys[itemIndex], "");

            if (itemIndex < Items.Length && Items[itemIndex] != null){Items[itemIndex].SetActive(false);}

            if (itemIndex < ItemLockers.Length && ItemLockers[itemIndex] != null){ItemLockers[itemIndex].SetActive(true);}
        }
    }

    // Method to check if a specific item is unlocked
    public bool IsItemUnlocked(int itemIndex)
    {
        if (localPlayer == null || itemIndex < 0 || itemIndex >= PlayerDataKeys.Length) return false;

        if (!string.IsNullOrEmpty(PlayerDataKeys[itemIndex]))
        {
            string playerDataValue = PlayerData.GetString(localPlayer, PlayerDataKeys[itemIndex]);
            return !string.IsNullOrEmpty(playerDataValue);
        }

        return false;
    }

    // Method to refresh all item states (useful for external calls)
    public void RefreshAllItems(){LoadSaveFile();}
}
