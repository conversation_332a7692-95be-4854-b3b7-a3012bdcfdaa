﻿using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GamblingSystem : UdonSharpBehaviour
{
    //Variables
    public int RollPrice;
    public int ChosenItem;
    public int[] ItemRotation;
    public string[] ItemName;
    public Image ItemImage;
    public Sprite[] ItemSprite;
    public Color[] OptionalImageColor;
    public bool[] IsCoins;

    //Required
    public GameObject SpawnMenuSystemObject, PlayerSystemObject;
    public GameObject Menu, RollButton, RollResult, CannotAffordObject;
    public bool PlayerInProximity;
    private VRCPlayerApi localPlayer;
    public LayerMask whatIsPlayer;
    public TextMeshPro RollPriceText, RollResultText;
    public AudioSource PurchaseSource;
    public AudioClip RollSoundClip, RollNewItemSoundClip;

    void Start()
    {
        SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
        localPlayer = Networking.LocalPlayer;

        RollPriceText.text = RollPrice.ToString();

        bool playerInSightRange = Physics.CheckSphere(transform.position, 15f, whatIsPlayer);

        if(playerInSightRange){Menu.SetActive(true);}
        else{Menu.SetActive(false);}   

        RollResult.SetActive(false);
        RollButton.SetActive(true);
    }

    public void RollFunction()
    {
        int PlayerMoney = PlayerData.GetInt(localPlayer, "EmperCoins");

        if(PlayerMoney > RollPrice){
            int ChosenItem = Random.Range(0, ItemRotation.Length);

            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            MainPlayerCurrencySystem mainPlayerCurrencySystem = PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>();
            
            ItemImage.sprite = ItemSprite[ChosenItem];
            ItemImage.color = OptionalImageColor[ChosenItem];

            if(!IsCoins[ChosenItem]){

                if(spawnMenuDataSystem.IsItemUnlocked(ItemRotation[ChosenItem]) == false){
                    RollResultText.text = "You Got: " + ItemName[ChosenItem] + "!" + " (NEW!)";
                    PurchaseSource.PlayOneShot(RollNewItemSoundClip);
                }
                else{
                    RollResultText.text = "You Got: " + ItemName[ChosenItem] + "!";
                    PurchaseSource.PlayOneShot(RollSoundClip);
                }

                if(spawnMenuDataSystem != null && spawnMenuDataSystem.IsItemUnlocked(ItemRotation[ChosenItem]) == false){spawnMenuDataSystem.UnlockItem(ItemRotation[ChosenItem]);}
                if(mainPlayerCurrencySystem != null){mainPlayerCurrencySystem.RemovePoints(RollPrice);}
            }
            else{
                if(mainPlayerCurrencySystem != null){
                    mainPlayerCurrencySystem.RemovePoints(RollPrice); 
                    mainPlayerCurrencySystem.AddPoints(ItemRotation[ChosenItem]);
                }
                RollResultText.text = "You Got: " + ItemRotation[ChosenItem] + " EmperCoins!";
                PurchaseSource.PlayOneShot(RollSoundClip);
            }

            RollResult.SetActive(true);
            RollButton.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(ResetRollResult), 3f);
        }
        else{
            CannotAffordObject.SetActive(true);
            SendCustomEventDelayedSeconds(nameof(CloseCannotAfford), 3f);
        }
    }
    public void ResetRollResult()
    {
        RollResult.SetActive(false);
        RollButton.SetActive(true);
    }
    public void CloseCannotAfford()
    {
        CannotAffordObject.SetActive(false);
    }

    #region CollisionFunctions
    //Collision Related
    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(true);
        PlayerInProximity = true;
    }
    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(false);
        PlayerInProximity = false;
    }
    #endregion CollisionFunctions
}
