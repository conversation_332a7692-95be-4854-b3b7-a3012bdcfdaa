﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CollectibleSystem : UdonSharpBehaviour
{
    public int CollectibleType; //0 is Collectible, 1 is Coin

    //required
    public GameObject MainObject, DecorationObject;
    public string PlayerDataKey;
    public bool CollectibleOwned;

    //collectible related
    public GameObject SpawnMenuSystemObject;
    public GameObject[] EntityObjects;
    public int ArraySelector;

    //coin related
    public GameObject CoinTextObject;
    public TextMeshPro CoinText;
    public int MoneyDropAmount = 0;
    public bool CanBeCollectedMultipleTimes;

    //Effects
    public ParticleSystem CollectibleEffect;
    public AudioSource CollectibleAudio;
    public AudioClip CollectibleAudioClip;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        if(CollectibleType == 0){
            SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");

            ArraySelector = int.Parse(PlayerDataKey.Substring(4)) - 1;

            for (int i = 0; i < EntityObjects.Length; i++)
            {
                if (i == ArraySelector && EntityObjects[i] != null){EntityObjects[i].SetActive(true);}
                else if(EntityObjects[i] != null){EntityObjects[i].SetActive(false);}
            }
        }

        if(CanBeCollectedMultipleTimes == false){
            MainObject.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(RefreshCollectible), 5f);
        }
    }

    void OnEnable(){
        if(CanBeCollectedMultipleTimes == true)
        {
            MainObject.SetActive(true);
            DecorationObject.SetActive(true);
            CollectibleOwned = false;
        }
    }

    public void RefreshCollectible(){
        if (localPlayer == null) return;

        string playerDataValue = PlayerData.GetString(localPlayer, PlayerDataKey);
        CollectibleOwned = !string.IsNullOrEmpty(playerDataValue);

        if (CollectibleOwned){MainObject.SetActive(false);}
        else{MainObject.SetActive(true);}
    }

    public void CloseMainObject(){MainObject.SetActive(false); if(CoinTextObject != null){CoinTextObject.SetActive(false);}}

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player.isLocal)
        {
            if(CollectibleType == 0){
                if (!CollectibleOwned)
                {
                    CollectibleOwned = true;
                    DecorationObject.SetActive(false);
                    CollectibleEffect.Play();
                    CollectibleAudio.PlayOneShot(CollectibleAudioClip);

                    if(SpawnMenuSystemObject != null){
                        SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
                        if(spawnMenuDataSystem != null){spawnMenuDataSystem.UnlockItem(ArraySelector);}
                    }

                    SendCustomEventDelayedSeconds(nameof(CloseMainObject), 1f);
                }
            }
            if(CollectibleType == 1){
                if (!CollectibleOwned)
                {
                    CollectibleOwned = true;
                    DecorationObject.SetActive(false);
                    CollectibleEffect.Play();
                    CollectibleAudio.PlayOneShot(CollectibleAudioClip);

                    if(MoneyDropAmount != 0){
                        GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
                        PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(MoneyDropAmount);
                    }

                    if(CanBeCollectedMultipleTimes == false){PlayerData.SetString(PlayerDataKey, "1");}

                    CoinTextObject.SetActive(true);
                    CoinText.text = MoneyDropAmount + "!";

                    SendCustomEventDelayedSeconds(nameof(CloseMainObject), 1f);
                }
            }
        }
    }
}
