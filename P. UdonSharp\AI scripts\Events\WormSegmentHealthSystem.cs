using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class WormSegmentHealthSystem : UdonSharpBehaviour
{
    public UniversalEnemySystem UniversalEnemySystem;
    public LayerMask damageLayer;

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        UniversalEnemySystem.OnTriggerEnter(collision);
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        UniversalEnemySystem.OnTriggerStay(collision);
    }
}
