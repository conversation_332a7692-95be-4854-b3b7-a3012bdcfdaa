﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BotPhysicsEnemyAI : UdonSharpBehaviour
{
    public GameObject Body;
    public Rigidbody rb;
    public Rigidbody[] SecondaryRbs;
    public AudioSource GlobalAudioSource;

    public BotPhysicsHealthSystem HealthSystem;

    public int Attacks;

    public float Speed = 250f;
    public float Multiplier = 1f;
    public float SightRange = 50f;
    public bool IsRunning;
    public Vector3 SpawnedPoint;

    public VRCPlayerApi localPlayer;

    public GameObject TeleportIndicator;
    public ParticleSystem TeleportIndicatorEffect;
    public AudioClip TeleportIndicatorSound,SuccessfulTeleportSound;

    void Start(){
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        TeleportIndicator.SetActive(false);
        TeleportIndicator.transform.position = transform.position;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
    }

    public void EnemyAISystem()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
            if(HealthSystem != null){HealthSystem.CanDie = 1;}
            if(IsRunning){Attacks = 2;}
            else{Attacks = Random.Range(1, 3);}

            if(Attacks == 1)
            {
                IsRunning = true;
                SendCustomEventDelayedSeconds(nameof(AttackOne), 1f/Multiplier);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(5f/Multiplier, 10f/Multiplier));
            }
            else if(Attacks == 2)
            {
                IsRunning = false;
                SendCustomEventDelayedSeconds(nameof(AttackTwo), 0.1f/Multiplier);
            }
        }
        else{
            if(HealthSystem != null){HealthSystem.CanDie = 0;}
            if(IsRunning){Attacks = 2;}
            else{Attacks = Random.Range(1, 3);}

            if(Attacks == 1)
            {
                IsRunning = true;
                SendCustomEventDelayedSeconds(nameof(IdleOne), 1f/Multiplier);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(5f/Multiplier, 10f/Multiplier));
            }
            else if(Attacks == 2)
            {
                IsRunning = false;
                SendCustomEventDelayedSeconds(nameof(IdleTwo), 0.1f/Multiplier);
            }
        }
    }

    public void AttackOne()
    {
        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        rb.AddForce((Body.transform.forward)*Speed*Multiplier);
        if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(AttackOne), 0.05f);}
    }

    public void AttackTwo()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-5f/Multiplier,5f/Multiplier),1f,Random.Range(-5f/Multiplier,5f/Multiplier));
        rb.velocity = Vector3.zero;
        if(SecondaryRbs != null && SecondaryRbs.Length > 0){
            for (int i = 0; i < SecondaryRbs.Length; i++){
                if(SecondaryRbs[i] != null){
                    SecondaryRbs[i].velocity = Vector3.zero;
                }
            }
        }
    
        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(AttackTwo2), 2f/Multiplier);
    }

    public void AttackTwo2()
    {
        Body.transform.position = TeleportIndicator.transform.position;
        rb.velocity = Vector3.zero;
        if(SecondaryRbs != null && SecondaryRbs.Length > 0){
            for (int i = 0; i < SecondaryRbs.Length; i++){
                if(SecondaryRbs[i] != null){
                    SecondaryRbs[i].velocity = Vector3.zero;
                }
            }
        }
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f/Multiplier);
    }


    //Idle Attacks
    public void IdleOne()
    {
        // Get player position
        Vector3 playerPos = SpawnedPoint + new Vector3(Random.Range(-5f/Multiplier,5f/Multiplier),1f,Random.Range(-5f/Multiplier,5f/Multiplier));
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        rb.AddForce((Body.transform.forward)*Speed*Multiplier);
        if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(IdleOne), 0.5f/Multiplier);}
    }

    public void IdleTwo()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.position = SpawnedPoint + new Vector3(Random.Range(-5f/Multiplier,5f/Multiplier),1f,Random.Range(-5f/Multiplier,5f/Multiplier));
        rb.isKinematic = true;
        if(SecondaryRbs != null && SecondaryRbs.Length > 0){
            for (int i = 0; i < SecondaryRbs.Length; i++){
                if(SecondaryRbs[i] != null){
                    SecondaryRbs[i].isKinematic = true;
                }
            }
        }

        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(IdleTwo2), 2f/Multiplier);
    }

    public void IdleTwo2()
    {
        Body.transform.position = TeleportIndicator.transform.position;
        rb.isKinematic = false;
        if(SecondaryRbs != null && SecondaryRbs.Length > 0){
            for (int i = 0; i < SecondaryRbs.Length; i++){
                if(SecondaryRbs[i] != null){
                    SecondaryRbs[i].isKinematic = false;
                }
            }
        }
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f/Multiplier);
    }
}
