﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BluemonRangedSystem : UdonSharpBehaviour
{
    public int RangedSystemType = 0; //0 is normal, 1 is no parenting and transform/rotationchange
    public GameObject[] Objects;
    public Transform[] ObjectTranform;
    public int HasLaunched;
    public float SightRange1a;
    public float SightRange1b;
    public float SightRange2a;
    public float SightRange2b;
    public float SightRange3a;
    public float SightRange3b;
    public float SightRange4a;
    public float SightRange4b;

    private VRCPlayerApi localPlayer;

    void Start(){
        HasLaunched = 0;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.1f);
    }

    public void UpdateTimer()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange1a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange1b){
            if(HasLaunched == 0){
                for(int i = 0; i < Objects.Length; i++){
                    if(Objects[i] != null){
                        if(RangedSystemType == 0){
                            Objects[i].transform.position = ObjectTranform[i].position;
                            Objects[i].transform.rotation = ObjectTranform[i].rotation;
                            Objects[i].transform.parent = null;
                        }
                        Objects[i].SetActive(true);

                        Rigidbody rb = Objects[i].GetComponent<Rigidbody>();
                        if(rb != null){rb.velocity = Vector3.zero;}
                    }
                }
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange2a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange2b){
            if(HasLaunched == 0){
                for(int i = 0; i < Objects.Length; i++){
                    if(Objects[i] != null){
                        if(RangedSystemType == 0){
                            Objects[i].transform.position = ObjectTranform[i].position;
                            Objects[i].transform.rotation = ObjectTranform[i].rotation;
                            Objects[i].transform.parent = null;
                        }
                        Objects[i].SetActive(true);

                        Rigidbody rb = Objects[i].GetComponent<Rigidbody>();
                        if(rb != null){rb.velocity = Vector3.zero;}
                    }
                }
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange3a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange3b){
            if(HasLaunched == 0){
                for(int i = 0; i < Objects.Length; i++){
                    if(Objects[i] != null){
                        if(RangedSystemType == 0){
                            Objects[i].transform.position = ObjectTranform[i].position;
                            Objects[i].transform.rotation = ObjectTranform[i].rotation;
                            Objects[i].transform.parent = null;
                        }
                        Objects[i].SetActive(true);

                        Rigidbody rb = Objects[i].GetComponent<Rigidbody>();
                        if(rb != null){rb.velocity = Vector3.zero;}
                    }
                }
                HasLaunched = 1;
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange4a && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange4b){
            if(HasLaunched == 0){
                for(int i = 0; i < Objects.Length; i++){
                    if(Objects[i] != null){
                        if(RangedSystemType == 0){
                            Objects[i].transform.position = ObjectTranform[i].position;
                            Objects[i].transform.rotation = ObjectTranform[i].rotation;
                            Objects[i].transform.parent = null;
                        }
                        Objects[i].SetActive(true);

                        Rigidbody rb = Objects[i].GetComponent<Rigidbody>();
                        if(rb != null){rb.velocity = Vector3.zero;}
                    }
                }
                HasLaunched = 1;
            }
        }
        else{
            if(HasLaunched == 1){
                for(int i = 0; i < Objects.Length; i++){
                    if(Objects[i] != null){
                        if(RangedSystemType == 0){
                            Objects[i].transform.parent = gameObject.transform;
                            Objects[i].transform.position = ObjectTranform[i].position;
                            Objects[i].transform.rotation = ObjectTranform[i].rotation;
                        }
                        Objects[i].SetActive(false);
                    }
                }
                HasLaunched = 0;
            }
        }
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.1f);
    }

    public void OnDestroy(){
        for(int i = 0; i < Objects.Length; i++){
            if(Objects[i] != null){Destroy(Objects[i]);}
        }
    }
}
