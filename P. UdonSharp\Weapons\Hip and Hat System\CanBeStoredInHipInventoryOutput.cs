using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CanBeStoredInHipInventoryOutput : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public LayerMask LayerRequired;

    void Start(){localPlayer = Networking.LocalPlayer;}

    public override void PostLateUpdate(){gameObject.transform.rotation = localPlayer.GetRotation();}

    public void OnTriggerEnter(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        CanBeStoredInHipInventoryInput CanbeStoredInHipInventoryInput = collision.gameObject.GetComponent<CanBeStoredInHipInventoryInput>();
        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanbeStoredInHipInventoryInput != null && CanbeStoredInHipInventoryInput.isActive == true)
        {
            if(DamageInput != null){
                DamageInput.DamageCache = DamageInput.Damage;
                DamageInput.Damage = DamageInput.Damage/3;
            }
            collision.gameObject.transform.parent = transform;
            Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
            Collider[] colliders = collision.gameObject.GetComponents<Collider>();
            Collider collider = colliders[0];
            collider.isTrigger = true;
            //rb.isKinematic = true;
        }
    }
    public void OnTriggerExit(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        CanBeStoredInHipInventoryInput CanbeStoredInHipInventoryInput = collision.gameObject.GetComponent<CanBeStoredInHipInventoryInput>();
        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanbeStoredInHipInventoryInput != null && CanbeStoredInHipInventoryInput.isActive == true)
        {
            if(DamageInput != null){
                DamageInput.Damage = DamageInput.DamageCache;
            }
            collision.gameObject.transform.parent = null;
            Rigidbody rb = collision.gameObject.GetComponent<Rigidbody>();
            Collider[] colliders = collision.gameObject.GetComponents<Collider>();
            Collider collider = colliders[0];
            collider.isTrigger = false;
            //rb.isKinematic = false;
        }
    }
}
