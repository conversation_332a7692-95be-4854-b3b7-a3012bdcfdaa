﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class BasicPlaneSystem : UdonSharpBehaviour
{
    public GameObject PlaneObject;
    public Rigidbody rb;
    public Transform chairposition;
    private VRCPlayerApi localPlayer;
    public bool LocalPlayerInSeat;
    public float Strength;
    public bool EngineStarted;
    public bool IsSyncable = false;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
    }

    public void UpdateCustom()
    {
        if (localPlayer != null){
            bool PlayerInDistance = Vector3.Distance(localPlayer.GetPosition(), chairposition.position) < 2f;
            if (PlayerInDistance){
                LocalPlayerInSeat = true; 
                if(IsSyncable){
                    if(!Networking.IsOwner(PlaneObject)){Networking.SetOwner(Networking.LocalPlayer, PlaneObject);}
                }
            }
            else{LocalPlayerInSeat = false;}

            if(!localPlayer.IsUserInVR() && LocalPlayerInSeat){
                if (Input.GetKey(KeyCode.UpArrow)){rb.AddTorque(-transform.right * Strength*10);}
                if (Input.GetKey(KeyCode.DownArrow)){rb.AddTorque(transform.right * Strength*10);}
                if (Input.GetKey(KeyCode.LeftArrow)){rb.AddTorque(transform.forward * Strength*2);}
                if (Input.GetKey(KeyCode.RightArrow)){rb.AddTorque(-transform.forward * Strength*2);}
            }

            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);
        }
    }

    public override void InputLookHorizontal(float value, UdonInputEventArgs args)
    {
        if(localPlayer.IsUserInVR()){
            if (LocalPlayerInSeat){rb.AddTorque(-transform.forward * value * Strength*2);}
        }
    }

    public override void InputLookVertical(float value, UdonInputEventArgs args)
    {
        if(localPlayer.IsUserInVR()){
            if (LocalPlayerInSeat){rb.AddTorque(-transform.right * value * Strength*10);}
        }
    }

    public override void InputJump(bool value, UdonInputEventArgs args)
    {
        if (value && LocalPlayerInSeat){EngineStarted = !EngineStarted; ForwardEngine();}
    }

    public void ForwardEngine()
    {
        if (EngineStarted){rb.AddForce(transform.forward * Strength*2); SendCustomEventDelayedSeconds(nameof(ForwardEngine), 0.05f);}
    }
}
