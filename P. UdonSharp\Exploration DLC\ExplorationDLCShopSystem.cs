﻿using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class ExplorationDLCShopSystem : UdonSharpBehaviour
{
    public GameObject SpawnMenuSystemObject, PlayerSystemObject;
    public GameObject PurchaseButton, OwnedButton, PurchaseButton2, OwnedButton2, PurchaseButton3, OwnedButton3;
    public GameObject CannotAfford1, CannotAfford2, CannotAfford3;

    //Items, Costs and Images
    public int Item1, Item2, Item3, ItemPrice1, ItemPrice2, ItemPrice3;
    public Image Item1Image, Item2Image, Item3Image;
    public TextMeshPro Item1PriceText, Item2PriceText, Item3PriceText;
    public Sprite[] ItemsSprite;
    public string[] ItemName;

    public int PlayerMoney;

    private VRCPlayerApi localPlayer;
    public AudioSource PurchaseSource;
    public AudioClip PurchaseClip;

    private void Start()
    {
        SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
        localPlayer = Networking.LocalPlayer;

        Item1Image.sprite = ItemsSprite[0];
        Item2Image.sprite = ItemsSprite[1];
        Item3Image.sprite = ItemsSprite[2];

        Item1PriceText.text = ItemName[0] + " for " + ItemPrice1.ToString();
        Item2PriceText.text = ItemName[1] + " for " + ItemPrice2.ToString();
        Item3PriceText.text = ItemName[2] + " for " + ItemPrice3.ToString();

        SendCustomEventDelayedSeconds(nameof(RefreshShop), 0.1f);
    }

    public void RefreshShop(){
        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){
                if(spawnMenuDataSystem.IsItemUnlocked(Item1)){
                    PurchaseButton.SetActive(false);
                    OwnedButton.SetActive(true);
                }
                else{
                    PurchaseButton.SetActive(true);
                    OwnedButton.SetActive(false);
                }
                if(spawnMenuDataSystem.IsItemUnlocked(Item2)){
                    PurchaseButton2.SetActive(false);
                    OwnedButton2.SetActive(true);
                }
                else{
                    PurchaseButton2.SetActive(true);
                    OwnedButton2.SetActive(false);
                }
                if(spawnMenuDataSystem.IsItemUnlocked(Item3)){
                    PurchaseButton3.SetActive(false);
                    OwnedButton3.SetActive(true);
                }
                else{
                    PurchaseButton3.SetActive(true);
                    OwnedButton3.SetActive(false);
                }
            }
        }
    }

    public void PurchaseItem1(){
        PlayerMoney = PlayerData.GetInt(localPlayer, "SpaceCoins");


        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            SpaceCoinsCurrencySystem SpaceCoinsCurrencySystem = PlayerSystemObject.GetComponent<SpaceCoinsCurrencySystem>();
            if(spawnMenuDataSystem != null && SpaceCoinsCurrencySystem != null && PlayerMoney >= ItemPrice1 && spawnMenuDataSystem.IsItemUnlocked(Item1) == false){
                spawnMenuDataSystem.UnlockItem(Item1);
                SpaceCoinsCurrencySystem.RemovePoints(ItemPrice1);
                PurchaseButton.SetActive(false);
                OwnedButton.SetActive(true);
                PurchaseSource.PlayOneShot(PurchaseClip);
            }
            else{
                if(spawnMenuDataSystem.IsItemUnlocked(Item1) == true){return;}
                CannotAfford1.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseCannotAfford1), 1f);
            }
        }
    }
    public void PurchaseItem2(){
        PlayerMoney = PlayerData.GetInt(localPlayer, "SpaceCoins");


        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            SpaceCoinsCurrencySystem SpaceCoinsCurrencySystem = PlayerSystemObject.GetComponent<SpaceCoinsCurrencySystem>();
            if(spawnMenuDataSystem != null && SpaceCoinsCurrencySystem != null && PlayerMoney >= ItemPrice2 && spawnMenuDataSystem.IsItemUnlocked(Item2) == false){
                spawnMenuDataSystem.UnlockItem(Item2);
                SpaceCoinsCurrencySystem.RemovePoints(ItemPrice2);
                PurchaseButton2.SetActive(false);
                OwnedButton2.SetActive(true);
                PurchaseSource.PlayOneShot(PurchaseClip);
            }
            else{
                if(spawnMenuDataSystem.IsItemUnlocked(Item2) == true){return;}
                CannotAfford2.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseCannotAfford2), 1f);
            }
        }
    }
    public void PurchaseItem3(){
        PlayerMoney = PlayerData.GetInt(localPlayer, "SpaceCoins");


        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            SpaceCoinsCurrencySystem SpaceCoinsCurrencySystem = PlayerSystemObject.GetComponent<SpaceCoinsCurrencySystem>();
            if(spawnMenuDataSystem != null && SpaceCoinsCurrencySystem != null && PlayerMoney >= ItemPrice3 && spawnMenuDataSystem.IsItemUnlocked(Item3) == false){
                spawnMenuDataSystem.UnlockItem(Item3);
                SpaceCoinsCurrencySystem.RemovePoints(ItemPrice3);
                PurchaseButton3.SetActive(false);
                OwnedButton3.SetActive(true);
                PurchaseSource.PlayOneShot(PurchaseClip);
            }
            else{
                if(spawnMenuDataSystem.IsItemUnlocked(Item3) == true){return;}
                CannotAfford3.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseCannotAfford3), 1f);
            }
        }
    }
    public void CloseCannotAfford1(){CannotAfford1.SetActive(false);}
    public void CloseCannotAfford2(){CannotAfford2.SetActive(false);}
    public void CloseCannotAfford3(){CannotAfford3.SetActive(false);}
}
