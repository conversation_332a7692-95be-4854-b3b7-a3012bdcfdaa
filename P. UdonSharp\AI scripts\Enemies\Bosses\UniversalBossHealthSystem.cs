﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalBossHealthSystem : UdonSharpBehaviour
{
    public int Health,HealthMax = 100;
    public bool IsDead;

    //Effects
    public ParticleSystem Particle2;
    public AudioSource AudioSource;
    public LayerMask damageLayer;

    public GameObject HealthBar, ProtectionBar;
    public TextMeshPro DamageText;
    public Slider HealthSlider;
    public bool HasInterstellarArmor;
    public bool CanBeHit,CanBeKilled,ImmunetoType2Damage;
    public float ImmunityFramesDoneDelay = 0.1f;

    public int Damage;

    public void Start()
    {
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.maxValue = HealthMax;
        HealthSlider.value = Health;
        CanBeHit = true;
    }

    public void ImmunityFramesDone(){CanBeHit = true;}

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && CanBeKilled && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && !IsDead)
        {
            if(!HasInterstellarArmor){
                Damage = DamageInput.Damage;
            }
            else if(HasInterstellarArmor){
                if(!DamageInput.CanPenetrateIntestellarArmor)
                {
                    Damage = DamageInput.Damage/3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
                else
                {
                    Damage = DamageInput.Damage*3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
            }
            Health -= Damage;
            if(Health <= 0){Health = 0;}
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(CanBeHit && CanBeKilled && DamageInput == null && !IsDead)
        {
            int Damage = 1;
            Health -= Damage;
            if(Health <= 0){Health = 0;}
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;
    
        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && CanBeKilled && !ImmunetoType2Damage && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !IsDead)
        {
            if(!HasInterstellarArmor){
                Damage = DamageInput.Damage;
            }
            else if(HasInterstellarArmor){
                if(!DamageInput.CanPenetrateIntestellarArmor)
                {
                    Damage = DamageInput.Damage/3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
                else
                {
                    Damage = DamageInput.Damage*3;
                    Damage += Random.Range(DamageInput.Damage/5, DamageInput.Damage/2);
                }
            }
            Health -= Damage;
            if(Health <= 0){Health = 0;}
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;
    
            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
    
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(CanBeHit && CanBeKilled && ImmunetoType2Damage && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !IsDead)
        {
            if(!HasInterstellarArmor){
                Damage = DamageInput.Damage/3;
            }
            else if(HasInterstellarArmor){
                if(!DamageInput.CanPenetrateIntestellarArmor)
                {
                    Damage = DamageInput.Damage/9;
                    Damage += Random.Range(DamageInput.Damage/15, DamageInput.Damage/6);
                }
                else
                {
                    Damage = DamageInput.Damage;
                    Damage += Random.Range(DamageInput.Damage/15, DamageInput.Damage/6);
                }
            }
            Health -= Damage;
            if(Health <= 0){Health = 0;}
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;
    
            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
    
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}
