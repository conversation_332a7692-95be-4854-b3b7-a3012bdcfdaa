﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GenericPvpIndicator : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;

    public int DamageType = 1; // 1 = OnEnter, 2 = OnStay
    private int ImmunityFrames = 1;

    // OptionalHurtParticleForGlobal Weapons
    public ParticleSystem OptionalHurtParticle;
    public AudioSource OptionalHurtAudio;
    public AudioClip OptionalHurtAudioClip;

    void Start(){localPlayer = Networking.LocalPlayer; ImmunityFrames = 1;}

    public void UpdateFrames(){ImmunityFrames = 1;}

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer && DamageType == 1 && ImmunityFrames == 1)
        {
            OptionalHurtParticle.Play();
            OptionalHurtAudio.PlayOneShot(OptionalHurtAudioClip);
            ImmunityFrames = 0;
            SendCustomEventDelayedSeconds(nameof(UpdateFrames), 0.1f);
        }
    }
    public override void OnPlayerTriggerStay(VRCPlayerApi player)
    {
        if (player != localPlayer && DamageType == 2 && ImmunityFrames == 1)
        {
            OptionalHurtParticle.Play();
            OptionalHurtAudio.PlayOneShot(OptionalHurtAudioClip);
            ImmunityFrames = 0;
            SendCustomEventDelayedSeconds(nameof(UpdateFrames), 0.1f);
        }
    }
}
