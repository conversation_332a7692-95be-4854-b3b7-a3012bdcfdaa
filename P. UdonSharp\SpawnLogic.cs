﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using System;
using VRC.SDK3.Components;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpawnLogic : UdonSharpBehaviour
{
    public Transform teleportDestination,tutorialDestination;
    public GameObject Room,TutorialRoom,VideoPlayer, Hud;
    public GameObject[] SpawnMenu;
    public SpawnMenuSystem SpawnMenuSystem;
    public VRCPlayerApi localPlayer;
    public int CanClose = 0;
    public int HasSpawned = 0;

    public float PlayerHeight = 1.45f;

    public AudioSource audioSource;
    public AudioClip buzzsound;

    public GameObject PlayButton,LockedButton;

    public TextMeshProUGUI TipText;
    public string[] TipStrings;

    public void Start()
    {
        localPlayer = Networking.LocalPlayer;


        //randomized tips
        int randomIndex = UnityEngine.Random.Range(0, TipStrings.Length);
        TipText.text = "Quote of the Day! \n" + TipStrings[randomIndex];

        if(Room != null){Room.SetActive(true);}
        VideoPlayer.SetActive(false);
        Hud.SetActive(false);
        localPlayer.TeleportTo(teleportDestination.position, teleportDestination.rotation);
        SendCustomEventDelayedSeconds(nameof(GetridofSpawnMenu), 1f);
        SendCustomEventDelayedSeconds(nameof(CheckTutorialFinished), 5f);
    }

    public void CheckTutorialFinished(){
        //string playerDataValue = PlayerData.GetString(localPlayer, "TutorialFinished");

        //if(playerDataValue == "1"){
        //    PlayButton.SetActive(true);
        //    LockedButton.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(SpawnPlayer), 120f);
        //}
        //else{
        //    PlayButton.SetActive(false);
        //    LockedButton.SetActive(true);
        //    SendCustomEventDelayedSeconds(nameof(SpawnTutorial), 120f);
        //}

        //SendCustomEventDelayedSeconds(nameof(SpawnPlayer), 120f);
    }

    public void SpawnPlayer()
    {
        if(CanClose < 2 && HasSpawned == 0){
            HasSpawned = 1;
            localPlayer.TeleportTo(new Vector3(0,0,0), new Quaternion(0,0,0,0)); 
            Room.SetActive(false); 
            VideoPlayer.SetActive(true);
            localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
        }
    }
    public void SpawnTutorial()
    {
        if(HasSpawned == 0){
            HasSpawned = 1;
            localPlayer.TeleportTo(tutorialDestination.position, tutorialDestination.rotation);
            Room.SetActive(false); 
            TutorialRoom.SetActive(true);
            localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
        }
    }

    public void GetridofSpawnMenu(){for(int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(false);}}

    public void Spawned(){
        HasSpawned = 1;
        for(int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}
        Hud.SetActive(true);
        SpawnMenuSystem.CloseMenu();
        SpawnMenuSystem.MinimizedMode = true;
        SpawnMenuSystem.MenuContainer.SetActive(false);
        SpawnMenuSystem.spawnpoint.gameObject.SetActive(false);
        SpawnMenuSystem.MinimizedMenu.SetActive(true);
        SpawnMenuSystem.PickupCollider.enabled = false;

        //Set Height to 1.45f
        localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
    }

    public void BuzzSound(){
        audioSource.PlayOneShot(buzzsound);
    }
    
    public override void OnPlayerTriggerEnter(VRCPlayerApi player){
        localPlayer = Networking.LocalPlayer;
        if (player != localPlayer) return;

        CanClose++;
        if(CanClose >= 2){
            Room.SetActive(false);
            VideoPlayer.SetActive(true);
            HasSpawned = 1;
            localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
        }
    }
}
