﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class ItemUnlockSystem : UdonSharpBehaviour
{
    public GameObject SpawnMenuSystemObject;
    public string ItemName; //Optional Name of the item
    public int ItemRewardID; //ID of the item at the spawnmenu

    public void OnEnable()
    {
        if(SpawnMenuSystemObject == null){SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");}

        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){spawnMenuDataSystem.UnlockItem(ItemRewardID);}
        }
    }
}
