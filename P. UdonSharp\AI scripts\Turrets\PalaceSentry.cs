﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PalaceSentry : UdonSharpBehaviour
{
    public float SightRange = 10f;
    public LayerMask WhatIsEnemy;
    public LayerMask WhatIsPlayer;
    public GameObject Head;
    public GameObject closestEnemy;
    public bool IsEnemyTurret,PlayerInRange;
    //Bullet
    public float bulletSpeed = 10;
    public GameObject bullet,bulletPrefab;
    public Transform bulletSpawnPoint;
    public float BulletDelay = 0.2f;

    private VRCPlayerApi localPlayer;  

    public bool IsActive;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        IsActive = true;
    
        //Timer for update
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    public void OnEnable(){
        IsActive = true;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }
    public void OnDisable(){
        IsActive = false;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    public void UpdateCustom()
    {
        if(!IsEnemyTurret){
        //Find enemieswithinrange
        Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, SightRange, WhatIsEnemy);
        float closestDistance = Mathf.Infinity;

        foreach (Collider enemy in enemiesInRange)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestEnemy = enemy.gameObject;
            }
        }

        //Timer Stuff
        if (enemiesInRange.Length == 0)
        {
            closestEnemy = null;
            if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);}
            return;
        }

        if (closestEnemy != null)
        {
            TargetAndFire();
            if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), BulletDelay);}
        }

        }
        else{
            PlayerInRange = Physics.CheckSphere(transform.position, SightRange, WhatIsPlayer);
            
            if(PlayerInRange){
                TargetAndFire();
                if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), BulletDelay);}
            }
            else{
                if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);}
            }
        }


    }
    void TargetAndFire()
    {
        if(gameObject.activeInHierarchy){
            if(!IsEnemyTurret){
                //Aimtotarget
                Vector3 HeadDirection = closestEnemy.transform.position - Head.transform.position;
                Head.transform.rotation = Quaternion.LookRotation(HeadDirection);

                //Bullet Shoot
                var bullet = VRCInstantiate(bulletPrefab);
                bullet.transform.position = bulletSpawnPoint.position;
                bullet.transform.rotation = bulletSpawnPoint.rotation;
                bullet.GetComponent<Rigidbody>().velocity = bulletSpawnPoint.forward * bulletSpeed;
            }
            else{
                //Aimtotarget
                Vector3 HeadDirection = (localPlayer.GetPosition() + new Vector3(0,1,0)) - Head.transform.position;
                Head.transform.rotation = Quaternion.LookRotation(HeadDirection);

                //Bullet Shoot
                var bullet = VRCInstantiate(bulletPrefab);
                bullet.transform.position = bulletSpawnPoint.position;
                bullet.transform.rotation = bulletSpawnPoint.rotation;
                bullet.GetComponent<Rigidbody>().velocity = bulletSpawnPoint.forward * bulletSpeed;
            }
        }
    }
}
