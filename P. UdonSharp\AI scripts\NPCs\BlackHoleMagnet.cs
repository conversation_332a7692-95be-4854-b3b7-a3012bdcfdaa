﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Continuous)]
public class BlackHoleMagnet : UdonSharpBehaviour
{
    [UdonSynced] private bool isPickedUp = false;
    public bool IsBeingPickedUpByLocal;

    public VRC_Pickup pickup;
    public AIbotNPC1[] AIontheScene;

    void Start()
    {
        pickup = GetComponent<VRC_Pickup>();
    }

    public override void OnPickup()
    {
        if (!Networking.IsOwner(gameObject)){Networking.SetOwner(Networking.LocalPlayer, gameObject);}

        if (pickup.currentPlayer == Networking.LocalPlayer){IsBeingPickedUpByLocal = true;}

        isPickedUp = true;
        SyncAIState(1);

        foreach (AIbotNPC1 bot in AIontheScene){bot.TakeOwnership();}

        RequestSerialization();
    }

    public override void OnDrop()
    {
        isPickedUp = false;
        SyncAIState(0);
        IsBeingPickedUpByLocal = false;
        RequestSerialization();
    }

    private void SyncAIState(int state)
    {
        foreach (AIbotNPC1 bot in AIontheScene)
        {
            bot.State = state;
            bot.RequestSerialization();
        }
    }

    public override void OnDeserialization(){SyncAIState(isPickedUp ? 1 : 0);}
}
