﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class C02BossSystem : UdonSharpBehaviour
{
    public Animator BossAnimator;
    public GameObject MainBody,Minion;
    public GameObject[] Body;
    public AudioSource GlobalAudioSource;
    private VRCPlayerApi localPlayer;
    public UniversalBossHealthSystem HealthSystem;

    public int Attacks;
    public bool BossActive;

    public float SightRange = 50f;
    public Vector3 SpawnedPoint;


    //TeleportStuff
    public GameObject TeleportIndicator;
    public ParticleSystem TeleportIndicatorEffect;
    public AudioClip TeleportIndicatorSound,SuccessfulTeleportSound;

    //Missile
    public GameObject Missile;
    public Transform MissileSpawnpoint;
    public bool ThrewMissile;

    //Death
    public GameObject Explosion;
    public bool IsDead;

    //StoryModeEnemy
    public bool IsStoryModeEnemy;

    //Money
    public int MoneyDropAmount;

    void Start(){
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        TeleportIndicator.SetActive(false);
        TeleportIndicator.transform.position = transform.position;
        BossActive = true;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 2f);
        SendCustomEventDelayedSeconds(nameof(StareAtTarget), 1f);
    }
    public void StareAtTarget()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange && !IsDead){
            // Get player position
            Vector3 playerPos = localPlayer.GetPosition();

            // Create a flattened direction vector (only horizontal)
            Vector3 flatDirection = playerPos - transform.position;
            flatDirection.y = 0; // Remove the vertical component
            flatDirection = flatDirection.normalized;

            // Create rotation only around Y axis
            Quaternion rotation = Quaternion.LookRotation(flatDirection);
            MainBody.transform.rotation = rotation;
            SendCustomEventDelayedSeconds(nameof(StareAtTarget), 0.05f);
        }
        else{
            SendCustomEventDelayedSeconds(nameof(StareAtTarget), 1f);
        }
    }

    public void OnDisable(){
        Minion.SetActive(false);
        BossActive = false;
    }
    public void OnEnable(){
        BossActive = true;
    }

    public void EnemyAISystem()
    {
        if(HealthSystem.Health <= 0){IsDead = true;}

        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange && !IsDead && BossActive){
            HealthSystem.CanBeKilled = true;
            HealthSystem.HealthBar.SetActive(true);
            if(Minion.activeSelf && ThrewMissile == false){Attacks = Random.Range(2, 4);}
            else if(Minion.activeSelf && ThrewMissile == true){Attacks = Random.Range(3, 4);}
            else if(!Minion.activeSelf && ThrewMissile == true){
                int Attack = Random.Range(0, 2);
                if(Attack == 0){Attacks = 1;}
                else if(Attack == 1){Attacks = 3;}
            }
            else{
                Attacks = Random.Range(1, 4);
            }

            if(Attacks == 1)
            {
                ThrewMissile = false;
                BossAnimator.SetInteger("AnimType", 1);
                SendCustomEventDelayedSeconds(nameof(AttackOne), 2f);
            }
            else if(Attacks == 2)
            {
                ThrewMissile = true;
                Missile.SetActive(false);
                Missile.transform.position = MissileSpawnpoint.position;
                Missile.transform.rotation = MissileSpawnpoint.rotation;
                Missile.transform.parent = transform;
                BossAnimator.SetInteger("AnimType", 2);
                SendCustomEventDelayedSeconds(nameof(AttackTwo), 1.5f);
            }
            else if(Attacks == 3)
            {
                ThrewMissile = false;
                BossAnimator.SetInteger("AnimType", 0);
                SendCustomEventDelayedSeconds(nameof(AttackThree), 0.1f);
            }
        }
        else if(!IsDead || !BossActive){
            HealthSystem.CanBeKilled = false;
            HealthSystem.HealthBar.SetActive(false);
            if(HealthSystem.Health < HealthSystem.HealthMax){HealthSystem.Health += HealthSystem.HealthMax/5;}
            if(HealthSystem.Health > HealthSystem.HealthMax){HealthSystem.Health = HealthSystem.HealthMax;}
            HealthSystem.DamageText.text = HealthSystem.Health.ToString() + "/" + HealthSystem.HealthMax.ToString();
            HealthSystem.HealthSlider.value = HealthSystem.Health;
            Attacks = 1;

            if(Attacks == 1)
            {
                ThrewMissile = false;
                BossAnimator.SetInteger("AnimType", 0);
                SendCustomEventDelayedSeconds(nameof(IdleOne), 2f);
            }
        }
        else{
            HealthSystem.IsDead = true;
            HealthSystem.CanBeKilled = false;
            HealthSystem.HealthBar.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(IsDying), 1f);
        }
    }

    public void AttackOne()
    {
        BossAnimator.SetInteger("AnimType", 0);
        SendCustomEventDelayedSeconds(nameof(AttackOne2), 0.55f);
    }
    public void AttackOne2()
    {
        if(HealthSystem.Health > HealthSystem.HealthMax/4){
            HealthSystem.Health -= 800;
            HealthSystem.DamageText.text = HealthSystem.Health.ToString() + "/" + HealthSystem.HealthMax.ToString();
            HealthSystem.HealthSlider.value = HealthSystem.Health;
        }
        Minion.SetActive(true);
        Minion.transform.position = transform.position;
        Minion.transform.rotation = transform.rotation;
        Minion.transform.parent = null;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }

    public void AttackTwo()
    {
        Missile.SetActive(true);
        Missile.transform.position = MissileSpawnpoint.position;
        Missile.transform.rotation = MissileSpawnpoint.rotation;
        Missile.transform.parent = null;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.5f);
    }

    public void AttackThree()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.parent = null;
        TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-10f,10f),1f,Random.Range(-10f,10f));
        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(AttackThree2), 1f);
    }

    public void AttackThree2()
    {
        if(HealthSystem.Health <= HealthSystem.HealthMax/3){
            HealthSystem.Health += 50;
            HealthSystem.DamageText.text = HealthSystem.Health.ToString() + "/" + HealthSystem.HealthMax.ToString();
            HealthSystem.HealthSlider.value = HealthSystem.Health;
        }
        TeleportIndicator.transform.parent = transform;
        MainBody.transform.position = TeleportIndicator.transform.position;
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }


    public void IdleOne()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.parent = null;
        TeleportIndicator.transform.position = SpawnedPoint + new Vector3(Random.Range(-2f,2f),1f,Random.Range(-2f,2f));;
        SendCustomEventDelayedSeconds(nameof(IdleOne2), 1f);
    }
    public void IdleOne2()
    {
        TeleportIndicator.SetActive(false);
        TeleportIndicator.transform.parent = transform;
        MainBody.transform.position = TeleportIndicator.transform.position;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }


    public void IsDying()
    {
        BossAnimator.SetInteger("AnimType", 3);
        SendCustomEventDelayedSeconds(nameof(ExplosionFunction), 3f);
    }
    public void ExplosionFunction()
    {
        //MoneyDrop
        if(MoneyDropAmount != 0){
            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(MoneyDropAmount);
        }

        //StoryModeEnemy Daily Quest
        if(IsStoryModeEnemy){
            GameObject QuestObject = GameObject.Find("Daily Quests");
            QuestObject.GetComponent<DailyQuestsSystem>().DailyQuest2();
        }

        for (int i = 0; i < Body.Length; i++)
        {
            Body[i].SetActive(false);
        }
        Explosion.SetActive(true);
        SendCustomEventDelayedSeconds(nameof(Death), 3f);
    }
    public void Death()
    {
        IsDead = true;
        Destroy(gameObject);
        Destroy(Missile);
        Destroy(Minion);
    }

    void OnDestroy(){
        Destroy(TeleportIndicator);
        Destroy(gameObject);
        Destroy(Missile);
        Destroy(Minion);
    }
}
