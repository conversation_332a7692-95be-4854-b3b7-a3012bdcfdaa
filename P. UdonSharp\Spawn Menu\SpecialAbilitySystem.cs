﻿using TMPro;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Data;
using VRC.SDKBase;
using VRC.Udon.Common;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpecialAbilitySystem : UdonSharpBehaviour
{
    public int SpecialAbilityType; //0 is None, 1 is Supply Drop, 2 is Super Jump, 3 is Shockwave, 4 is Homing Rocket, 5 is Invincibility

    public int Cooldown;
    public int AbilityCooldownMax;

    public TextMeshProUGUI AbilityText;
    public Image CustomizableImage;
    public Color[] CustomizableColor;
    public Slider[] AbilityCooldownSlider;
    public GameObject[] Slider;

    //Visuals
    public TextMeshProUGUI[] AbilityCooldownText;
    public Image AbilityBarImage;
    public Sprite[] AbilityBarImageSprite;

    //Supply Drop
    public GameObject SupplyDropPrefab;
    public Transform SupplyDropSpawnPoint;
    public GameObject SupplyDropObject;

    //ProtectionBarrier
    public GameObject ProtectionBarrier, ProtectionParentObject;

    //Homing Rocket
    public GameObject RocketPrefab;
    public Transform RocketSpawnPoint;

    //invincibility
    public PlayerWorldStartSystem PlayerWorldStartSystem;
    public GameObject InvincibilityObject;

    //Required
    private float lastVertical;
    public VRCPlayerApi localPlayer;

    void Start()
    {
        SpecialAbilityType = 0;
        AbilityText.text = "R-stick Up: None";
        CustomizableImage.color = CustomizableColor[5];

        AbilityBarImage.sprite = AbilityBarImageSprite[5];

        localPlayer = Networking.LocalPlayer;

        UpdateSlider();
        for (int i = 0; i < Slider.Length; i++){Slider[i].SetActive(false);}

        //ProtectionBarrier
        ProtectionBarrier.SetActive(false);

        //Invincibility
        InvincibilityObject.SetActive(false);

        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);
    }

    public void UpdateTimer()
    {
        if (Input.GetKey(KeyCode.E) && Cooldown >= AbilityCooldownMax)
        {
            if(SpecialAbilityType == 1){
                GameObject SupplyDrop = Instantiate(SupplyDropPrefab, SupplyDropSpawnPoint.position + new Vector3(Random.Range(-2, 2), 0, Random.Range(-2, 2)), SupplyDropSpawnPoint.rotation * Quaternion.Euler(0, Random.Range(0, 360), 0));
                SupplyDropObject = SupplyDrop;
                SupplyDropObject.transform.parent = null;
                SendCustomEventDelayedSeconds(nameof(DestroySupplyDrop), 5f);
            }
            if(SpecialAbilityType == 2){
                //Super Jump - forward if moving, upward if stationary
                Vector3 currentVelocity = localPlayer.GetVelocity();
                Vector3 horizontalVelocity = new Vector3(currentVelocity.x, 0, currentVelocity.z);

                Vector3 jumpVelocity;
                if (horizontalVelocity.magnitude > 0.5f) // Player is moving
                {
                    Vector3 jumpDirection = horizontalVelocity.normalized;
                    jumpVelocity = jumpDirection * 15f + Vector3.up * 12f;
                }
                else // Player is stationary
                {jumpVelocity = Vector3.up * 18f;}

                localPlayer.SetVelocity(currentVelocity + jumpVelocity);
            }
            if(SpecialAbilityType == 3){
                ProtectionBarrier.SetActive(true);
                ProtectionBarrier.transform.parent = null;
                SendCustomEventDelayedSeconds(nameof(ProtectionBarrierOff), 10f);
            }
            if(SpecialAbilityType == 4){
                GameObject Rocket = Instantiate(RocketPrefab, RocketSpawnPoint.position, RocketSpawnPoint.rotation);
                Rocket.transform.parent = null;
            }
            if(SpecialAbilityType == 5){
                PlayerWorldStartSystem.SpecialImmunityFrames = true;
                InvincibilityObject.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(DeactivateInvincibility), 3f);
            }
            Cooldown = 0;
        }

        if(Cooldown < AbilityCooldownMax){
            SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);
            Cooldown += 1;
            for (int i = 0; i < AbilityCooldownText.Length; i++){AbilityCooldownText[i].text = "[ON COOLDOWN]";}
            UpdateSlider();
        }
        else{
            if(SpecialAbilityType != 0){
            for (int i = 0; i < AbilityCooldownText.Length; i++){AbilityCooldownText[i].text = "[Ready]";}
            }
            SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.1f);
        }
    }


    public override void InputLookVertical(float value, UdonInputEventArgs args)
    {
        if (!localPlayer.IsUserInVR()) return;

        if (lastVertical < 0.5f && value >= 0.5f && Cooldown >= AbilityCooldownMax){

            if(SpecialAbilityType == 1){
                GameObject SupplyDrop = Instantiate(SupplyDropPrefab, SupplyDropSpawnPoint.position + new Vector3(Random.Range(-2, 2), 0, Random.Range(-2, 2)), SupplyDropSpawnPoint.rotation * Quaternion.Euler(0, Random.Range(0, 360), 0));
                SupplyDropObject = SupplyDrop;
                SupplyDropObject.transform.parent = null;
                SendCustomEventDelayedSeconds(nameof(DestroySupplyDrop), 10f);
            }
            if(SpecialAbilityType == 2){
                //Super Jump - forward if moving, upward if stationary
                Vector3 currentVelocity = localPlayer.GetVelocity();
                Vector3 horizontalVelocity = new Vector3(currentVelocity.x, 0, currentVelocity.z);

                Vector3 jumpVelocity;
                if (horizontalVelocity.magnitude > 0.5f) // Player is moving
                {
                    Vector3 jumpDirection = horizontalVelocity.normalized;
                    jumpVelocity = jumpDirection * 15f + Vector3.up * 12f;
                }
                else // Player is stationary
                {jumpVelocity = Vector3.up * 18f;}

                localPlayer.SetVelocity(currentVelocity + jumpVelocity);
            }
            if(SpecialAbilityType == 3){
                ProtectionBarrier.SetActive(true);
                ProtectionBarrier.transform.parent = null;
                SendCustomEventDelayedSeconds(nameof(ProtectionBarrierOff), 10f);
            }
            if(SpecialAbilityType == 4){
                GameObject Rocket = Instantiate(RocketPrefab, RocketSpawnPoint.position, RocketSpawnPoint.rotation);
                Rocket.transform.parent = null;
            }
            if(SpecialAbilityType == 5){
                PlayerWorldStartSystem.SpecialImmunityFrames = true;
                InvincibilityObject.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(DeactivateInvincibility), 3f);
            }
            Cooldown = 0;
        }
        lastVertical = value;
    }


    public void NextMechanic(){
        SpecialAbilityType += 1;
        if(SpecialAbilityType == 1){
            AbilityCooldownMax = 25;
            AbilityText.text = "R-stick Up: Supply Airdrop!\n (Healing Cube)";
            CustomizableImage.color = CustomizableColor[0];

            AbilityBarImage.sprite = AbilityBarImageSprite[0];

            UpdateSlider();
        }
        if(SpecialAbilityType == 2){
            AbilityCooldownMax = 10;
            AbilityText.text = "R-stick Up: Super Jump!\n (Movement)";
            CustomizableImage.color = CustomizableColor[1];

            AbilityBarImage.sprite = AbilityBarImageSprite[1];

            UpdateSlider();
        }
        if(SpecialAbilityType == 3){
            AbilityCooldownMax = 20;
            AbilityText.text = "R-stick Up: Protection Barrier!\n (Physical)";
            CustomizableImage.color = CustomizableColor[2];

            AbilityBarImage.sprite = AbilityBarImageSprite[2];

            UpdateSlider();
        }
        if(SpecialAbilityType == 4){
            AbilityCooldownMax = 25;
            AbilityText.text = "R-stick Up: Heated Rocket!\n (Fire)";
            CustomizableImage.color = CustomizableColor[3];

            AbilityBarImage.sprite = AbilityBarImageSprite[3];

            UpdateSlider();
        }
        if(SpecialAbilityType == 5){
            AbilityCooldownMax = 50;
            AbilityText.text = "R-stick Up: Temporary Invincibility!\n (5 Seconds)";
            CustomizableImage.color = CustomizableColor[4];

            AbilityBarImage.sprite = AbilityBarImageSprite[4];

            UpdateSlider();
        }
        if(SpecialAbilityType == 6){
            SpecialAbilityType = 0;
            AbilityCooldownMax = 0;
            AbilityText.text = "R-stick Up: None!";
            CustomizableImage.color = CustomizableColor[5];

            AbilityBarImage.sprite = AbilityBarImageSprite[5];

            for (int i = 0; i < AbilityCooldownText.Length; i++){
                AbilityCooldownText[i].text = "[None]";
            }

            UpdateSlider();
        }
        Cooldown = 0;
    }

    public void UpdateSlider(){
        for (int i = 0; i < AbilityCooldownSlider.Length; i++){
            AbilityCooldownSlider[i].value = Cooldown;
            AbilityCooldownSlider[i].maxValue = AbilityCooldownMax;
        }
        if(SpecialAbilityType == 0){for(int i = 0; i < Slider.Length; i++){Slider[i].SetActive(false);}}
        else{for(int i = 0; i < Slider.Length; i++){Slider[i].SetActive(true);}}
    }

    public void DestroySupplyDrop(){Destroy(SupplyDropObject);}
    public void ProtectionBarrierOff(){
        ProtectionBarrier.SetActive(false);
        ProtectionBarrier.transform.parent = ProtectionParentObject.transform;
        ProtectionBarrier.transform.position = ProtectionParentObject.transform.position;
    }
    public void DeactivateInvincibility(){PlayerWorldStartSystem.SpecialImmunityFrames = false; InvincibilityObject.SetActive(false);}
}
