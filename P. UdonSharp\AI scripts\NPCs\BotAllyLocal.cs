﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.AI;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BotAllyLocal : UdonSharpBehaviour
{
    public NavMeshAgent agent;
    public int TypeofAlly; //0 is normal, 1 is healing
    public float WanderDistance = 10f, sightRange = 30f, attackRange = 2f, healRange = 5f;
    public LayerMask whatIsEnemy;
    public LayerMask damageLayer;
    public LayerMask whatIsAlly; // Layer mask for detecting other ally bots
    public int healAmount = 5; // Amount to heal per tick
    public float healCooldown = 1f; // Time between heals
    private float healTimer = 0f;

    public int State;
    public float speed = 6f;
    public int Health = 100,HealthMax;
    public bool IsDead;

    private VRCPlayerApi localPlayer;

    //Effects
    public ParticleSystem Particle;
    public ParticleSystem Particle2;
    public ParticleSystem HealingParticle;
    public AudioSource AudioSource;
    public GameObject Body;
    public Collider collider;
    public GameObject[] DamageAreas;
    public Animator animator;
    public float animatorvalue;

    //HealthBar
    public GameObject HealthBar;
    public TextMeshPro DamageText;
    public Slider HealthSlider;
    public bool CanBeHit;
    public float ImmunityFramesDoneDelay = 0.1f;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        collider = GetComponent<Collider>();

        agent.speed = speed;

        //Health Stuff
        HealthMax = (int)(HealthMax * transform.localScale.x);
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.maxValue = HealthMax;
        HealthSlider.value = Health;
        CanBeHit = true;

        SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
    }

    public void UpdateDestination()
    {
        agent.enabled = true;

        if (!agent.isOnNavMesh){Destroy(gameObject);}

        bool EnemyInSightRange = Physics.CheckSphere(transform.position, sightRange, whatIsEnemy);
        bool EnemyInAttackRange = Physics.CheckSphere(transform.position, attackRange, whatIsEnemy);

        if(healTimer < healCooldown){healTimer += 0.1f;}

        if (EnemyInSightRange) {
            State = 1;
            agent.speed = speed*2;
        }
        else if (!EnemyInSightRange) {
            State = 0;
            agent.speed = speed;
        }

        if(!EnemyInAttackRange && !EnemyInSightRange && State == 0 && animatorvalue > 0){animatorvalue -= 0.05f;}
        else if(!EnemyInAttackRange && EnemyInSightRange && State == 1 && animatorvalue < 0.50f){animatorvalue += 0.05f;}
        else if(!EnemyInAttackRange && EnemyInSightRange && State == 1 && animatorvalue > 0.55f){animatorvalue -= 0.05f;}
        else if(EnemyInAttackRange && EnemyInSightRange && State == 1 && animatorvalue < 1f){animatorvalue += 0.1f;}

        animator.SetFloat("EnemyAnimationValue", animatorvalue);

        // For healing bots, try to heal nearby allies
        if (TypeofAlly == 1 && !IsDead && healTimer >= healCooldown) {HealNearbyAllies(); healTimer = 0f;}

        // Adjust update timing dynamically
        if(TypeofAlly == 0){
            if(State == 0){
                if(!IsDead){SetRandomDestination();}
                SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
            }
            else if (State == 1){
                if(!IsDead){TargetedDestination();}
                SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
            }
        }
        else if(TypeofAlly == 1){
            if(State == 0){
                if(!IsDead){
                    FindAlliesToHeal();
                }
                SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
            }
            else if (State == 1){
                if(!IsDead){
                    SetRandomDestination();
                }
                SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
            }
        }
    }

    void SetRandomDestination()
    {
        if (!agent.isOnNavMesh) return;

        if (agent.remainingDistance < agent.stoppingDistance*1.1f) {
            Vector3 targetPosition = Random.insideUnitSphere * WanderDistance + localPlayer.GetPosition();
            agent.SetDestination(targetPosition);
        }
        else{
            agent.stoppingDistance = 1f;
        }
    }
    void TargetedDestination()
    {
        if (!agent.isOnNavMesh) return;

        agent.stoppingDistance = 3f;

        Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, sightRange, whatIsEnemy);
        float closestDistance = Mathf.Infinity;
        GameObject closestEnemy = null;

        foreach (Collider enemy in enemiesInRange)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestEnemy = enemy.gameObject;
            }
        }

        if (closestEnemy != null){agent.SetDestination(closestEnemy.transform.position);}
        else
        {
            // If no enemies are found, follow the player
            Vector3 targetPosition = Random.insideUnitSphere * WanderDistance + localPlayer.GetPosition();
            agent.SetDestination(targetPosition);
        }
    }

    public void ImmunityFramesDone(){CanBeHit = true;}
    public void Dead()
    {
        IsDead = true;
        agent.SetDestination(transform.position);
        Particle.Play();
        Body.SetActive(false);
        agent.enabled = false;
        collider.enabled = false;
        HealthBar.SetActive(false);
        for (int i = 0; i < DamageAreas.Length; i++){DamageAreas[i].SetActive(false);}
        SendCustomEventDelayedSeconds(nameof(Delete), 3f);
    }
    public void Delete(){Destroy(gameObject);}

    void FindAlliesToHeal()
    {
        if (!agent.isOnNavMesh) return;

        Collider[] alliesInRange = Physics.OverlapSphere(transform.position, sightRange, whatIsAlly);
        float closestDistance = Mathf.Infinity;
        GameObject allyToHeal = null;
        BotAllyLocal allyScript = null;

        foreach (Collider ally in alliesInRange)
        {
            if (ally.gameObject == gameObject) continue;

            BotAllyLocal botScript = ally.GetComponent<BotAllyLocal>();
            if (botScript != null && !botScript.IsDead && botScript.Health < botScript.HealthMax)
            {
                float distance = Vector3.Distance(transform.position, ally.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    allyToHeal = ally.gameObject;
                    allyScript = botScript;
                }
            }
        }

        if (allyToHeal != null)
        {
            agent.stoppingDistance = 2f;
            agent.SetDestination(allyToHeal.transform.position);

            if (closestDistance <= healRange && allyScript != null)
            {
                if (HealingParticle != null){HealingParticle.Play();}
            }
        }
        else{SetRandomDestination();}
    }

    void HealNearbyAllies()
    {
        Collider[] alliesInRange = Physics.OverlapSphere(transform.position, healRange, whatIsAlly);

        bool healedSomeone = false;

        foreach (Collider ally in alliesInRange)
        {
            if (ally.gameObject == gameObject) continue;

            BotAllyLocal botScript = ally.GetComponent<BotAllyLocal>();
            if (botScript != null && !botScript.IsDead && botScript.Health < botScript.HealthMax)
            {
                botScript.Health += healAmount;

                if (botScript.Health > botScript.HealthMax){botScript.Health = botScript.HealthMax;}

                botScript.DamageText.text = botScript.Health.ToString() + "/" + botScript.HealthMax.ToString();
                botScript.HealthSlider.value = botScript.Health;

                healedSomeone = true;

                if (HealingParticle != null){HealingParticle.Play();}
            }
        }

        if (!healedSomeone && HealingParticle != null && alliesInRange.Length > 1){HealingParticle.Play();}
    }

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        if(CanBeHit && !IsDead)
        {
            Health -= HealthMax/5;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}
