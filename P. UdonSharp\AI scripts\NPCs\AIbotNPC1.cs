﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.AI;

public class AIbotNPC1 : UdonSharpBehaviour
{
    public NavMeshAgent agent;
    public float sightRange = 10f;
    public LayerMask whatIsPlayer;
    public GameObject BlackHoleMagnet;

    [UdonSynced] public int State;
    [UdonSynced] private Vector3 syncedDestination;
    [UdonSynced] private float syncedSpeed;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        if (Networking.IsOwner(gameObject))
        {
            SetRandomDestination();
            RequestSerialization();
        }

        SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.2f);
    }

    public void UpdateDestination()
    {

        bool playerInSightRange = Physics.CheckSphere(transform.position, sightRange, whatIsPlayer);

        if (playerInSightRange && State == 0) { syncedSpeed = 8f; }
        else if (!playerInSightRange && State == 0) { syncedSpeed = 3f; }
        else if (State == 1) { syncedSpeed = 10f; }

        agent.speed = syncedSpeed;

        // Update destination if reached
        if (agent.remainingDistance < 1f) { SetRandomDestination(); }

        RequestSerialization();

        // Adjust update timing dynamically
        if(playerInSightRange && State == 0){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.2f);
        }
        else if (!playerInSightRange && State == 0){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
        }
        else if (State == 1){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
        }
    }

    void SetRandomDestination()
    {
        if (!agent.isOnNavMesh || !Networking.IsOwner(gameObject)) return;

        Vector3 targetPosition = (State == 1) ? BlackHoleMagnet.transform.position : Random.insideUnitSphere * 100f + transform.position;
        agent.SetDestination(targetPosition);
        syncedDestination = targetPosition;

        RequestSerialization();
    }

    public override void OnDeserialization()
    {
        if (agent.isOnNavMesh)
        {
            agent.SetDestination(syncedDestination);
            agent.speed = syncedSpeed;
        }
    }

    public override void OnOwnershipTransferred(VRCPlayerApi newOwner)
    {
        if (Networking.IsOwner(gameObject))
        {
            SetRandomDestination();
            RequestSerialization();
        }
    }

    public void TakeOwnership()
    {
        if (!Networking.IsOwner(gameObject))
        {
            Networking.SetOwner(localPlayer, gameObject);
            RequestSerialization();
        }
    }

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

        Vector3 directionToPlayer = localPlayer.GetPosition() - transform.position;
        localPlayer.SetVelocity(-directionToPlayer.normalized * 50);
    }
}