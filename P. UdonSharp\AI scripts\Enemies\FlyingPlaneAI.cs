﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class FlyingPlaneAI : UdonSharpBehaviour
{

    public float speed,Turn;
    public int ChangeTargetDelay,BombDelay,BombDelayMax = 300,CurrentTarget, ChangeTargetDelayMax = 100;
    public int FlyingMode; //0 is plane, 1 is carrier, 2 is missile
    public int PlaneType; //0 bomber Plane, 1 Fighter Plane
    public int TargetType; //0 Is targets, 1 is targetPositions
    public Transform[] targets;
    public Vector3[] targetPositions;

    public GameObject BombBotObject;

    void Start()
    {
        if(BombBotObject != null && PlaneType == 0){BombBotObject.SetActive(false);}
        SendCustomEventDelayedSeconds(nameof(PlaneAIInterval), 1f);
    }

    public void PlaneAIInterval(){

        ChangeTargetDelay++;
        if(PlaneType == 0 && FlyingMode == 0){BombDelay++;}

        if(ChangeTargetDelay > ChangeTargetDelayMax){
            ChangeTargetDelay = 0;
            CurrentTarget = Random.Range(0, targets.Length);
        }
        if(BombDelay > BombDelayMax && PlaneType == 0 && FlyingMode == 0){
            BombDelay = 0;

            BombBotObject.transform.parent = gameObject.transform;
            BombBotObject.SetActive(true);
            BombBotObject.transform.position = transform.position - new Vector3(0,-1,0);
            BombBotObject.transform.rotation = transform.rotation;
        }

        if(TargetType == 0){
            if (targets[CurrentTarget] != null)
            {
                Vector3 direction = (targets[CurrentTarget].position - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
        }
        else if(TargetType == 1){
            //for(int i = 0; i < targetPositions.Length; i++){targetPositions[i] = targets[i].position;} for syncing
            if (targetPositions[CurrentTarget] != null)
            {
                Vector3 direction = (targetPositions[CurrentTarget] - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
        }

        SendCustomEventDelayedSeconds(nameof(PlaneAIInterval), 0.03f);
    }
}
