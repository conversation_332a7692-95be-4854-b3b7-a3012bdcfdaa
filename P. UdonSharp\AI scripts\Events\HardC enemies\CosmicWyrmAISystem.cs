﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CosmicWyrmAISystem : UdonSharpBehaviour
{
    public int AImode;
    public float speed,Turn;
    public float sightRangeMax = 25;
    private Vector3 SpawnedPoint;

    private VRCPlayerApi localPlayer;

    //Worm Dash Function
    public float DashDelay,DashDelayMax, DashingTime, dashSpeed;
    public bool IsDashing;
    public GameObject IndicatorBeforeDash;

    //Teleport Functions
    public float TeleportDelay,TeleportDelayMax;
    public bool DashingCooldown, TeleportSpawned, HasTeleported;
    public GameObject TeleportIndicator, TeleportIndicatorEffect;
    public float TeleportForward = 5f;
    public float TeleportMovementSpeed = 10f;

    //Extras
    public float ExtraHeight = 1f;
    public AudioSource TeleportSource;
    public AudioClip TeleportSound;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
    }

    public void UpdateTimer(){
        if(localPlayer == null) return;

        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && !IsDashing && !DashingCooldown){
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,ExtraHeight,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
            Vector3 directionPlayer = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Slerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;

            if(DashDelay >= DashDelayMax){
                IsDashing = true;
                DashingCooldown = true;
                if(IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(false);}
                transform.rotation = Quaternion.LookRotation(directionPlayer);
            }
            else if(DashDelay > DashDelayMax/1.5f && DashDelay < DashDelayMax){
                DashDelay++;
                if(!IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(true);}
            }
            else if(DashDelay <= DashDelayMax/1.5f){
                DashDelay++;
                if(IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(false);}
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && IsDashing && DashingCooldown){
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn * 0.5f; // Allow some rotation during dash
            transform.rotation = Quaternion.Slerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * dashSpeed;

            if(DashDelay > 0){DashDelay -= DashingTime;}
            else{IsDashing = false;}
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && !IsDashing && DashingCooldown){
            // Continue circling player during teleport cooldown
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,ExtraHeight,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn * 0.3f; // Slower rotation during cooldown
            transform.rotation = Quaternion.Slerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * (speed * 0.5f); // Slower movement during cooldown

            if(!TeleportSpawned){
                TeleportIndicator.transform.parent = null;
                TeleportIndicatorEffect.transform.parent = null;
                int RandomPositionDirection = Random.Range(0, 4);
                if(RandomPositionDirection == 0){TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(7f,15f),4f,Random.Range(7f,15f));}
                else if(RandomPositionDirection == 1){TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(7f,15f),4f,Random.Range(-15f,-7f));}
                else if(RandomPositionDirection == 2){TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-15f,-7f),4f,Random.Range(7f,15f));}
                else if(RandomPositionDirection == 3){TeleportIndicator.transform.position = localPlayer.GetPosition() + new Vector3(Random.Range(-15f,-7f),4f,Random.Range(-15f,-7f));}

                //spawn teleportindicatoreffect in front of the enemy to get into the portal
                TeleportIndicatorEffect.transform.position = gameObject.transform.position + transform.forward * TeleportForward;

                TeleportIndicator.SetActive(true);
                TeleportIndicatorEffect.SetActive(true);
                HasTeleported = false;
                TeleportDelay = 0;

                DashDelayMax = Random.Range(75f, 125f);
                DashingTime = Random.Range(5f, 10f);

                TeleportSpawned = true;
            }

            if(TeleportDelay >= TeleportDelayMax){
                TeleportIndicator.SetActive(false);
                TeleportIndicatorEffect.SetActive(false);
                TeleportSpawned = false;
                DashingCooldown = false;
                TeleportIndicator.transform.parent = transform;
                TeleportIndicatorEffect.transform.parent = transform;
                TeleportDelay = 0;
            }
            else if(TeleportDelay > TeleportDelayMax/2f && TeleportDelay < TeleportDelayMax){
                if(!HasTeleported){
                    //TeleportIndicatorlooks at player
                    Vector3 direction = (localPlayer.GetPosition() - TeleportIndicator.transform.position).normalized;
                    Quaternion rotation = Quaternion.LookRotation(direction);
                    TeleportIndicator.transform.rotation = rotation;

                    transform.position = TeleportIndicator.transform.position; 
                    transform.rotation = TeleportIndicator.transform.rotation;
                    TeleportSource.PlayOneShot(TeleportSound);
                    HasTeleported = true;
                }
                TeleportDelay++;
            }
            else{TeleportDelay++;}
        }


        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRangeMax){
            Vector3 direction = (SpawnedPoint - transform.position + new Vector3(0,ExtraHeight,0)).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;

            DashDelay = 0;
            DashingCooldown = false;
            TeleportDelay = 0;
            TeleportSpawned = false;
            HasTeleported = false;
            IsDashing = false;
            if(IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(false);}
            if(TeleportIndicator.activeSelf){TeleportIndicator.SetActive(false);}
            if(TeleportIndicatorEffect.activeSelf){TeleportIndicatorEffect.SetActive(false);}
        }

        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.02f);
    }

    public void OnDestroy(){
        Destroy(IndicatorBeforeDash);
        Destroy(TeleportIndicator);
        Destroy(TeleportIndicatorEffect);
    }
}
