﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GuardianStomperGeneralSystem : UdonSharpBehaviour
{

    public GameObject Body;
    public Rigidbody rb;
    public AudioSource GlobalAudioSource;

    public int Attacks;

    public float Force = 250f;
    public float SightRange = 50f;
    public Vector3 SpawnedPoint;

    public VRCPlayerApi localPlayer;

    //Flash indicator effect
    public ParticleSystem FlashIndicator;
    public AudioSource FlashIndicatorSound;

    //Teleport Indicator effect
    public GameObject TeleportIndicator;
    public ParticleSystem TeleportIndicatorEffect;
    public AudioClip TeleportIndicatorSound,SuccessfulTeleportSound;

    //Cooldowns
    public bool HasRan, HasDashed, IsIdle;

    //Optional Animations
    public Animator BodyAnimator;

    //HealthSystem
    public UniversalEnemySystem HealthSystem;

    //phase 2
    public LineRenderer LineRenderer;
    public Transform LineEnemyPosition;
    public GameObject StormDamageObject, MeteorsObject;
    public bool SpawnMeteorsduringPhase2;
    public GameObject[] OptionalPhase1Objects,OptionalPhase2Objects;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        TeleportIndicator.SetActive(false);
        MeteorsObject.SetActive(false);
        TeleportIndicator.transform.position = transform.position;

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
        SendCustomEventDelayedSeconds(nameof(PhaseActivator), 0.1f);
    }

    public void EnemyAISystem()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
            if(HasRan == true && HasDashed == false){Attacks = 2;}
            else if(HasRan == false && HasDashed == true){Attacks = 1;}
            else if(HasRan == false && HasDashed == false){Attacks = Random.Range(1, 3);}

            IsIdle = false;

            if(Attacks == 1)
            {
                if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 1);}
                HasRan = true;
                HasDashed = false;
                SendCustomEventDelayedSeconds(nameof(AttackOne), 1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(5f, 10f));
            }
            else if(Attacks == 2){
                if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 2);}
                HasRan = false;
                HasDashed = true;
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(AttackTwo), 1f);
            }
        }
        else{
            Attacks = Random.Range(1, 3);

            IsIdle = true;

            if(BodyAnimator != null){BodyAnimator.SetInteger("AnimType", 0);}

            if(Attacks == 1)
            {
                SendCustomEventDelayedSeconds(nameof(IdleOne), 0.1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f, 4f));
            }
            else if(Attacks == 2)
            {
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(IdleTwo), 0.1f);
            }
        }
    }

    public void PhaseActivator()
    {
        if(HealthSystem.Health >= HealthSystem.HealthMax/2 && IsIdle == false)
        {
            if(LineRenderer != null && LineRenderer.enabled == true){LineRenderer.enabled = false;}

            if(StormDamageObject != null && StormDamageObject.activeSelf == true){StormDamageObject.SetActive(false);}

            if(OptionalPhase1Objects != null && OptionalPhase1Objects[0].activeSelf == false){for (int i = 0; i < OptionalPhase1Objects.Length; i++){OptionalPhase1Objects[i].SetActive(true);}}
            if(OptionalPhase2Objects != null && OptionalPhase2Objects[0].activeSelf == true){for (int i = 0; i < OptionalPhase2Objects.Length; i++){OptionalPhase2Objects[i].SetActive(false);}}
        }
        else if(HealthSystem.Health < HealthSystem.HealthMax/2 && IsIdle == false)
        {
            //Pull the player close if they are more than 75% of the range away
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > SightRange*0.5f && Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange*1f)
            {
                //Enable Linerenderer and put it between the player and the enemy
                if(LineRenderer != null && LineRenderer.enabled == false){LineRenderer.enabled = true;}
                LineRenderer.SetPosition(0, LineEnemyPosition.position);
                LineRenderer.SetPosition(1, localPlayer.GetPosition());

                Vector3 directionToPlayer = transform.position - (localPlayer.GetPosition() + new Vector3(0,0.2f,0));
                localPlayer.SetVelocity(directionToPlayer.normalized * 25f);

                if(StormDamageObject != null && StormDamageObject.activeSelf == false){StormDamageObject.SetActive(true);}
            }
            else{
                if(LineRenderer != null && LineRenderer.enabled == true){LineRenderer.enabled = false;}
                if(StormDamageObject != null && StormDamageObject.activeSelf == true){StormDamageObject.SetActive(false);}
            }

            if(OptionalPhase1Objects != null && OptionalPhase1Objects[0].activeSelf == true){for (int i = 0; i < OptionalPhase1Objects.Length; i++){OptionalPhase1Objects[i].SetActive(false);}}
            if(OptionalPhase2Objects != null && OptionalPhase2Objects[0].activeSelf == false){for (int i = 0; i < OptionalPhase2Objects.Length; i++){OptionalPhase2Objects[i].SetActive(true);}}
            if(SpawnMeteorsduringPhase2 == true && MeteorsObject.activeSelf == false){MeteorsObject.SetActive(true);}
        }
        else if(IsIdle == true){
            if(LineRenderer != null && LineRenderer.enabled == true){LineRenderer.enabled = false;}

            if(StormDamageObject != null && StormDamageObject.activeSelf == true){StormDamageObject.SetActive(false);}

            if(OptionalPhase1Objects != null && OptionalPhase1Objects[0].activeSelf == false){for (int i = 0; i < OptionalPhase1Objects.Length; i++){OptionalPhase1Objects[i].SetActive(true);}}
            if(OptionalPhase2Objects != null && OptionalPhase2Objects[0].activeSelf == true){for (int i = 0; i < OptionalPhase2Objects.Length; i++){OptionalPhase2Objects[i].SetActive(false);}}
        }

        SendCustomEventDelayedSeconds(nameof(PhaseActivator), 0.1f);
    }

    public void AttackOne()
    {
        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Slowly move towards the direction of the player smoothly. Slowly enough so it's not instant
        Body.transform.rotation = Quaternion.Slerp(Body.transform.rotation, Quaternion.LookRotation(flatDirection), 0.01f);

        rb.AddForce((Body.transform.forward)*Force);
        if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(AttackOne), 0.05f);}
    }


    public void AttackTwo()
    {
        //Body goes up in the air and waits a second before dashing at the player
        rb.AddForce(Vector3.up*Force*30);

        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + new Vector3(0,0.2f,0);

        //Dashes at the player
        rb.AddForce((playerPos - transform.position).normalized*Force*50);

        FlashIndicator.Play();
        FlashIndicatorSound.PlayOneShot(FlashIndicatorSound.clip);

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 2f);
    }


    //Idle Attacks
    public void IdleOne()
    {
        // Get player position
        Vector3 playerPos = SpawnedPoint + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f));
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        Body.transform.rotation = rotation;

        if(Attacks == 1 && IsIdle == true){SendCustomEventDelayedSeconds(nameof(IdleOne), 2f);}
    }

    public void IdleTwo()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.position = SpawnedPoint + new Vector3(Random.Range(-15f,15f),1f,Random.Range(-15f,15f));
        rb.isKinematic = true;
        TeleportIndicatorEffect.Play();
        GlobalAudioSource.PlayOneShot(TeleportIndicatorSound);
        SendCustomEventDelayedSeconds(nameof(IdleTwo2), 6f);
    }

    public void IdleTwo2()
    {
        Body.transform.position = TeleportIndicator.transform.position;
        rb.isKinematic = false;
        TeleportIndicator.SetActive(false);
        GlobalAudioSource.PlayOneShot(SuccessfulTeleportSound);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
    }

    void OnDestroy(){Destroy(TeleportIndicator);}
}