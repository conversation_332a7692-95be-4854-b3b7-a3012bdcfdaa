﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class WormAISystem : UdonSharpBehaviour
{
    public GameObject WormHead;
    public Rigidbody rb;
    public int SightRange = 100;
    public int Attacks;
    public float Speed;
    public float Multiplier = 1f;
    public bool PlayerNotInRange,DashCooldown;
    public Vector3 LastPosition;

    public VRCPlayerApi localPlayer;

    //Flash indicator effect
    public ParticleSystem FlashIndicator;
    public AudioSource FlashIndicatorSound;

    public ParticleSystem TeleFlashIndicator2;
    public AudioSource TeleIndicatorSound2;
    public GameObject TeleportIndicator;

    void Start(){
        LastPosition = transform.position;
        localPlayer = Networking.LocalPlayer;
        TeleportIndicator.SetActive(false);
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);
    }

    void OnEnable(){LastPosition = transform.position;}

    public void EnemyAISystem()
    {
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){
            if(!DashCooldown){Attacks = Random.Range(1, 3);}
            else{Attacks = 1;}

            PlayerNotInRange = false;
            if(Attacks == 1)
            {
                DashCooldown = false;
                SendCustomEventDelayedSeconds(nameof(StartAttackone), 0.1f/Multiplier);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f/Multiplier, 5f/Multiplier));
            }
            else if(Attacks == 2)
            {
                DashCooldown = true;
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(AttackOne), 0.2f/Multiplier);
            }
        }
        else{
            Attacks = Random.Range(1, 4);
            PlayerNotInRange = true;
            if(Attacks == 1){
                SendCustomEventDelayedSeconds(nameof(IdleOne), 0.1f);
                SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f/Multiplier, 5f/Multiplier));
            }
            else if(Attacks == 2)
            {
                rb.velocity = Vector3.zero;
                SendCustomEventDelayedSeconds(nameof(IdleTwo), 0.2f/Multiplier);
            }
            else if(Attacks == 3)
            {
                SendCustomEventDelayedSeconds(nameof(TeleportingIdle), 0.1f/Multiplier);
            }
        }
    }

    public void StartAttackone()
    {
        rb.AddForce((localPlayer.GetPosition() - WormHead.transform.position)*Speed*Multiplier);
        WormHead.transform.rotation = Quaternion.LookRotation((localPlayer.GetPosition() + new Vector3(0,1,0)) - WormHead.transform.position);
        if(Attacks == 1){SendCustomEventDelayedSeconds(nameof(StartAttackone), 0.01f);}
    }

    public void AttackOne()
    {
        WormHead.transform.rotation = Quaternion.LookRotation((localPlayer.GetPosition() + new Vector3(0,1,0)) - WormHead.transform.position);
        rb.velocity = WormHead.transform.up*Speed;
        FlashIndicator.Play();
        FlashIndicatorSound.PlayOneShot(FlashIndicatorSound.clip);
        SendCustomEventDelayedSeconds(nameof(AttackOneHalf), 1f/Multiplier);
    }

    public void AttackOneHalf()
    {
        WormHead.transform.rotation = Quaternion.LookRotation((localPlayer.GetPosition() + new Vector3(0,2,0)) - WormHead.transform.position);
        rb.velocity = WormHead.transform.forward*Speed*2;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.5f);
    }


    //ifPlayerNotInRange
    public void IdleOne()
    {
        rb.AddForce((LastPosition - WormHead.transform.position)*Speed*Multiplier);
        WormHead.transform.rotation = Quaternion.LookRotation(LastPosition - WormHead.transform.position);
        if(Attacks == 1 && PlayerNotInRange){SendCustomEventDelayedSeconds(nameof(IdleOne), 0.01f);}
    }
    public void IdleTwo()
    {
        WormHead.transform.rotation = Quaternion.LookRotation(LastPosition - WormHead.transform.position);
        rb.velocity = WormHead.transform.up*Speed;
        SendCustomEventDelayedSeconds(nameof(IdleTwo2), 1f/Multiplier);
    }

    public void IdleTwo2()
    {
        WormHead.transform.rotation = Quaternion.LookRotation(LastPosition - WormHead.transform.position);
        rb.velocity = WormHead.transform.forward*Speed*2;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.5f);
    }

    public void TeleportingIdle()
    {
        TeleportIndicator.SetActive(true);
        TeleportIndicator.transform.parent = null;
        TeleportIndicator.transform.position = LastPosition + new Vector3(Random.Range(-5f/Multiplier,5f/Multiplier),1f,Random.Range(-5f/Multiplier,5f/Multiplier));
        rb.velocity = Vector3.zero;
        TeleFlashIndicator2.Play();
        TeleIndicatorSound2.PlayOneShot(TeleIndicatorSound2.clip);
        SendCustomEventDelayedSeconds(nameof(TeleportingIdle2), 2f/Multiplier);
    }
    public void TeleportingIdle2()
    {
        TeleportIndicator.SetActive(false);
        TeleportIndicator.transform.parent = transform;
        transform.position = TeleportIndicator.transform.position;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f/Multiplier);
    }

    void OnDestroy(){Destroy(TeleportIndicator);}
}

