﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class EventEnder : UdonSharpBehaviour
{
    public bool IsFinished;
    public int EnderType; //0 is Portal, 1 is GameObject Remaining
    public GameObject FinishedObject;

    public PortalSystem[] PortalSystems;
    public int EnemiesRemain;

    public GameObject[] RemainingObjects;
    public int RemainingObjectsCount;

    public GameObject[] MainEventObject;
    public float DisableDelay = 20;

    void Start(){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}

    void OnEnable(){
        IsFinished = false;
        FinishedObject.SetActive(false);
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);
    }

    public void UpdateCustom(){
        if(!IsFinished){
            if(EnderType == 0){
                EnemiesRemain = 0;
                for (int i = 0; i < PortalSystems.Length; i++){EnemiesRemain += PortalSystems[i].EnemySlotLeft;}

                if(EnemiesRemain > 0){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}
                else{
                    IsFinished = true; 
                    FinishedObject.SetActive(true);
                    SendCustomEventDelayedSeconds(nameof(DeactivateMainEventObjects), DisableDelay);
                }
            }
            else if(EnderType == 1){
                RemainingObjectsCount = 0;

                for (int i = 0; i < RemainingObjects.Length; i++){if(RemainingObjects[i] != null && RemainingObjects[i].activeSelf){RemainingObjectsCount++;}}

                if(RemainingObjectsCount > 0){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 2f);}
                else{
                    IsFinished = true; 
                    FinishedObject.SetActive(true);
                    SendCustomEventDelayedSeconds(nameof(DeactivateMainEventObjects), DisableDelay); 
                }
            }
        }
    }
    public void DeactivateMainEventObjects(){
        if(MainEventObject == null) return;
        for (int i = 0; i < MainEventObject.Length; i++){MainEventObject[i].SetActive(false);}
    }
}
