﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

public class PopupSystemOutput : UdonSharpBehaviour
{
    public TextMeshPro[] Text;
    public GameObject PopupMain;

    public void ShowPopUp(string PopupMessage)
    {
        PopupMain.SetActive(true);
        Text[0].text = PopupMessage;
        SendCustomEventDelayedSeconds(nameof(ClosePopUp), 40f);
    }

    public void ClosePopUp()
    {
        PopupMain.SetActive(false);
    }
}
