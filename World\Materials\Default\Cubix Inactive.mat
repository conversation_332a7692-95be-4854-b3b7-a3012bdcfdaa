%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Cubix Inactive
  m_Shader: {fileID: 4800000, guid: bb513f8647422524c856788e63fd07c9, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ALPHATEST_ON
  - _SMOOTHNESS_TEXTURE_ALBEDO_CHANNEL_A
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 1ee21b630ac62184ab8c8a4e70c3948c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - PixelSnap: 0
    - _AlphaCutoff: 0.5
    - _BumpScale: 1
    - _Cutoff: 0.267
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EnableExternalAlpha: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _HasLighting: 0
    - _InvFade: 1
    - _Metallic: 0
    - _Mode: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 1
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _UseLighting: 1
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 0.3018868, g: 0.3018868, b: 0.3018868, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _ScreenPos: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _ScreenSize: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _TintColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
  m_BuildTextureStacks: []
