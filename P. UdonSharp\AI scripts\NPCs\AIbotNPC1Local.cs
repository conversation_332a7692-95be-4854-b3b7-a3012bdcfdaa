﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.AI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class AIbotNPC1Local : UdonSharpBehaviour
{
    public NavMeshAgent agent;
    public float sightRange = 10f;
    public LayerMask whatIsPlayer;
    public GameObject BlackHoleMagnetObject;
    private BlackHoleMagnet BlackHoleMagnetScipt;

    public int State;
    private float speed;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        BlackHoleMagnetObject = GameObject.Find("BlackHole Magnet");
        BlackHoleMagnetScipt = BlackHoleMagnetObject.GetComponent<BlackHoleMagnet>();
        localPlayer = Networking.LocalPlayer;

        agent.enabled = true;
        if (!agent.isOnNavMesh){Destroy(gameObject);}

        SetRandomDestination();
        SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.2f);
    }

    public void UpdateDestination()
    {
    
        bool playerInSightRange = Physics.CheckSphere(transform.position, sightRange, whatIsPlayer);

        if (playerInSightRange && State == 0) { speed = 8f; }
        else if (!playerInSightRange && State == 0) { speed = 3f; }
        else if (State == 1) { speed = 10f; }

        agent.speed = speed;

        // Update destination if reached
        if (agent.remainingDistance < 1f) { SetRandomDestination(); }

        if(BlackHoleMagnetObject != null && BlackHoleMagnetScipt.IsBeingPickedUpByLocal == true){State = 1;}
        else{State = 0;}

        // Adjust update timing dynamically
        if(playerInSightRange && State == 0){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.2f);
        }
        else if (!playerInSightRange && State == 0){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
        }
        else if (State == 1){
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
        }
    }

    void SetRandomDestination()
    {
        if (!agent.isOnNavMesh) return;

        Vector3 targetPosition = (State == 1) ? BlackHoleMagnetObject.transform.position : Random.insideUnitSphere * 100f + transform.position;
        agent.SetDestination(targetPosition);
    }

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

        Vector3 directionToPlayer = localPlayer.GetPosition() - transform.position;
        localPlayer.SetVelocity(-directionToPlayer.normalized * 50);
    }
}
