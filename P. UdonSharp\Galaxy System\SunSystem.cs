﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SunSystem : UdonSharpBehaviour
{
    public System.DateTime CurrentTime;
    public GameObject AnimObject;
    public Transform[] Positions;
    [Range(0f, 1f)]
    public float progress; // normalized float from 0 to 1

    public int segmentCount;
    public float segmentLength;

    void Start()
    {
        segmentCount = Positions.Length - 1;
        segmentLength = 1f / segmentCount;
        UpdateTimer();
    }

    public void UpdateTimer()
    {
        System.DateTime CurrentTime = Networking.GetNetworkDateTime().ToUniversalTime();

        // Get the time of day in seconds
        float secondsSinceMidnight = (float)CurrentTime.TimeOfDay.TotalSeconds;
        float totalSecondsInDay = 24f * 60f * 60f;

        // Calculate normalized progress based on current time of day
        progress = Mathf.Clamp01(secondsSinceMidnight / totalSecondsInDay);

        // Determine which segment we're in
        int currentIndex = Mathf.FloorToInt(progress / segmentLength);
        if (currentIndex >= segmentCount) currentIndex = segmentCount - 1;

        float segmentStart = currentIndex * segmentLength;
        float segmentProgress = (progress - segmentStart) / segmentLength;

        // Get transforms to interpolate between
        Transform from = Positions[currentIndex];
        Transform to = Positions[currentIndex + 1];

        // Linearly interpolate position and rotation
        AnimObject.transform.position = Vector3.Lerp(from.position, to.position, segmentProgress);

        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);
    }
}
