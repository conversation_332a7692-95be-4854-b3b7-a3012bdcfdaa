using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Linq;

#if UNITY_EDITOR
public class ToolAudioShower : EditorWindow
{
    private Vector2 scrollPosition;
    private List<Object> audioObjects = new List<Object>();
    private List<string> objectTypes = new List<string>();
    private List<string> audioInfo = new List<string>();
    private bool[] selectedObjects;
    private bool selectAll = false;
    private int audioCount = 0;
    private bool showHelp = false;
    private bool showStats = false;
    private bool showAudioSources = true;
    private bool showAudioListeners = true;
    private bool showAudioReverbZones = true;
    private bool showAudioLowPassFilters = true;
    private bool showAudioHighPassFilters = true;
    private bool showAudioEchoFilters = true;
    private bool showAudioDistortionFilters = true;
    private bool showAudioReverbFilters = true;
    private bool showAudioChorusFilters = true;
    private bool includeInactive = false;
    private string searchFilter = "";

    // Filter options
    private bool filterByPlayingOnly = false;
    private bool filterByMutedOnly = false;
    private bool filterBy3DOnly = false;
    private bool filterBy2DOnly = false;

    // Statistics
    private int totalAudioSources = 0;
    private int totalAudioListeners = 0;
    private int totalAudioFilters = 0;
    private int playingAudioSources = 0;
    private int mutedAudioSources = 0;
    private int audio3DSources = 0;
    private int audio2DSources = 0;

    [MenuItem("Kon Tools/Audio Component Search Tool")]
    public static void ShowWindow()
    {
        GetWindow<ToolAudioShower>("Audio Component Search Tool");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("Audio Component Search Tool", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Find Audio Components", GUILayout.Height(30)))
        {
            FindAudioComponents();
        }

        if (audioObjects.Count > 0 && GUILayout.Button("Select All Found Objects", GUILayout.Height(30)))
        {
            SelectAllFoundObjects();
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        showHelp = EditorGUILayout.Foldout(showHelp, "Help");
        if (showHelp)
        {
            EditorGUILayout.HelpBox(
                "This tool helps you find and manage Audio components in your scene.\n\n" +
                "1. Click 'Find Audio Components' to scan the scene\n" +
                "2. Use filters to show specific audio component types\n" +
                "3. Use individual filters to check playing, muted, 3D, or 2D audio sources\n" +
                "4. Click on object names to select them in the hierarchy\n" +
                "5. Use 'Select All Found Objects' to select all filtered results\n\n" +
                "Filters:\n" +
                "- Show Audio Sources: Shows objects with AudioSource components\n" +
                "- Show Audio Listeners: Shows objects with AudioListener components\n" +
                "- Show Audio Filters: Shows objects with various audio filter components\n" +
                "- Playing Only: Shows only AudioSources that are currently playing\n" +
                "- Muted Only: Shows only AudioSources that are muted\n" +
                "- 3D Only: Shows only AudioSources with 3D spatial blend\n" +
                "- 2D Only: Shows only AudioSources with 2D spatial blend",
                MessageType.Info);
        }

        showStats = EditorGUILayout.Foldout(showStats, "Statistics");
        if (showStats)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Scene Statistics", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Total Audio Sources: {totalAudioSources}");
            EditorGUILayout.LabelField($"Total Audio Listeners: {totalAudioListeners}");
            EditorGUILayout.LabelField($"Total Audio Filters: {totalAudioFilters}");
            EditorGUILayout.LabelField($"Playing Audio Sources: {playingAudioSources}");
            EditorGUILayout.LabelField($"Muted Audio Sources: {mutedAudioSources}");
            EditorGUILayout.LabelField($"3D Audio Sources: {audio3DSources}");
            EditorGUILayout.LabelField($"2D Audio Sources: {audio2DSources}");
            EditorGUILayout.LabelField($"Currently Showing: {audioCount} objects");
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Filter options
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Component Filter Options", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        showAudioSources = EditorGUILayout.ToggleLeft("Audio Sources", showAudioSources, GUILayout.Width(120));
        showAudioListeners = EditorGUILayout.ToggleLeft("Audio Listeners", showAudioListeners, GUILayout.Width(120));
        showAudioReverbZones = EditorGUILayout.ToggleLeft("Reverb Zones", showAudioReverbZones, GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        showAudioLowPassFilters = EditorGUILayout.ToggleLeft("Low Pass Filters", showAudioLowPassFilters, GUILayout.Width(120));
        showAudioHighPassFilters = EditorGUILayout.ToggleLeft("High Pass Filters", showAudioHighPassFilters, GUILayout.Width(120));
        showAudioEchoFilters = EditorGUILayout.ToggleLeft("Echo Filters", showAudioEchoFilters, GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        showAudioDistortionFilters = EditorGUILayout.ToggleLeft("Distortion Filters", showAudioDistortionFilters, GUILayout.Width(120));
        showAudioReverbFilters = EditorGUILayout.ToggleLeft("Reverb Filters", showAudioReverbFilters, GUILayout.Width(120));
        showAudioChorusFilters = EditorGUILayout.ToggleLeft("Chorus Filters", showAudioChorusFilters, GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();

        includeInactive = EditorGUILayout.ToggleLeft("Include Inactive Objects", includeInactive);

        EditorGUILayout.LabelField("AudioSource Filter Options", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();
        filterByPlayingOnly = EditorGUILayout.ToggleLeft("Playing Only", filterByPlayingOnly, GUILayout.Width(120));
        filterByMutedOnly = EditorGUILayout.ToggleLeft("Muted Only", filterByMutedOnly, GUILayout.Width(120));
        filterBy3DOnly = EditorGUILayout.ToggleLeft("3D Only", filterBy3DOnly, GUILayout.Width(120));
        filterBy2DOnly = EditorGUILayout.ToggleLeft("2D Only", filterBy2DOnly, GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();

        searchFilter = EditorGUILayout.TextField("Search by Name", searchFilter);

        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();

        if (audioObjects.Count > 0)
        {
            EditorGUILayout.LabelField($"Found {audioCount} objects", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            bool newSelectAll = EditorGUILayout.Toggle("Select All", selectAll);
            if (newSelectAll != selectAll)
            {
                selectAll = newSelectAll;
                for (int i = 0; i < selectedObjects.Length; i++)
                {
                    selectedObjects[i] = selectAll;
                }
            }

            if (GUILayout.Button("Focus Selected in Scene"))
            {
                FocusSelectedObjects();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            int displayedCount = 0;
            for (int i = 0; i < audioObjects.Count; i++)
            {
                if (audioObjects[i] != null)
                {
                    GameObject obj = GetGameObjectFromComponent(audioObjects[i]);
                    if (obj == null) continue;

                    // Apply filters
                    if (!ShouldDisplayObject(obj))
                        continue;

                    // Skip inactive objects if not including them
                    if (!includeInactive && !obj.activeInHierarchy)
                        continue;

                    // Apply name filter
                    if (!string.IsNullOrEmpty(searchFilter) && !obj.name.ToLower().Contains(searchFilter.ToLower()))
                        continue;

                    displayedCount++;

                    EditorGUILayout.BeginHorizontal();
                    selectedObjects[i] = EditorGUILayout.Toggle(selectedObjects[i], GUILayout.Width(20));

                    if (GUILayout.Button(obj.name, EditorStyles.label))
                    {
                        Selection.activeGameObject = obj;
                        EditorGUIUtility.PingObject(obj);
                    }

                    EditorGUILayout.LabelField(objectTypes[i], GUILayout.Width(150));

                    // Show audio info
                    if (i < audioInfo.Count)
                    {
                        string info = audioInfo[i];
                        if (info.Contains("Playing"))
                            GUI.color = Color.green;
                        else if (info.Contains("Muted"))
                            GUI.color = Color.red;
                        else if (info.Contains("3D"))
                            GUI.color = Color.cyan;
                        else if (info.Contains("2D"))
                            GUI.color = Color.yellow;
                        else
                            GUI.color = Color.white;

                        EditorGUILayout.LabelField(info, GUILayout.Width(120));
                        GUI.color = Color.white;
                    }

                    EditorGUILayout.EndHorizontal();
                }
            }

            // Update the displayed count
            audioCount = displayedCount;

            EditorGUILayout.EndScrollView();
        }
    }

    private void FindAudioComponents()
    {
        audioObjects.Clear();
        objectTypes.Clear();
        audioInfo.Clear();

        // Reset statistics
        totalAudioSources = 0;
        totalAudioListeners = 0;
        totalAudioFilters = 0;
        playingAudioSources = 0;
        mutedAudioSources = 0;
        audio3DSources = 0;
        audio2DSources = 0;

        // Find all audio components
        AudioSource[] audioSources = GameObject.FindObjectsOfType<AudioSource>(includeInactive);
        AudioListener[] audioListeners = GameObject.FindObjectsOfType<AudioListener>(includeInactive);
        AudioReverbZone[] reverbZones = GameObject.FindObjectsOfType<AudioReverbZone>(includeInactive);
        AudioLowPassFilter[] lowPassFilters = GameObject.FindObjectsOfType<AudioLowPassFilter>(includeInactive);
        AudioHighPassFilter[] highPassFilters = GameObject.FindObjectsOfType<AudioHighPassFilter>(includeInactive);
        AudioEchoFilter[] echoFilters = GameObject.FindObjectsOfType<AudioEchoFilter>(includeInactive);
        AudioDistortionFilter[] distortionFilters = GameObject.FindObjectsOfType<AudioDistortionFilter>(includeInactive);
        AudioReverbFilter[] reverbFilters = GameObject.FindObjectsOfType<AudioReverbFilter>(includeInactive);
        AudioChorusFilter[] chorusFilters = GameObject.FindObjectsOfType<AudioChorusFilter>(includeInactive);

        // Process AudioSource objects
        foreach (AudioSource audioSource in audioSources)
        {
            GameObject obj = audioSource.gameObject;
            audioObjects.Add(obj);
            objectTypes.Add("AudioSource");
            audioInfo.Add(GetAudioSourceInfo(audioSource));
            totalAudioSources++;

            if (audioSource.isPlaying) playingAudioSources++;
            if (audioSource.mute) mutedAudioSources++;
            if (audioSource.spatialBlend >= 0.5f) audio3DSources++;
            else audio2DSources++;
        }

        // Process AudioListener objects
        foreach (AudioListener audioListener in audioListeners)
        {
            GameObject obj = audioListener.gameObject;
            if (!audioObjects.Contains(obj))
            {
                audioObjects.Add(obj);
                objectTypes.Add("AudioListener");
                audioInfo.Add(audioListener.enabled ? "Enabled" : "Disabled");
                totalAudioListeners++;
            }
        }

        // Process AudioReverbZone objects
        foreach (AudioReverbZone reverbZone in reverbZones)
        {
            GameObject obj = reverbZone.gameObject;
            if (!audioObjects.Contains(obj))
            {
                audioObjects.Add(obj);
                objectTypes.Add("AudioReverbZone");
                audioInfo.Add($"Room: {reverbZone.room}");
                totalAudioFilters++;
            }
        }

        // Process Audio Filter objects
        ProcessAudioFilters(lowPassFilters, "AudioLowPassFilter");
        ProcessAudioFilters(highPassFilters, "AudioHighPassFilter");
        ProcessAudioFilters(echoFilters, "AudioEchoFilter");
        ProcessAudioFilters(distortionFilters, "AudioDistortionFilter");
        ProcessAudioFilters(reverbFilters, "AudioReverbFilter");
        ProcessAudioFilters(chorusFilters, "AudioChorusFilter");

        audioCount = audioObjects.Count;
        selectedObjects = new bool[audioCount];

        Debug.Log($"Found {totalAudioSources} AudioSources, {totalAudioListeners} AudioListeners, and {totalAudioFilters} Audio Filters in the scene.");
    }

    private void ProcessAudioFilters<T>(T[] filters, string typeName) where T : Behaviour
    {
        foreach (T filter in filters)
        {
            GameObject obj = filter.gameObject;
            if (!audioObjects.Contains(obj))
            {
                audioObjects.Add(obj);
                objectTypes.Add(typeName);
                audioInfo.Add(filter.enabled ? "Enabled" : "Disabled");
                totalAudioFilters++;
            }
        }
    }

    private string GetAudioSourceInfo(AudioSource audioSource)
    {
        List<string> info = new List<string>();

        if (audioSource.isPlaying) info.Add("Playing");
        if (audioSource.mute) info.Add("Muted");

        if (audioSource.spatialBlend >= 0.5f) info.Add("3D");
        else info.Add("2D");

        if (audioSource.clip != null) info.Add($"Clip: {audioSource.clip.name}");
        else info.Add("No Clip");

        info.Add($"Vol: {audioSource.volume:F2}");

        return string.Join(", ", info);
    }

    private bool ShouldDisplayObject(GameObject obj)
    {
        AudioSource audioSource = obj.GetComponent<AudioSource>();
        AudioListener audioListener = obj.GetComponent<AudioListener>();
        AudioReverbZone reverbZone = obj.GetComponent<AudioReverbZone>();

        // Check component type filters
        bool hasAudioSource = audioSource != null;
        bool hasAudioListener = audioListener != null;
        bool hasReverbZone = reverbZone != null;
        bool hasAudioFilters = HasAudioFilters(obj);

        if (!showAudioSources && hasAudioSource && !hasAudioListener && !hasReverbZone && !hasAudioFilters)
            return false;
        if (!showAudioListeners && hasAudioListener && !hasAudioSource && !hasReverbZone && !hasAudioFilters)
            return false;
        if (!showAudioReverbZones && hasReverbZone && !hasAudioSource && !hasAudioListener && !hasAudioFilters)
            return false;

        // AudioSource specific filters
        if (audioSource != null)
        {
            if (filterByPlayingOnly && !audioSource.isPlaying)
                return false;
            if (filterByMutedOnly && !audioSource.mute)
                return false;
            if (filterBy3DOnly && audioSource.spatialBlend < 0.5f)
                return false;
            if (filterBy2DOnly && audioSource.spatialBlend >= 0.5f)
                return false;
        }

        return true;
    }

    private bool HasAudioFilters(GameObject obj)
    {
        return obj.GetComponent<AudioLowPassFilter>() != null ||
               obj.GetComponent<AudioHighPassFilter>() != null ||
               obj.GetComponent<AudioEchoFilter>() != null ||
               obj.GetComponent<AudioDistortionFilter>() != null ||
               obj.GetComponent<AudioReverbFilter>() != null ||
               obj.GetComponent<AudioChorusFilter>() != null;
    }

    private GameObject GetGameObjectFromComponent(Object obj)
    {
        if (obj is GameObject)
            return (GameObject)obj;
        else if (obj is Component)
            return ((Component)obj).gameObject;
        return null;
    }

    private void SelectAllFoundObjects()
    {
        List<GameObject> objectsToSelect = new List<GameObject>();

        for (int i = 0; i < audioObjects.Count; i++)
        {
            if (audioObjects[i] != null)
            {
                GameObject obj = GetGameObjectFromComponent(audioObjects[i]);
                if (obj != null)
                {
                    // Apply the same filters as in the display
                    if (!ShouldDisplayObject(obj))
                        continue;

                    if (!includeInactive && !obj.activeInHierarchy)
                        continue;

                    if (!string.IsNullOrEmpty(searchFilter) && !obj.name.ToLower().Contains(searchFilter.ToLower()))
                        continue;

                    objectsToSelect.Add(obj);
                }
            }
        }

        if (objectsToSelect.Count > 0)
        {
            Selection.objects = objectsToSelect.ToArray();
            Debug.Log($"Selected {objectsToSelect.Count} audio objects in the hierarchy.");
        }
        else
        {
            Debug.Log("No audio objects match the current filters.");
        }
    }

    private void FocusSelectedObjects()
    {
        List<GameObject> objectsToFocus = new List<GameObject>();

        for (int i = 0; i < audioObjects.Count; i++)
        {
            if (selectedObjects[i] && audioObjects[i] != null)
            {
                GameObject obj = GetGameObjectFromComponent(audioObjects[i]);
                if (obj != null)
                    objectsToFocus.Add(obj);
            }
        }

        if (objectsToFocus.Count > 0)
        {
            Selection.objects = objectsToFocus.ToArray();
            if (objectsToFocus.Count == 1)
                EditorGUIUtility.PingObject(objectsToFocus[0]);
            Debug.Log($"Focused on {objectsToFocus.Count} selected audio objects.");
        }
        else
        {
            Debug.Log("No audio objects are selected.");
        }
    }

    // Add context menu items for quick access
    [MenuItem("GameObject/Kon Tools/Find Audio Components", false, 21)]
    static void FindAudioComponentsContextMenu()
    {
        var window = GetWindow<ToolAudioShower>("Audio Component Search Tool");
        window.FindAudioComponents();
    }
}
#endif