﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PlayerDamageInput : UdonSharpBehaviour
{
    public int Damage;
    public int DamageType; //0 is normal damage, 1 is drain damage
    public int DamageColor; //0 is red, 1 is green, 2 is blue, 3 is heal, 4 is MovementIncrease, 5 is Fire
    public bool IsFriendlyFireDamage; //false is no, true is yes
    public bool IsHealing; //false is no, true is yes
    public bool IsPrisonWeapon; //false is no, true is yes
}
