%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &298764092122440546
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1094651692091364414}
  m_Layer: 13
  m_Name: Gun Grip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1094651692091364414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 298764092122440546}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0.069, y: -0.1994, z: 0.0027}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 708098044211706455}
  m_LocalEulerAnglesHint: {x: -90, y: -90, z: 0}
--- !u!1 &1714093535250661799
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6476192339473735899}
  - component: {fileID: 654361212723617650}
  - component: {fileID: 1036461198033723416}
  - component: {fileID: 8353347368050235628}
  - component: {fileID: 4689946361967752964}
  - component: {fileID: 3685874493723211426}
  - component: {fileID: 4334374498687437619}
  m_Layer: 11
  m_Name: PvPIndicator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6476192339473735899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 708098044211706455}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &654361212723617650
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.5432739, z: 0.05}
  m_Center: {x: 0.07, y: 0, z: 0}
--- !u!114 &1036461198033723416
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f0786780022172c4b87de464c57c5ed2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  _udonSharpBackingUdonBehaviour: {fileID: 8353347368050235628}
  DamageType: 2
  OptionalHurtParticle: {fileID: 3685874493723211426}
  OptionalHurtAudio: {fileID: 4334374498687437619}
  OptionalHurtAudioClip: {fileID: 8300000, guid: c10b817885ef8b643bdb8a78bdc9e1ba,
    type: 3}
--- !u!114 &8353347368050235628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45115577ef41a5b4ca741ed302693907, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactTextPlacement: {fileID: 0}
  interactText: Use
  interactTextGO: {fileID: 0}
  proximity: 2
  SynchronizePosition: 0
  AllowCollisionOwnershipTransfer: 0
  Reliable: 0
  _syncMethod: 1
  serializedProgramAsset: {fileID: 11400000, guid: ad17433569365ee47b5b11a8bde1e35d,
    type: 2}
  programSource: {fileID: 11400000, guid: b96123e7875c717478ad8fa8e708ae71, type: 2}
  serializedPublicVariablesBytesString: Ai8AAAAAATIAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAFQAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AAAAAAAYBAAAAAAAAACcBBAAAAHQAeQBwAGUAAWgAAABTAHkAcwB0AGUAbQAuAEMAbwBsAGwAZQBjAHQAaQBvAG4AcwAuAEcAZQBuAGUAcgBpAGMALgBMAGkAcwB0AGAAMQBbAFsAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4ALgBJAG4AdABlAHIAZgBhAGMAZQBzAC4ASQBVAGQAbwBuAFYAYQByAGkAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AXQBdACwAIABtAHMAYwBvAHIAbABpAGIAAQEJAAAAVgBhAHIAaQBhAGIAbABlAHMALwEAAAABaAAAAFMAeQBzAHQAZQBtAC4AQwBvAGwAbABlAGMAdABpAG8AbgBzAC4ARwBlAG4AZQByAGkAYwAuAEwAaQBzAHQAYAAxAFsAWwBWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAEkAbgB0AGUAcgBmAGEAYwBlAHMALgBJAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlACwAIABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgBdAF0ALAAgAG0AcwBjAG8AcgBsAGkAYgABAAAABgEAAAAAAAAAAi8CAAAAAUkAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAGAAMQBbAFsAUwB5AHMAdABlAG0ALgBJAG4AdAAzADIALAAgAG0AcwBjAG8AcgBsAGkAYgBdAF0ALAAgAFYAUgBDAC4AVQBkAG8AbgAuAEMAbwBtAG0AbwBuAAIAAAAGAgAAAAAAAAAnAQQAAAB0AHkAcABlAAEXAAAAUwB5AHMAdABlAG0ALgBTAHQAcgBpAG4AZwAsACAAbQBzAGMAbwByAGwAaQBiACcBCgAAAFMAeQBtAGIAbwBsAE4AYQBtAGUAAR8AAABfAF8AXwBVAGQAbwBuAFMAaABhAHIAcABCAGUAaABhAHYAaQBvAHUAcgBWAGUAcgBzAGkAbwBuAF8AXwBfACcBBAAAAHQAeQBwAGUAARYAAABTAHkAcwB0AGUAbQAuAEkAbgB0ADMAMgAsACAAbQBzAGMAbwByAGwAaQBiABcBBQAAAFYAYQBsAHUAZQACAAAABwUHBQcF
  publicVariablesUnityEngineObjects: []
  publicVariablesSerializationDataFormat: 0
--- !u!199 &4689946361967752964
ParticleSystemRenderer:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8c013f5280b2ac24f89a80d6108a5bf3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_RenderMode: 0
  m_MeshDistribution: 0
  m_SortMode: 0
  m_MinParticleSize: 0
  m_MaxParticleSize: 0.5
  m_CameraVelocityScale: 0
  m_VelocityScale: 0
  m_LengthScale: 2
  m_SortingFudge: 0
  m_NormalDirection: 1
  m_ShadowBias: 0
  m_RenderAlignment: 0
  m_Pivot: {x: 0, y: 0, z: 0}
  m_Flip: {x: 0, y: 0, z: 0}
  m_EnableGPUInstancing: 1
  m_ApplyActiveColorSpace: 1
  m_AllowRoll: 1
  m_FreeformStretching: 0
  m_RotateWithStretchDirection: 1
  m_UseCustomVertexStreams: 0
  m_VertexStreams: 00010304
  m_UseCustomTrailVertexStreams: 0
  m_TrailVertexStreams: 00010304
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
  m_Mesh1: {fileID: 0}
  m_Mesh2: {fileID: 0}
  m_Mesh3: {fileID: 0}
  m_MeshWeighting: 1
  m_MeshWeighting1: 1
  m_MeshWeighting2: 1
  m_MeshWeighting3: 1
  m_MaskInteraction: 0
--- !u!198 &3685874493723211426
ParticleSystem:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  serializedVersion: 8
  lengthInSec: 1
  simulationSpeed: 1
  stopAction: 0
  cullingMode: 0
  ringBufferMode: 0
  ringBufferLoopRange: {x: 0, y: 1}
  emitterVelocityMode: 1
  looping: 0
  prewarm: 0
  playOnAwake: 0
  useUnscaledTime: 0
  autoRandomSeed: 1
  startDelay:
    serializedVersion: 2
    minMaxState: 0
    scalar: 0
    minScalar: 0
    maxCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    minCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  moveWithTransform: 1
  moveWithCustomTransform: {fileID: 0}
  scalingMode: 0
  randomSeed: 0
  InitialModule:
    serializedVersion: 3
    enabled: 1
    startLifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.1
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startColor:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    startSize:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.05
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotation:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    randomizeRotationDirection: 0
    gravitySource: 0
    maxNumParticles: 150
    customEmitterVelocity: {x: 0, y: 0, z: 0}
    size3D: 0
    rotation3D: 0
    gravityModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  ShapeModule:
    serializedVersion: 6
    enabled: 1
    type: 0
    angle: 25
    length: 5
    boxThickness: {x: 0, y: 0, z: 0}
    radiusThickness: 0.1
    donutRadius: 0.2
    m_Position: {x: 0.06, y: 0.09, z: 0}
    m_Rotation: {x: 0, y: 0, z: 0}
    m_Scale: {x: 1, y: 1, z: 1}
    placementMode: 0
    m_MeshMaterialIndex: 0
    m_MeshNormalOffset: 0
    m_MeshSpawn:
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    m_Mesh: {fileID: 0}
    m_MeshRenderer: {fileID: 0}
    m_SkinnedMeshRenderer: {fileID: 0}
    m_Sprite: {fileID: 0}
    m_SpriteRenderer: {fileID: 0}
    m_UseMeshMaterialIndex: 0
    m_UseMeshColors: 1
    alignToDirection: 0
    m_Texture: {fileID: 0}
    m_TextureClipChannel: 3
    m_TextureClipThreshold: 0
    m_TextureUVChannel: 0
    m_TextureColorAffectsParticles: 1
    m_TextureAlphaAffectsParticles: 1
    m_TextureBilinearFiltering: 0
    randomDirectionAmount: 0
    sphericalDirectionAmount: 0
    randomPositionAmount: 0
    radius:
      value: 0.11
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    arc:
      value: 360
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
  EmissionModule:
    enabled: 1
    serializedVersion: 4
    rateOverTime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 10
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rateOverDistance:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_BurstCount: 1
    m_Bursts:
    - serializedVersion: 2
      time: 0
      countCurve:
        serializedVersion: 2
        minMaxState: 0
        scalar: 20
        minScalar: 30
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      cycleCount: 1
      repeatInterval: 0.01
      probability: 1
  SizeModule:
    enabled: 1
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: -2
          outSlope: -2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  RotationModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  ColorModule:
    enabled: 1
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 31804
        atime2: 65535
        atime3: 65535
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 3
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  UVModule:
    serializedVersion: 2
    enabled: 0
    mode: 0
    timeMode: 0
    fps: 30
    frameOverTime:
      serializedVersion: 2
      minMaxState: 1
      scalar: 0.9999
      minScalar: 0.9999
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startFrame:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedRange: {x: 0, y: 1}
    tilesX: 1
    tilesY: 1
    animationType: 0
    rowIndex: 0
    cycles: 1
    uvChannelMask: -1
    rowMode: 1
    sprites:
    - sprite: {fileID: 0}
    flipU: 0
    flipV: 0
  VelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.95
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    radial:
      serializedVersion: 2
      minMaxState: 0
      scalar: -0.88
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
  InheritVelocityModule:
    enabled: 0
    m_Mode: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  LifetimeByEmitterSpeedModule:
    enabled: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: -0.8
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0.2
          inSlope: -0.8
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Range: {x: 0, y: 1}
  ForceModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
    randomizePerFrame: 0
  ExternalForcesModule:
    serializedVersion: 2
    enabled: 0
    multiplierCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    influenceFilter: 0
    influenceMask:
      serializedVersion: 2
      m_Bits: 4294967295
    influenceList: []
  ClampVelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    magnitude:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxis: 0
    inWorldSpace: 0
    multiplyDragByParticleSize: 1
    multiplyDragByParticleVelocity: 1
    dampen: 0
    drag:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  NoiseModule:
    enabled: 0
    strength:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    frequency: 0.5
    damping: 1
    octaves: 1
    octaveMultiplier: 0.5
    octaveScale: 2
    quality: 1
    scrollSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remap:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapY:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapZ:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapEnabled: 0
    positionAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rotationAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    sizeAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  SizeBySpeedModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    range: {x: 0, y: 1}
    separateAxes: 0
  RotationBySpeedModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    range: {x: 0, y: 1}
  ColorBySpeedModule:
    enabled: 0
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    range: {x: 0, y: 1}
  CollisionModule:
    enabled: 1
    serializedVersion: 4
    type: 1
    collisionMode: 0
    colliderForce: 0
    multiplyColliderForceByParticleSize: 0
    multiplyColliderForceByParticleSpeed: 0
    multiplyColliderForceByCollisionAngle: 1
    m_Planes: []
    m_Dampen:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Bounce:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.5
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_EnergyLossOnCollision:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minKillSpeed: 0
    maxKillSpeed: 10000
    radiusScale: 1
    collidesWith:
      serializedVersion: 2
      m_Bits: 2048
    maxCollisionShapes: 256
    quality: 0
    voxelSize: 0.5
    collisionMessages: 0
    collidesWithDynamic: 1
    interiorCollisions: 0
  TriggerModule:
    enabled: 0
    serializedVersion: 2
    inside: 1
    outside: 0
    enter: 0
    exit: 0
    colliderQueryMode: 0
    radiusScale: 1
    primitives: []
  SubModule:
    serializedVersion: 2
    enabled: 0
    subEmitters:
    - serializedVersion: 3
      emitter: {fileID: 0}
      type: 0
      properties: 0
      emitProbability: 1
  LightsModule:
    enabled: 0
    ratio: 0
    light: {fileID: 0}
    randomDistribution: 1
    color: 1
    range: 1
    intensity: 1
    rangeCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    intensityCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    maxLights: 20
  TrailModule:
    enabled: 0
    mode: 0
    ratio: 1
    lifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minVertexDistance: 0.2
    textureMode: 0
    textureScale: {x: 1, y: 1}
    ribbonCount: 1
    shadowBias: 0.5
    worldSpace: 0
    dieWithParticles: 1
    sizeAffectsWidth: 1
    sizeAffectsLifetime: 0
    inheritParticleColor: 1
    generateLightingData: 0
    splitSubEmitterRibbons: 0
    attachRibbonsToTransform: 0
    colorOverLifetime:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    widthOverTrail:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    colorOverTrail:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  CustomDataModule:
    enabled: 0
    mode0: 0
    vectorComponentCount0: 4
    color0:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel0: Color
    vector0_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_0: X
    vector0_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_1: Y
    vector0_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_2: Z
    vector0_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_3: W
    mode1: 0
    vectorComponentCount1: 4
    color1:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel1: Color
    vector1_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_0: X
    vector1_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_1: Y
    vector1_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_2: Z
    vector1_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_3: W
--- !u!82 &4334374498687437619
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714093535250661799}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: c10b817885ef8b643bdb8a78bdc9e1ba, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.2
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 5
  MinDistance: 1
  MaxDistance: 15
  Pan2D: 0
  rolloffMode: 1
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &2836874910360305926
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7367563093699229640}
  - component: {fileID: 6149869216975117790}
  - component: {fileID: 9140600233069738051}
  - component: {fileID: 2140476129692069914}
  m_Layer: 23
  m_Name: DamageInputSource (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7367563093699229640
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2836874910360305926}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 708098044211706455}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6149869216975117790
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2836874910360305926}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b3ec89ed36ebb547bbf8fe258235cf2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  _udonSharpBackingUdonBehaviour: {fileID: 9140600233069738051}
  Damage: 1
  DamageType: 0
  DamageColor: 0
  IsFriendlyFireDamage: 1
  IsHealing: 0
  IsPrisonWeapon: 1
--- !u!114 &9140600233069738051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2836874910360305926}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45115577ef41a5b4ca741ed302693907, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactTextPlacement: {fileID: 0}
  interactText: Use
  interactTextGO: {fileID: 0}
  proximity: 2
  SynchronizePosition: 0
  AllowCollisionOwnershipTransfer: 0
  Reliable: 0
  _syncMethod: 1
  serializedProgramAsset: {fileID: 11400000, guid: c99b81339601ff24a9f9d4279121524a,
    type: 2}
  programSource: {fileID: 11400000, guid: fdb933ad9b7104c42a1eb5417ad7c725, type: 2}
  serializedPublicVariablesBytesString: Ai8AAAAAATIAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAFQAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AAAAAAAYBAAAAAAAAACcBBAAAAHQAeQBwAGUAAWgAAABTAHkAcwB0AGUAbQAuAEMAbwBsAGwAZQBjAHQAaQBvAG4AcwAuAEcAZQBuAGUAcgBpAGMALgBMAGkAcwB0AGAAMQBbAFsAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4ALgBJAG4AdABlAHIAZgBhAGMAZQBzAC4ASQBVAGQAbwBuAFYAYQByAGkAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AXQBdACwAIABtAHMAYwBvAHIAbABpAGIAAQEJAAAAVgBhAHIAaQBhAGIAbABlAHMALwEAAAABaAAAAFMAeQBzAHQAZQBtAC4AQwBvAGwAbABlAGMAdABpAG8AbgBzAC4ARwBlAG4AZQByAGkAYwAuAEwAaQBzAHQAYAAxAFsAWwBWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAEkAbgB0AGUAcgBmAGEAYwBlAHMALgBJAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlACwAIABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgBdAF0ALAAgAG0AcwBjAG8AcgBsAGkAYgABAAAABgEAAAAAAAAAAi8CAAAAAUkAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAGAAMQBbAFsAUwB5AHMAdABlAG0ALgBJAG4AdAAzADIALAAgAG0AcwBjAG8AcgBsAGkAYgBdAF0ALAAgAFYAUgBDAC4AVQBkAG8AbgAuAEMAbwBtAG0AbwBuAAIAAAAGAgAAAAAAAAAnAQQAAAB0AHkAcABlAAEXAAAAUwB5AHMAdABlAG0ALgBTAHQAcgBpAG4AZwAsACAAbQBzAGMAbwByAGwAaQBiACcBCgAAAFMAeQBtAGIAbwBsAE4AYQBtAGUAAR8AAABfAF8AXwBVAGQAbwBuAFMAaABhAHIAcABCAGUAaABhAHYAaQBvAHUAcgBWAGUAcgBzAGkAbwBuAF8AXwBfACcBBAAAAHQAeQBwAGUAARYAAABTAHkAcwB0AGUAbQAuAEkAbgB0ADMAMgAsACAAbQBzAGMAbwByAGwAaQBiABcBBQAAAFYAYQBsAHUAZQACAAAABwUHBQcF
  publicVariablesUnityEngineObjects: []
  publicVariablesSerializationDataFormat: 0
--- !u!65 &2140476129692069914
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2836874910360305926}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.5432739, z: 0.05}
  m_Center: {x: 0.07, y: 0, z: 0}
--- !u!1 &7467639311059526379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 708098044211706455}
  - component: {fileID: 1380069209614905431}
  - component: {fileID: 5228820311652149913}
  - component: {fileID: 1310716941597793860}
  - component: {fileID: 4102158821355279633}
  - component: {fileID: 265917788973944225}
  - component: {fileID: 2402978863349888236}
  - component: {fileID: 8529164691690629047}
  - component: {fileID: 1617607676671781162}
  - component: {fileID: 4920511608281680595}
  - component: {fileID: 5234092477611288778}
  m_Layer: 13
  m_Name: Prison Baton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &708098044211706455
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -453.07, y: 146.75047, z: 2305.01}
  m_LocalScale: {x: 8, y: 8, z: 8}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1094651692091364414}
  - {fileID: 7367563093699229640}
  - {fileID: 686092620287250105}
  - {fileID: 6476192339473735899}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!114 &1380069209614905431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 020000000100000000000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.00081, y: 20.003288}
      m_Offset: {x: -0.00006246567, y: 0.0000011324883}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 050000000400000003000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.999691, y: 20.000814}
      m_Offset: {x: 0.000023126602, y: 0.0000011324883}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 080000000700000006000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.999691, y: 20.000814}
      m_Offset: {x: 0.000023245811, y: 0.0000011324883}
      m_Rotation: 360
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0b0000000a00000009000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.000814, y: 20.00329}
      m_Offset: {x: -0.000062704086, y: 0.0000011920929}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0e0000000d0000000c000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.000814, y: 20.003286}
      m_Offset: {x: -0.000062704086, y: 0.0000011324883}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 11000000100000000f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.999691, y: 20.000814}
      m_Offset: {x: 0.000023245811, y: 0.0000011324883}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 140000001300000012000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.999685, y: 20.000814}
      m_Offset: {x: 0.00002360344, y: 0.0000011324883}
      m_Rotation: 360
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 170000001600000015000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.00081, y: 20.003292}
      m_Offset: {x: -0.00006246567, y: 0.0000011324883}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1b0000001c0000001a0000001c000000180000001a0000001a0000001800000019000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.081366, y: 10.000132}
      m_Offset: {x: -0.0024004579, y: 0.00009367615}
      m_Rotation: 0.01978234
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 20000000210000001f0000001d0000001f000000210000001d0000001e0000001f000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.076958, y: 10.000132}
      m_Offset: {x: -0.0053581, y: 0.00009226054}
      m_Rotation: 0.01978234
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 260000002400000025000000220000002400000026000000220000002300000024000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 24.98661, y: 10.016951}
      m_Offset: {x: -0.3521099, y: -0.000114031136}
      m_Rotation: 358.73358
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2a0000002b00000029000000290000002b00000027000000290000002700000028000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.081358, y: 10.000132}
      m_Offset: {x: -0.0023492575, y: 0.00009203702}
      m_Rotation: 0.01978234
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2e0000002f000000300000002c0000002e000000300000002d0000002e0000002c000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.00332, y: 9.999826}
      m_Offset: {x: 0.00006353855, y: 0.000095784664}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 330000003400000035000000310000003300000035000000330000003100000032000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.998991, y: 9.999826}
      m_Offset: {x: -0.000094652176, y: 0.000096306205}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 38000000390000003a00000036000000380000003a000000380000003600000037000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.998985, y: 9.999826}
      m_Offset: {x: -0.00014793873, y: 0.00009600818}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3f0000003d0000003e0000003f0000003b0000003d0000003d0000003b0000003c000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 24.995033, y: 10.016959}
      m_Offset: {x: 0.14613318, y: -0.000113256276}
      m_Rotation: 358.73282
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 400000004100000042000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.722858, y: 19.585888}
      m_Offset: {x: -2.2737408, y: -0.80516666}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 440000004500000043000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.372803, y: 18.18796}
      m_Offset: {x: -2.2278755, y: -0.72967845}
      m_Rotation: 356.48053
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 460000004700000048000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.723232, y: 16.898436}
      m_Offset: {x: -1.2972475, y: -1.8217226}
      m_Rotation: 358.92334
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4a0000004b00000049000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.373955, y: 16.904772}
      m_Offset: {x: -1.2750297, y: -1.8229339}
      m_Rotation: 355.40796
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4c0000004d0000004e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 18.15806, y: 16.722883}
      m_Offset: {x: 0.89993143, y: -1.4831748}
      m_Rotation: 8.110046
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 510000004f00000050000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.72321, y: 16.898443}
      m_Offset: {x: 0.7039132, y: -1.5047497}
      m_Rotation: 1.0766551
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 530000005400000052000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.372766, y: 18.187965}
      m_Offset: {x: 1.3353021, y: -0.46225816}
      m_Rotation: 3.5194752
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 570000005500000056000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.722828, y: 19.585884}
      m_Offset: {x: 1.4224715, y: -0.50368834}
      m_Rotation: 360
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 590000005a00000058000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.691278, y: 20.004166}
      m_Offset: {x: 1.4157879, y: 0.51856184}
      m_Rotation: 356.47974
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5b0000005c0000005d000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.371897, y: 18.19186}
      m_Offset: {x: 1.3351166, y: 0.4623761}
      m_Rotation: 356.4904
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5f000000600000005e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 17.674484, y: 16.373913}
      m_Offset: {x: 0.8424741, y: 1.4384505}
      m_Rotation: 355.4113
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 610000006200000063000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.377768, y: 16.904388}
      m_Offset: {x: 0.6316333, y: 1.5056083}
      m_Rotation: 355.40057
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 660000006400000065000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.37787, y: 16.904383}
      m_Offset: {x: -1.2750533, y: 1.8228724}
      m_Rotation: 4.5996404
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 680000006900000067000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 17.674524, y: 16.37391}
      m_Offset: {x: -1.2980975, y: 1.7396361}
      m_Rotation: 4.5888104
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6a0000006b0000006c000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.653282, y: 16.723177}
      m_Offset: {x: -2.2545948, y: 0.64955723}
      m_Rotation: 7.0370097
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6d0000006e0000006f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.691244, y: 20.004576}
      m_Offset: {x: -2.2707822, y: 0.8303441}
      m_Rotation: 3.5237007
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d4000000d5000000d6000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e7000000e8000000e9000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: fd000000fe000000ff000000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040100000501000006010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0b0100000c0100000d010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 120100001301000014010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 700000007100000072000000700000007200000073000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 740000007500000076000000740000007600000077000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 78000000790000007a000000780000007a0000007b000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7c0000007d0000007e0000007c0000007e0000007f000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 800000008100000082000000800000008200000083000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 840000008500000086000000840000008600000087000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 88000000890000008a000000880000008a0000008b000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8c0000008d0000008e0000008c0000008e0000008f000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 900000009100000092000000900000009200000093000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 940000009500000096000000940000009600000097000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 98000000990000009a000000980000009a0000009b000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 9c0000009d0000009e0000009c0000009e0000009f000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a0000000a1000000a2000000a0000000a2000000a3000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a4000000a5000000a6000000a4000000a6000000a7000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a8000000a9000000aa000000a8000000aa000000ab000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ac000000ad000000ae000000ac000000ae000000af000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b0000000b1000000b2000000b0000000b2000000b3000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b4000000b5000000b6000000b4000000b6000000b7000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b8000000b9000000ba000000b8000000ba000000bb000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: bc000000bd000000be000000bc000000be000000bf000000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c0000000c1000000c2000000c3000000c1000000c0000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c4000000c5000000c6000000c6000000c5000000ca000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c7000000c8000000c9000000cb000000c8000000c7000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: cc000000cd000000ce000000ce000000cd000000d2000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: cf000000d0000000d1000000d3000000d0000000cf000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d7000000d8000000d9000000dd000000d8000000d7000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: da000000db000000dc000000dc000000db000000de000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: df000000e0000000e1000000e5000000e0000000df000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e2000000e3000000e4000000e4000000e3000000e6000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ea000000eb000000ec000000ec000000eb000000f0000000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ed000000ee000000ef000000f1000000ee000000ed000000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f2000000f3000000f4000000f4000000f3000000f8000000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f5000000f6000000f7000000f9000000f6000000f5000000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: fa000000fb000000fc000000fc000000fb00000000010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 010100000201000003010000070100000201000001010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08010000090100000a0100000a010000090100000e010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0f010000100100001101000015010000100100000f010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7a0100007b0100007c010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8d0100008e0100008f010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a3010000a4010000a5010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: aa010000ab010000ac010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b1010000b2010000b3010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b8010000b9010000ba010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 160100001701000018010000160100001801000019010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1a0100001b0100001c0100001a0100001c0100001d010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1e0100001f010000200100001e0100002001000021010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 220100002301000024010000220100002401000025010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 260100002701000028010000260100002801000029010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2a0100002b0100002c0100002a0100002c0100002d010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2e0100002f010000300100002e0100003001000031010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 320100003301000034010000320100003401000035010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 360100003701000038010000360100003801000039010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3a0100003b0100003c0100003a0100003c0100003d010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3e0100003f010000400100003e0100004001000041010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 420100004301000044010000420100004401000045010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 460100004701000048010000460100004801000049010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4a0100004b0100004c0100004a0100004c0100004d010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4e0100004f010000500100004e0100005001000051010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 520100005301000054010000520100005401000055010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 560100005701000058010000560100005801000059010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5a0100005b0100005c0100005a0100005c0100005d010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5e0100005f010000600100005e0100006001000061010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 620100006301000064010000620100006401000065010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 660100006701000068010000690100006701000066010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6a0100006b0100006c0100006c0100006b01000070010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6d0100006e0100006f010000710100006e0100006d010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 720100007301000074010000740100007301000078010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 750100007601000077010000790100007601000075010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7d0100007e0100007f010000830100007e0100007d010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 800100008101000082010000820100008101000084010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8501000086010000870100008b0100008601000085010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 88010000890100008a0100008a010000890100008c010000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 900100009101000092010000920100009101000096010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 930100009401000095010000970100009401000093010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 98010000990100009a0100009a010000990100009e010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 9b0100009c0100009d0100009f0100009c0100009b010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a0010000a1010000a2010000a2010000a1010000a6010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a7010000a8010000a9010000ad010000a8010000a7010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ae010000af010000b0010000b0010000af010000b4010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b5010000b6010000b7010000bb010000b6010000b5010000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 200200002102000022020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 330200003402000035020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 490200004a0200004b020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 500200005102000052020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 570200005802000059020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5e0200005f02000060020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: bc010000bd010000be010000bc010000be010000bf010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c0010000c1010000c2010000c0010000c2010000c3010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c4010000c5010000c6010000c4010000c6010000c7010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c8010000c9010000ca010000c8010000ca010000cb010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: cc010000cd010000ce010000cc010000ce010000cf010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d0010000d1010000d2010000d0010000d2010000d3010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d4010000d5010000d6010000d4010000d6010000d7010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d8010000d9010000da010000d8010000da010000db010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: dc010000dd010000de010000dc010000de010000df010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e0010000e1010000e2010000e0010000e2010000e3010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e4010000e5010000e6010000e4010000e6010000e7010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e8010000e9010000ea010000e8010000ea010000eb010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ec010000ed010000ee010000ec010000ee010000ef010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f0010000f1010000f2010000f0010000f2010000f3010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f4010000f5010000f6010000f4010000f6010000f7010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f8010000f9010000fa010000f8010000fa010000fb010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: fc010000fd010000fe010000fc010000fe010000ff010000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 000200000102000002020000000200000202000003020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 040200000502000006020000040200000602000007020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 08020000090200000a020000080200000a0200000b020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0c0200000d0200000e0200000f0200000d0200000c020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 100200001102000012020000120200001102000016020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 130200001402000015020000170200001402000013020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 18020000190200001a0200001a020000190200001e020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1b0200001c0200001d0200001f0200001c0200001b020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 230200002402000025020000290200002402000023020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 26020000270200002802000028020000270200002a020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2b0200002c0200002d020000310200002c0200002b020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2e0200002f02000030020000300200002f02000032020000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 36020000370200003802000038020000370200003c020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 390200003a0200003b0200003d0200003a02000039020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3e0200003f02000040020000400200003f02000044020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 410200004202000043020000450200004202000041020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 46020000470200004802000048020000470200004c020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4d0200004e0200004f020000530200004e0200004d020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 54020000550200005602000056020000550200005a020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5b0200005c0200005d020000610200005c0200005b020000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 650200006602000064020000660200006202000064020000640200006202000063020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.571451, y: 26.483625}
      m_Offset: {x: -2.3818834, y: -4.007553}
      m_Rotation: 89.99874
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6a0200006b0200006902000067020000690200006b020000670200006802000069020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.57143, y: 28.19861}
      m_Offset: {x: -2.3819242, y: -1.3948821}
      m_Rotation: 89.99961
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 700200006e0200006f0200006c0200006e020000700200006c0200006d0200006e020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.57886, y: 29.144936}
      m_Offset: {x: -2.3823955, y: 1.7428393}
      m_Rotation: 89.83019
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 740200007502000073020000730200007502000071020000730200007102000072020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.571423, y: 26.504597}
      m_Offset: {x: 2.3818824, y: -4.0587826}
      m_Rotation: 270.00024
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 78020000790200007a02000076020000780200007a020000770200007802000076020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.571432, y: 26.494083}
      m_Offset: {x: 2.3818734, y: -4.0729804}
      m_Rotation: 270
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7d0200007e0200007f0200007b0200007d0200007f0200007d0200007b0200007c020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.571428, y: 28.182457}
      m_Offset: {x: 2.3817668, y: -1.4769547}
      m_Rotation: 269.99933
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 820200008302000084020000800200008202000084020000820200008002000081020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.571432, y: 28.147985}
      m_Offset: {x: 2.3817673, y: 1.7687061}
      m_Rotation: 270.00067
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 890200008702000088020000890200008502000087020000870200008502000086020000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 28.622087, y: 32.71742}
      m_Offset: {x: -2.3850749, y: -5.048512}
      m_Rotation: 88.807785
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8a0200008c0200008d0200008a0200008b0200008c020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 18.77792, y: 11.791089}
      m_Offset: {x: 2.6029768, y: -2.0511677}
      m_Rotation: 44.054638
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 910200008e020000900200008e0200008f02000090020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 17.236467, y: 20.60799}
      m_Offset: {x: 4.15732, y: -3.7003188}
      m_Rotation: 101.803444
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 940200009502000093020000930200009502000092020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.85991, y: 9.654083}
      m_Offset: {x: 2.4567142, y: -1.8038442}
      m_Rotation: 79.8764
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 960200009702000098020000960200009802000099020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 17.295681, y: 10.518721}
      m_Offset: {x: 3.072098, y: -2.0628657}
      m_Rotation: 101.89853
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 9a0200009b0200009c0200009a0200009c0200009d020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 18.42269, y: 11.999196}
      m_Offset: {x: 3.4647377, y: -3.0003881}
      m_Rotation: 100.62567
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a10200009e0200009f020000a0020000a10200009f020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.698988, y: 10.37342}
      m_Offset: {x: 4.3357954, y: -2.5294485}
      m_Rotation: 105.83166
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a2020000a3020000a4020000a2020000a4020000a5020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.220179, y: 10.639161}
      m_Offset: {x: 3.4176693, y: -2.635395}
      m_Rotation: 93.92754
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: a6020000a7020000a8020000a6020000a8020000a9020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.131851, y: 10.97807}
      m_Offset: {x: 4.5770497, y: -2.960487}
      m_Rotation: 97.84482
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ad020000aa020000ab020000ad020000ab020000ac020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.912552, y: 9.123367}
      m_Offset: {x: 4.825879, y: -2.2100646}
      m_Rotation: 131.33609
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: af020000b0020000ae020000b0020000b1020000ae020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.008049, y: 9.177453}
      m_Offset: {x: -0.5600293, y: 1.0471878}
      m_Rotation: 7.465426
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b3020000b4020000b2020000b2020000b4020000b5020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.175316, y: 9.400196}
      m_Offset: {x: 0.29476878, y: 1.4784491}
      m_Rotation: 3.1822927
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: b6020000b7020000b9020000b9020000b7020000b8020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.001448, y: 9.325233}
      m_Offset: {x: 0.029701233, y: 2.2591915}
      m_Rotation: 0.32142544
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: bc020000bd020000ba020000bc020000ba020000bb020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 19.417494, y: 11.820073}
      m_Offset: {x: 1.3758284, y: 1.7359351}
      m_Rotation: 22.021658
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: bf020000c0020000be020000c0020000c1020000be020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.434914, y: 14.756679}
      m_Offset: {x: 0.9956732, y: 1.4046509}
      m_Rotation: 30.290161
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c3020000c5020000c2020000c3020000c4020000c5020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 17.685858, y: 10.408682}
      m_Offset: {x: 0.29913554, y: 2.365184}
      m_Rotation: 34.95366
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: c8020000c9020000c6020000c8020000c6020000c7020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 14.218344, y: 7.077329}
      m_Offset: {x: 1.6963053, y: 2.7367322}
      m_Rotation: 44.418457
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: cc020000cd020000ca020000cc020000ca020000cb020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 20.013922, y: 6.6220794}
      m_Offset: {x: 0.9685941, y: 2.2353086}
      m_Rotation: 22.293045
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ce020000cf020000d1020000cf020000d0020000d1020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.894642, y: 7.5213385}
      m_Offset: {x: 0.6385709, y: 2.8992093}
      m_Rotation: 342.55435
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d5020000d2020000d4020000d4020000d2020000d3020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 22.062176, y: 8.131733}
      m_Offset: {x: 1.6614822, y: 2.959748}
      m_Rotation: 37.469524
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: d9020000d6020000d8020000d6020000d7020000d8020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 25.1019, y: 6.8297677}
      m_Offset: {x: 1.9510771, y: 2.0308654}
      m_Rotation: 1.3929696
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: dc020000dd020000db020000db020000dd020000da020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 13.93738, y: 13.901991}
      m_Offset: {x: 2.2421749, y: 2.4647822}
      m_Rotation: 330.54764
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: de020000e0020000e1020000de020000df020000e0020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 16.837349, y: 12.584461}
      m_Offset: {x: 2.6981754, y: -1.7315519}
      m_Rotation: 34.785084
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e5020000e2020000e4020000e4020000e2020000e3020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 22.0177, y: 12.174783}
      m_Offset: {x: 4.0932183, y: -2.5044475}
      m_Rotation: 107.86
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: e7020000e8020000e9020000e7020000e9020000e6020000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 18.510307, y: 12.067846}
      m_Offset: {x: 2.5699232, y: -1.5272667}
      m_Rotation: 100.38737
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 4e0300004f03000050030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 610300006203000063030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 770300007803000079030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7e0300007f03000080030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 850300008603000087030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8c0300008d0300008e030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ea020000eb020000ec020000ea020000ec020000ed020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: ee020000ef020000f0020000ee020000f0020000f1020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f2020000f3020000f4020000f2020000f4020000f5020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: f6020000f7020000f8020000f6020000f8020000f9020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: fa020000fb020000fc020000fa020000fc020000fd020000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: fe020000ff02000000030000fe0200000003000001030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 020300000303000004030000020300000403000005030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 060300000703000008030000060300000803000009030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0a0300000b0300000c0300000a0300000c0300000d030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 0e0300000f030000100300000e0300001003000011030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 120300001303000014030000120300001403000015030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 160300001703000018030000160300001803000019030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1a0300001b0300001c0300001a0300001c0300001d030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 1e0300001f030000200300001e0300002003000021030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 220300002303000024030000220300002403000025030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 260300002703000028030000260300002803000029030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2a0300002b0300002c0300002a0300002c0300002d030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 2e0300002f030000300300002e0300003003000031030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 320300003303000034030000320300003403000035030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 360300003703000038030000360300003803000039030000
    m_SmoothingGroup: 3
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3a0300003b0300003c0300003d0300003b0300003a030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 3e0300003f03000040030000400300003f03000044030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 410300004203000043030000450300004203000041030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 46030000470300004803000048030000470300004c030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 490300004a0300004b0300004d0300004a03000049030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 510300005203000053030000570300005203000051030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 540300005503000056030000560300005503000058030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 590300005a0300005b0300005f0300005a03000059030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 5c0300005d0300005e0300005e0300005d03000060030000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 64030000650300006603000066030000650300006a030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6703000068030000690300006b0300006803000067030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6c0300006d0300006e0300006e0300006d03000072030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 6f030000700300007103000073030000700300006f030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 74030000750300007603000076030000750300007a030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7b0300007c0300007d030000810300007c0300007b030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 820300008303000084030000840300008303000088030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 890300008a0300008b0300008f0300008a03000089030000
    m_SmoothingGroup: 2
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 1
    m_ManualUV: 1
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 02000000030000001c0000001d000000
  - m_Vertices: 0100000004000000070000000a0000000d000000100000001300000016000000
  - m_Vertices: 0000000017000000180000003f000000
  - m_Vertices: 05000000060000002100000022000000
  - m_Vertices: 08000000090000002600000027000000
  - m_Vertices: 0b0000000c0000002b0000002c000000
  - m_Vertices: 0e0000000f0000003000000031000000
  - m_Vertices: 11000000120000003500000036000000
  - m_Vertices: 14000000150000003a0000003b000000
  - m_Vertices: 1b0000001e0000004300000046000000
  - m_Vertices: 1a0000004200000044000000
  - m_Vertices: 190000003e000000400000006d000000
  - m_Vertices: 2000000023000000490000004c000000
  - m_Vertices: 1f000000480000004a000000
  - m_Vertices: 240000004e00000050000000
  - m_Vertices: 25000000280000004f00000052000000
  - m_Vertices: 2a0000002d0000005500000058000000
  - m_Vertices: 290000005400000056000000
  - m_Vertices: 2e0000005a0000005c000000
  - m_Vertices: 2f000000320000005b0000005e000000
  - m_Vertices: 330000006000000062000000
  - m_Vertices: 34000000370000006100000064000000
  - m_Vertices: 380000006600000068000000
  - m_Vertices: 390000003c000000670000006a000000
  - m_Vertices: 3d0000006c0000006e000000
  - m_Vertices: 4100000045000000470000004b0000004d000000510000005300000057000000590000005d0000005f0000006300000065000000690000006b0000006f000000
  - m_Vertices: d40000007a0000007d000000d2000000
  - m_Vertices: d5000000e8000000c1000000c5000000c8000000cd000000d0000000d8000000db000000e0000000e3000000
  - m_Vertices: d60000007e00000081000000da000000
  - m_Vertices: e70000008e00000091000000e6000000
  - m_Vertices: e90000009200000095000000e5000000
  - m_Vertices: fd0000007f0000008000000003010000
  - m_Vertices: fe000000050100000c01000013010000eb000000ee000000f3000000f6000000fb000000020100000901000010010000
  - m_Vertices: ff0000007b0000007c000000f9000000
  - m_Vertices: 04010000a3000000a400000000010000
  - m_Vertices: 060100009f000000a000000008010000
  - m_Vertices: 0b0100008b0000008c00000011010000
  - m_Vertices: 0d010000870000008800000007010000
  - m_Vertices: 1201000097000000980000000e010000
  - m_Vertices: 14010000930000009400000015010000
  - m_Vertices: 70000000bf000000ed000000
  - m_Vertices: 71000000be000000c6000000
  - m_Vertices: 7200000075000000ca000000cc000000
  - m_Vertices: 7300000074000000f1000000f7000000
  - m_Vertices: 7600000079000000ce000000
  - m_Vertices: 7700000078000000f5000000
  - m_Vertices: 8200000085000000dc000000
  - m_Vertices: 830000008400000001010000
  - m_Vertices: 8600000089000000de000000e2000000
  - m_Vertices: 8a0000008d000000e4000000
  - m_Vertices: 8f000000900000000f010000
  - m_Vertices: 9600000099000000df000000
  - m_Vertices: 9a0000009d000000dd000000e1000000
  - m_Vertices: 9b0000009c0000000a010000
  - m_Vertices: 9e000000a1000000d7000000
  - m_Vertices: a2000000a5000000d3000000d9000000
  - m_Vertices: a6000000a9000000cf000000
  - m_Vertices: a7000000a8000000fc000000
  - m_Vertices: aa000000ad000000cb000000d1000000
  - m_Vertices: ab000000ac000000f8000000fa000000
  - m_Vertices: ae000000b1000000c7000000
  - m_Vertices: af000000b0000000f4000000
  - m_Vertices: b2000000b5000000c3000000c9000000
  - m_Vertices: b3000000b4000000f0000000f2000000
  - m_Vertices: b6000000b9000000c0000000
  - m_Vertices: b7000000b8000000ec000000
  - m_Vertices: ba000000bd000000c2000000c4000000
  - m_Vertices: bb000000bc000000ea000000ef000000
  - m_Vertices: 7a010000200100002301000078010000
  - m_Vertices: 7b0100008e010000670100006b0100006e01000073010000760100007e010000810100008601000089010000
  - m_Vertices: 7c010000240100002701000080010000
  - m_Vertices: 8d01000034010000370100008c010000
  - m_Vertices: 8f010000380100003b0100008b010000
  - m_Vertices: a30100002501000026010000a9010000
  - m_Vertices: a4010000ab010000b2010000b90100009101000094010000990100009c010000a1010000a8010000af010000b6010000
  - m_Vertices: a501000021010000220100009f010000
  - m_Vertices: aa010000490100004a010000a6010000
  - m_Vertices: ac0100004501000046010000ae010000
  - m_Vertices: b10100003101000032010000b7010000
  - m_Vertices: b30100002d0100002e010000ad010000
  - m_Vertices: b80100003d0100003e010000b4010000
  - m_Vertices: ba010000390100003a010000bb010000
  - m_Vertices: 160100006501000093010000
  - m_Vertices: 17010000640100006c010000
  - m_Vertices: 180100001b0100007001000072010000
  - m_Vertices: 190100001a010000970100009d010000
  - m_Vertices: 1c0100001f01000074010000
  - m_Vertices: 1d0100001e0100009b010000
  - m_Vertices: 280100002b01000082010000
  - m_Vertices: 290100002a010000a7010000
  - m_Vertices: 2c0100002f0100008401000088010000
  - m_Vertices: 30010000330100008a010000
  - m_Vertices: 3501000036010000b5010000
  - m_Vertices: 3c0100003f01000085010000
  - m_Vertices: 40010000430100008301000087010000
  - m_Vertices: 4101000042010000b0010000
  - m_Vertices: 44010000470100007d010000
  - m_Vertices: 480100004b010000790100007f010000
  - m_Vertices: 4c0100004f01000075010000
  - m_Vertices: 4d0100004e010000a2010000
  - m_Vertices: 50010000530100007101000077010000
  - m_Vertices: 51010000520100009e010000a0010000
  - m_Vertices: 54010000570100006d010000
  - m_Vertices: 55010000560100009a010000
  - m_Vertices: 580100005b010000690100006f010000
  - m_Vertices: 590100005a0100009601000098010000
  - m_Vertices: 5c0100005f01000066010000
  - m_Vertices: 5d0100005e01000092010000
  - m_Vertices: 6001000063010000680100006a010000
  - m_Vertices: 61010000620100009001000095010000
  - m_Vertices: 20020000c6010000c90100001e020000
  - m_Vertices: 21020000340200000d0200001102000014020000190200001c02000024020000270200002c0200002f020000
  - m_Vertices: 22020000ca010000cd01000026020000
  - m_Vertices: 33020000da010000dd01000032020000
  - m_Vertices: 35020000de010000e101000031020000
  - m_Vertices: 49020000cb010000cc0100004f020000
  - m_Vertices: 4a02000051020000580200005f020000370200003a0200003f02000042020000470200004e020000550200005c020000
  - m_Vertices: 4b020000c7010000c801000045020000
  - m_Vertices: 50020000ef010000f00100004c020000
  - m_Vertices: 52020000eb010000ec01000054020000
  - m_Vertices: 57020000d7010000d80100005d020000
  - m_Vertices: 59020000d3010000d401000053020000
  - m_Vertices: 5e020000e3010000e40100005a020000
  - m_Vertices: 60020000df010000e001000061020000
  - m_Vertices: bc0100000b02000039020000
  - m_Vertices: bd0100000a02000012020000
  - m_Vertices: be010000c10100001602000018020000
  - m_Vertices: bf010000c00100003d02000043020000
  - m_Vertices: c2010000c50100001a020000
  - m_Vertices: c3010000c401000041020000
  - m_Vertices: ce010000d101000028020000
  - m_Vertices: cf010000d00100004d020000
  - m_Vertices: d2010000d50100002a0200002e020000
  - m_Vertices: d6010000d901000030020000
  - m_Vertices: db010000dc0100005b020000
  - m_Vertices: e2010000e50100002b020000
  - m_Vertices: e6010000e9010000290200002d020000
  - m_Vertices: e7010000e801000056020000
  - m_Vertices: ea010000ed01000023020000
  - m_Vertices: ee010000f10100001f02000025020000
  - m_Vertices: f2010000f50100001b020000
  - m_Vertices: f3010000f401000048020000
  - m_Vertices: f6010000f9010000170200001d020000
  - m_Vertices: f7010000f80100004402000046020000
  - m_Vertices: fa010000fd01000013020000
  - m_Vertices: fb010000fc01000040020000
  - m_Vertices: fe010000010200000f02000015020000
  - m_Vertices: ff010000000200003c0200003e020000
  - m_Vertices: 02020000050200000c020000
  - m_Vertices: 030200000402000038020000
  - m_Vertices: 06020000090200000e02000010020000
  - m_Vertices: 0702000008020000360200003b020000
  - m_Vertices: 65020000680200009302000096020000
  - m_Vertices: 6602000067020000
  - m_Vertices: 640200008d02000094020000
  - m_Vertices: 6202000089020000
  - m_Vertices: 63020000880200008a020000e7020000
  - m_Vertices: 6a0200006d0200009f020000a2020000
  - m_Vertices: 6b0200006c020000
  - m_Vertices: 6902000099020000a0020000
  - m_Vertices: 7002000071020000
  - m_Vertices: 6e020000a5020000ac020000
  - m_Vertices: 6f02000072020000ab020000ae020000
  - m_Vertices: 7402000077020000b7020000ba020000
  - m_Vertices: 7502000076020000
  - m_Vertices: 73020000b1020000b8020000
  - m_Vertices: 78020000bd020000c4020000
  - m_Vertices: 790200007c020000c3020000c6020000
  - m_Vertices: 7a0200007b020000
  - m_Vertices: 7d020000c9020000d0020000
  - m_Vertices: 7e02000081020000cf020000d2020000
  - m_Vertices: 7f02000080020000
  - m_Vertices: 82020000d5020000dc020000
  - m_Vertices: 8302000086020000db020000de020000
  - m_Vertices: 8402000085020000
  - m_Vertices: 87020000e1020000e8020000
  - m_Vertices: 8c0200009102000095020000
  - m_Vertices: 8b0200008e020000e4020000e6020000
  - m_Vertices: 9002000092020000970200009a020000
  - m_Vertices: 8f0200009b020000a7020000b3020000bf020000cb020000d7020000e3020000
  - m_Vertices: 980200009d020000a1020000
  - m_Vertices: 9c0200009e020000a3020000a6020000
  - m_Vertices: a4020000a9020000ad020000
  - m_Vertices: a8020000aa020000af020000b2020000
  - m_Vertices: b0020000b5020000b9020000
  - m_Vertices: b4020000b6020000bb020000be020000
  - m_Vertices: bc020000c1020000c5020000
  - m_Vertices: c0020000c2020000c7020000ca020000
  - m_Vertices: c8020000cd020000d1020000
  - m_Vertices: cc020000ce020000d3020000d6020000
  - m_Vertices: d4020000d9020000dd020000
  - m_Vertices: d8020000da020000df020000e2020000
  - m_Vertices: e0020000e5020000e9020000
  - m_Vertices: 4e030000f4020000f70200004c030000
  - m_Vertices: 4f030000620300003b0300003f03000042030000470300004a03000052030000550300005a0300005d030000
  - m_Vertices: 50030000f8020000fb02000054030000
  - m_Vertices: 61030000080300000b03000060030000
  - m_Vertices: 630300000c0300000f0300005f030000
  - m_Vertices: 77030000f9020000fa0200007d030000
  - m_Vertices: 780300007f030000860300008d03000065030000680300006d03000070030000750300007c030000830300008a030000
  - m_Vertices: 79030000f5020000f602000073030000
  - m_Vertices: 7e0300001d0300001e0300007a030000
  - m_Vertices: 80030000190300001a03000082030000
  - m_Vertices: 8503000005030000060300008b030000
  - m_Vertices: 87030000010300000203000081030000
  - m_Vertices: 8c030000110300001203000088030000
  - m_Vertices: 8e0300000d0300000e0300008f030000
  - m_Vertices: ea0200003903000067030000
  - m_Vertices: eb0200003803000040030000
  - m_Vertices: ec020000ef0200004403000046030000
  - m_Vertices: ed020000ee0200006b03000071030000
  - m_Vertices: f0020000f302000048030000
  - m_Vertices: f1020000f20200006f030000
  - m_Vertices: fc020000ff02000056030000
  - m_Vertices: fd020000fe0200007b030000
  - m_Vertices: 0003000003030000580300005c030000
  - m_Vertices: 04030000070300005e030000
  - m_Vertices: 090300000a03000089030000
  - m_Vertices: 100300001303000059030000
  - m_Vertices: 1403000017030000570300005b030000
  - m_Vertices: 150300001603000084030000
  - m_Vertices: 180300001b03000051030000
  - m_Vertices: 1c0300001f0300004d03000053030000
  - m_Vertices: 200300002303000049030000
  - m_Vertices: 210300002203000076030000
  - m_Vertices: 2403000027030000450300004b030000
  - m_Vertices: 25030000260300007203000074030000
  - m_Vertices: 280300002b03000041030000
  - m_Vertices: 290300002a0300006e030000
  - m_Vertices: 2c0300002f0300003d03000043030000
  - m_Vertices: 2d0300002e0300006a0300006c030000
  - m_Vertices: 30030000330300003a030000
  - m_Vertices: 310300003203000066030000
  - m_Vertices: 34030000370300003c0300003e030000
  - m_Vertices: 35030000360300006403000069030000
  m_SharedTextures: []
  m_Positions:
  - {x: 0.10643387, y: -0.27163696, z: 0}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.09764862, y: -0.27163696, z: 0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: 0.021209717}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.07643509, y: -0.27163696, z: 0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: 0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.055221558, y: -0.27163696, z: 0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: 0.021209717}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.04643631, y: -0.27163696, z: 0}
  - {x: 0.04643631, y: -0.27163696, z: 0}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.055221558, y: -0.27163696, z: -0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: -0.021209717}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.07643509, y: -0.27163696, z: -0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: -0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.09764862, y: -0.27163696, z: -0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: -0.021209717}
  - {x: 0.07643509, y: -0.27163696, z: 0}
  - {x: 0.10643387, y: -0.27163696, z: 0}
  - {x: 0.10643387, y: -0.27163696, z: 0}
  - {x: 0.10643387, y: 0.24707031, z: 0}
  - {x: 0.10204315, y: 0.24707031, z: 0.010604858}
  - {x: 0.09764862, y: 0.24707031, z: 0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: 0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: 0.021209717}
  - {x: 0.09764862, y: 0.24707031, z: 0.021209717}
  - {x: 0.08703995, y: 0.24707031, z: 0.025604248}
  - {x: 0.07643509, y: 0.24707031, z: 0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: 0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: 0.02999878}
  - {x: 0.07643509, y: 0.24707031, z: 0.02999878}
  - {x: 0.06583023, y: 0.24707031, z: 0.025604248}
  - {x: 0.055221558, y: 0.24707031, z: 0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: 0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: 0.021209717}
  - {x: 0.055221558, y: 0.24707031, z: 0.021209717}
  - {x: 0.050827026, y: 0.24707031, z: 0.010604858}
  - {x: 0.04643631, y: 0.24707031, z: 0}
  - {x: 0.04643631, y: -0.27163696, z: 0}
  - {x: 0.04643631, y: -0.27163696, z: 0}
  - {x: 0.04643631, y: 0.24707031, z: 0}
  - {x: 0.050827026, y: 0.24707031, z: -0.010604858}
  - {x: 0.055221558, y: 0.24707031, z: -0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: -0.021209717}
  - {x: 0.055221558, y: -0.27163696, z: -0.021209717}
  - {x: 0.055221558, y: 0.24707031, z: -0.021209717}
  - {x: 0.06583023, y: 0.24707031, z: -0.025604248}
  - {x: 0.07643509, y: 0.24707031, z: -0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: -0.02999878}
  - {x: 0.07643509, y: -0.27163696, z: -0.02999878}
  - {x: 0.07643509, y: 0.24707031, z: -0.02999878}
  - {x: 0.08703995, y: 0.24707031, z: -0.025604248}
  - {x: 0.09764862, y: 0.24707031, z: -0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: -0.021209717}
  - {x: 0.09764862, y: -0.27163696, z: -0.021209717}
  - {x: 0.09764862, y: 0.24707031, z: -0.021209717}
  - {x: 0.10204315, y: 0.24707031, z: -0.010604858}
  - {x: 0.10643387, y: 0.24707031, z: 0}
  - {x: 0.10643387, y: -0.27163696, z: 0}
  - {x: 0.10643387, y: 0.24707031, z: 0}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.10204315, y: 0.24707031, z: 0.010604858}
  - {x: 0.09764862, y: 0.24707031, z: 0.021209717}
  - {x: 0.10204315, y: 0.24707031, z: 0.010604858}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.09764862, y: 0.24707031, z: 0.021209717}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.08703995, y: 0.24707031, z: 0.025604248}
  - {x: 0.07643509, y: 0.24707031, z: 0.02999878}
  - {x: 0.08703995, y: 0.24707031, z: 0.025604248}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.07643509, y: 0.24707031, z: 0.02999878}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.06583023, y: 0.24707031, z: 0.025604248}
  - {x: 0.055221558, y: 0.24707031, z: 0.021209717}
  - {x: 0.06583023, y: 0.24707031, z: 0.025604248}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.055221558, y: 0.24707031, z: 0.021209717}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.050827026, y: 0.24707031, z: 0.010604858}
  - {x: 0.04643631, y: 0.24707031, z: 0}
  - {x: 0.050827026, y: 0.24707031, z: 0.010604858}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.04643631, y: 0.24707031, z: 0}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.050827026, y: 0.24707031, z: -0.010604858}
  - {x: 0.055221558, y: 0.24707031, z: -0.021209717}
  - {x: 0.050827026, y: 0.24707031, z: -0.010604858}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.055221558, y: 0.24707031, z: -0.021209717}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.06583023, y: 0.24707031, z: -0.025604248}
  - {x: 0.07643509, y: 0.24707031, z: -0.02999878}
  - {x: 0.06583023, y: 0.24707031, z: -0.025604248}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.07643509, y: 0.24707031, z: -0.02999878}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.08703995, y: 0.24707031, z: -0.025604248}
  - {x: 0.09764862, y: 0.24707031, z: -0.021209717}
  - {x: 0.08703995, y: 0.24707031, z: -0.025604248}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.09764862, y: 0.24707031, z: -0.021209717}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.10204315, y: 0.24707031, z: -0.010604858}
  - {x: 0.10643387, y: 0.24707031, z: 0}
  - {x: 0.10204315, y: 0.24707031, z: -0.010604858}
  - {x: 0.07643509, y: 0.27163696, z: 0}
  - {x: 0.06216812, y: 0.19537354, z: 0.03149414}
  - {x: 0.06216812, y: 0.19537354, z: -0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: -0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: 0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: 0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: -0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: -0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: 0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: 0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: -0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: -0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: 0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: 0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: -0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: -0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: 0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: 0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: -0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: -0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: 0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: 0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: -0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: -0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: 0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: 0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: -0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: -0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: 0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: 0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: -0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: -0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: 0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: 0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: -0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: -0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: 0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: 0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: -0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: -0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: 0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: 0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: -0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: -0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: 0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: 0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: -0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: -0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: 0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: 0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: -0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: -0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: 0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: 0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: -0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: -0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: 0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: 0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: -0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: -0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: 0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: 0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: -0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: -0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: 0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: 0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: -0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: -0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: 0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: 0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: -0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: -0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: 0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: 0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: -0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: -0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: 0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: 0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: -0.03149414}
  - {x: 0.06216812, y: 0.19537354, z: -0.03149414}
  - {x: 0.06216812, y: 0.19537354, z: 0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: -0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: -0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.06216812, y: 0.19537354, z: -0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: -0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: -0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: -0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: -0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: -0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: -0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: -0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: -0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: -0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: -0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: -0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: -0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: -0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: -0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: -0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: -0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: -0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: -0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: -0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.06216812, y: 0.20462036, z: 0.03149414}
  - {x: 0.06216812, y: 0.19537354, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.0614357, y: 0.2000122, z: 0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: 0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: 0.03149414}
  - {x: 0.06430054, y: 0.20883179, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.067619324, y: 0.21212769, z: 0.03149414}
  - {x: 0.067619324, y: 0.18786621, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.06430054, y: 0.19119263, z: 0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: 0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: 0.03149414}
  - {x: 0.07180023, y: 0.21426392, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.07643509, y: 0.21499634, z: 0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.07180023, y: 0.18572998, z: 0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: 0.03149414}
  - {x: 0.08106995, y: 0.18572998, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.07643509, y: 0.18499756, z: 0.03149414}
  - {x: 0.08106995, y: 0.21426392, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: 0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: 0.03149414}
  - {x: 0.085250854, y: 0.21212769, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.08856964, y: 0.20883179, z: 0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.085250854, y: 0.18786621, z: 0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: 0.03149414}
  - {x: 0.09070206, y: 0.19537354, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.08856964, y: 0.19119263, z: 0.03149414}
  - {x: 0.09070206, y: 0.20462036, z: 0.03149414}
  - {x: 0.07643509, y: 0.2000122, z: 0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: 0.03149414}
  - {x: 0.09143448, y: 0.2000122, z: 0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: 0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: -0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: -0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: 0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: 0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: -0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: -0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: 0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: 0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: -0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: -0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: 0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: 0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: -0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: -0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: 0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: 0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: -0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: -0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: 0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: 0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: -0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: -0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: 0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: 0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: -0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: -0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: 0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: 0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: -0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: -0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: 0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: 0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: -0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: -0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: 0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: 0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: -0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: -0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: 0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: 0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: -0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: -0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: 0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: 0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: -0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: -0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: 0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: 0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: -0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: -0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: 0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: 0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: -0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: -0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: 0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: 0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: -0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: -0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: 0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: 0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: -0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: -0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: 0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: 0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: -0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: -0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: 0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: 0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: -0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: -0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: 0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: 0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: -0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: -0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: 0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: 0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: -0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: -0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: 0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: -0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: -0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: -0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: -0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: -0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: -0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: -0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: -0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: -0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: -0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: -0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: -0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: -0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: -0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: -0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: -0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: -0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: -0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: -0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: -0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: -0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: -0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.06216812, y: 0.104644775, z: 0.03149414}
  - {x: 0.06216812, y: 0.09536743, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.0614357, y: 0.1000061, z: 0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: 0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: 0.03149414}
  - {x: 0.06430054, y: 0.10882568, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.067619324, y: 0.11212158, z: 0.03149414}
  - {x: 0.067619324, y: 0.08786011, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.06430054, y: 0.09118652, z: 0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: 0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: 0.03149414}
  - {x: 0.07180023, y: 0.11425781, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.07643509, y: 0.114990234, z: 0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.07180023, y: 0.08572388, z: 0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: 0.03149414}
  - {x: 0.08106995, y: 0.08572388, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.07643509, y: 0.084991455, z: 0.03149414}
  - {x: 0.08106995, y: 0.11425781, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: 0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: 0.03149414}
  - {x: 0.085250854, y: 0.11212158, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.08856964, y: 0.10882568, z: 0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.085250854, y: 0.08786011, z: 0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: 0.03149414}
  - {x: 0.09070206, y: 0.09536743, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.08856964, y: 0.09118652, z: 0.03149414}
  - {x: 0.09070206, y: 0.104644775, z: 0.03149414}
  - {x: 0.07643509, y: 0.1000061, z: 0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: 0.03149414}
  - {x: 0.09143448, y: 0.1000061, z: 0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: 0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: -0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: -0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: -0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: -0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: 0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: 0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: -0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: -0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: -0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: -0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: -0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: -0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: -0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: -0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: -0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: -0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: 0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: 0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: -0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: -0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: 0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: 0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: -0.03149414}
  - {x: 0.09143448, y: 0, z: -0.03149414}
  - {x: 0.09143448, y: 0, z: 0.03149414}
  - {x: 0.09143448, y: 0, z: 0.03149414}
  - {x: 0.09143448, y: 0, z: -0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: -0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: 0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: 0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: -0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: -0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: 0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: 0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: -0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: -0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: -0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: -0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: -0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: -0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: -0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: -0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: -0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: -0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: 0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: 0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: -0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: -0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: -0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: -0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: 0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: 0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: -0.03149414}
  - {x: 0.0614357, y: 0, z: -0.03149414}
  - {x: 0.0614357, y: 0, z: 0.03149414}
  - {x: 0.0614357, y: 0, z: 0.03149414}
  - {x: 0.0614357, y: 0, z: -0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: -0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: 0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.0614357, y: 0, z: -0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: -0.03149414}
  - {x: 0.0614357, y: 0, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: -0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: -0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: -0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: -0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: -0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: -0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: -0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: -0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: -0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: -0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: -0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: -0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: -0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: -0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: -0.03149414}
  - {x: 0.09143448, y: 0, z: -0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: -0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: -0.03149414}
  - {x: 0.07643509, y: 0, z: -0.03149414}
  - {x: 0.09143448, y: 0, z: -0.03149414}
  - {x: 0.0614357, y: 0, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.06216812, y: 0.004638672, z: 0.03149414}
  - {x: 0.06216812, y: -0.004638672, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.0614357, y: 0, z: 0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: 0.03149414}
  - {x: 0.06430054, y: 0.00881958, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.067619324, y: 0.012145996, z: 0.03149414}
  - {x: 0.067619324, y: -0.012145996, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.06430054, y: -0.00881958, z: 0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: 0.03149414}
  - {x: 0.07180023, y: 0.014251709, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.07643509, y: 0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.07180023, y: -0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: 0.03149414}
  - {x: 0.08106995, y: -0.014251709, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.07643509, y: -0.015014648, z: 0.03149414}
  - {x: 0.08106995, y: 0.014251709, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: 0.03149414}
  - {x: 0.085250854, y: 0.012145996, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.08856964, y: 0.00881958, z: 0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.085250854, y: -0.012145996, z: 0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: 0.03149414}
  - {x: 0.09070206, y: -0.004638672, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.08856964, y: -0.00881958, z: 0.03149414}
  - {x: 0.09070206, y: 0.004638672, z: 0.03149414}
  - {x: 0.07643509, y: 0, z: 0.03149414}
  - {x: 0.09143448, y: 0, z: 0.03149414}
  - {x: 0.09143448, y: 0, z: 0.03149414}
  - {x: 0.08384323, y: -0.11495972, z: -0.021072388}
  - {x: -0.09770203, y: -0.11495972, z: -0.021072388}
  - {x: -0.09770203, y: -0.10687256, z: -0.018005371}
  - {x: -0.09770203, y: -0.09875488, z: -0.0149383545}
  - {x: 0.08384323, y: -0.09875488, z: -0.0149383545}
  - {x: 0.08384323, y: -0.09875488, z: -0.0149383545}
  - {x: -0.09770203, y: -0.09875488, z: -0.0149383545}
  - {x: -0.09770203, y: -0.09542847, z: -0.007507324}
  - {x: -0.09770203, y: -0.09207153, z: -0.000076293945}
  - {x: 0.08384323, y: -0.09207153, z: -0.000076293945}
  - {x: 0.08384323, y: -0.09207153, z: -0.000076293945}
  - {x: -0.09770203, y: -0.09207153, z: -0.000076293945}
  - {x: -0.09770203, y: -0.09542847, z: 0.0073547363}
  - {x: -0.09770203, y: -0.09875488, z: 0.014755249}
  - {x: 0.08384323, y: -0.09875488, z: 0.014755249}
  - {x: 0.08384323, y: -0.09875488, z: 0.014755249}
  - {x: -0.09770203, y: -0.09875488, z: 0.014755249}
  - {x: -0.09770203, y: -0.10687256, z: 0.017852783}
  - {x: -0.09770203, y: -0.11495972, z: 0.0209198}
  - {x: 0.08384323, y: -0.11495972, z: 0.0209198}
  - {x: 0.08384323, y: -0.11495972, z: 0.0209198}
  - {x: -0.09770203, y: -0.11495972, z: 0.0209198}
  - {x: -0.09770203, y: -0.12307739, z: 0.017837524}
  - {x: -0.09770203, y: -0.13116455, z: 0.014755249}
  - {x: 0.08384323, y: -0.13116455, z: 0.014755249}
  - {x: 0.08384323, y: -0.13116455, z: 0.014755249}
  - {x: -0.09770203, y: -0.13116455, z: 0.014755249}
  - {x: -0.09770203, y: -0.13452148, z: 0.0073394775}
  - {x: -0.09770203, y: -0.13790894, z: -0.000076293945}
  - {x: 0.08384323, y: -0.13790894, z: -0.000076293945}
  - {x: 0.08384323, y: -0.13790894, z: -0.000076293945}
  - {x: -0.09770203, y: -0.13790894, z: -0.000076293945}
  - {x: -0.09770203, y: -0.13452148, z: -0.007507324}
  - {x: -0.09770203, y: -0.13116455, z: -0.0149383545}
  - {x: 0.08384323, y: -0.13116455, z: -0.0149383545}
  - {x: 0.08384323, y: -0.13116455, z: -0.0149383545}
  - {x: -0.09770203, y: -0.13116455, z: -0.0149383545}
  - {x: -0.09770203, y: -0.12307739, z: -0.018005371}
  - {x: -0.09770203, y: -0.11495972, z: -0.021072388}
  - {x: 0.08384323, y: -0.11495972, z: -0.021072388}
  - {x: -0.09770203, y: -0.11495972, z: -0.021072388}
  - {x: -0.10154724, y: -0.11495972, z: -0.010574341}
  - {x: -0.10154724, y: -0.10958862, z: -0.012039185}
  - {x: -0.09770203, y: -0.10687256, z: -0.018005371}
  - {x: -0.10154724, y: -0.11495972, z: -0.010574341}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.10687256, z: -0.007507324}
  - {x: -0.10154724, y: -0.10958862, z: -0.012039185}
  - {x: -0.10154724, y: -0.10687256, z: -0.007507324}
  - {x: -0.09770203, y: -0.09875488, z: -0.0149383545}
  - {x: -0.09770203, y: -0.10687256, z: -0.018005371}
  - {x: -0.10154724, y: -0.10958862, z: -0.012039185}
  - {x: -0.09770203, y: -0.09875488, z: -0.0149383545}
  - {x: -0.10154724, y: -0.10687256, z: -0.007507324}
  - {x: -0.10154724, y: -0.10192871, z: -0.0050201416}
  - {x: -0.09770203, y: -0.09542847, z: -0.007507324}
  - {x: -0.10154724, y: -0.10687256, z: -0.007507324}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.103515625, z: -0.000076293945}
  - {x: -0.10154724, y: -0.10192871, z: -0.0050201416}
  - {x: -0.10154724, y: -0.103515625, z: -0.000076293945}
  - {x: -0.09770203, y: -0.09207153, z: -0.000076293945}
  - {x: -0.09770203, y: -0.09542847, z: -0.007507324}
  - {x: -0.10154724, y: -0.10192871, z: -0.0050201416}
  - {x: -0.09770203, y: -0.09207153, z: -0.000076293945}
  - {x: -0.10154724, y: -0.103515625, z: -0.000076293945}
  - {x: -0.10154724, y: -0.10195923, z: 0.0048675537}
  - {x: -0.09770203, y: -0.09542847, z: 0.0073547363}
  - {x: -0.10154724, y: -0.103515625, z: -0.000076293945}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.10687256, z: 0.0073394775}
  - {x: -0.10154724, y: -0.10195923, z: 0.0048675537}
  - {x: -0.10154724, y: -0.10687256, z: 0.0073394775}
  - {x: -0.09770203, y: -0.09875488, z: 0.014755249}
  - {x: -0.09770203, y: -0.09542847, z: 0.0073547363}
  - {x: -0.10154724, y: -0.10195923, z: 0.0048675537}
  - {x: -0.09770203, y: -0.09875488, z: 0.014755249}
  - {x: -0.10154724, y: -0.10687256, z: 0.0073394775}
  - {x: -0.10154724, y: -0.109558105, z: 0.011871338}
  - {x: -0.09770203, y: -0.10687256, z: 0.017852783}
  - {x: -0.10154724, y: -0.10687256, z: 0.0073394775}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.11495972, z: 0.010421753}
  - {x: -0.10154724, y: -0.109558105, z: 0.011871338}
  - {x: -0.10154724, y: -0.11495972, z: 0.010421753}
  - {x: -0.09770203, y: -0.11495972, z: 0.0209198}
  - {x: -0.09770203, y: -0.10687256, z: 0.017852783}
  - {x: -0.10154724, y: -0.109558105, z: 0.011871338}
  - {x: -0.09770203, y: -0.11495972, z: 0.0209198}
  - {x: -0.10154724, y: -0.11495972, z: 0.010421753}
  - {x: -0.10154724, y: -0.120391846, z: 0.011871338}
  - {x: -0.09770203, y: -0.12307739, z: 0.017837524}
  - {x: -0.10154724, y: -0.11495972, z: 0.010421753}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12307739, z: 0.0073394775}
  - {x: -0.10154724, y: -0.120391846, z: 0.011871338}
  - {x: -0.10154724, y: -0.12307739, z: 0.0073394775}
  - {x: -0.09770203, y: -0.13116455, z: 0.014755249}
  - {x: -0.09770203, y: -0.12307739, z: 0.017837524}
  - {x: -0.10154724, y: -0.120391846, z: 0.011871338}
  - {x: -0.09770203, y: -0.13116455, z: 0.014755249}
  - {x: -0.10154724, y: -0.12307739, z: 0.0073394775}
  - {x: -0.10154724, y: -0.12802124, z: 0.0048675537}
  - {x: -0.09770203, y: -0.13452148, z: 0.0073394775}
  - {x: -0.10154724, y: -0.12307739, z: 0.0073394775}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12640381, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12802124, z: 0.0048675537}
  - {x: -0.10154724, y: -0.12640381, z: -0.000076293945}
  - {x: -0.09770203, y: -0.13790894, z: -0.000076293945}
  - {x: -0.09770203, y: -0.13452148, z: 0.0073394775}
  - {x: -0.10154724, y: -0.12802124, z: 0.0048675537}
  - {x: -0.09770203, y: -0.13790894, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12640381, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12802124, z: -0.0050354004}
  - {x: -0.09770203, y: -0.13452148, z: -0.007507324}
  - {x: -0.10154724, y: -0.12640381, z: -0.000076293945}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.12307739, z: -0.007507324}
  - {x: -0.10154724, y: -0.12802124, z: -0.0050354004}
  - {x: -0.10154724, y: -0.12307739, z: -0.007507324}
  - {x: -0.09770203, y: -0.13116455, z: -0.0149383545}
  - {x: -0.09770203, y: -0.13452148, z: -0.007507324}
  - {x: -0.10154724, y: -0.12802124, z: -0.0050354004}
  - {x: -0.09770203, y: -0.13116455, z: -0.0149383545}
  - {x: -0.10154724, y: -0.12307739, z: -0.007507324}
  - {x: -0.10154724, y: -0.12036133, z: -0.012023926}
  - {x: -0.09770203, y: -0.12307739, z: -0.018005371}
  - {x: -0.10154724, y: -0.12307739, z: -0.007507324}
  - {x: -0.10630798, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10154724, y: -0.11495972, z: -0.010574341}
  - {x: -0.10154724, y: -0.12036133, z: -0.012023926}
  - {x: -0.10154724, y: -0.11495972, z: -0.010574341}
  - {x: -0.09770203, y: -0.11495972, z: -0.021072388}
  - {x: -0.09770203, y: -0.12307739, z: -0.018005371}
  - {x: -0.10154724, y: -0.12036133, z: -0.012023926}
  - {x: -0.09453201, y: -0.1232605, z: 0.023208618}
  - {x: -0.10643387, y: -0.1232605, z: 0.023208618}
  - {x: -0.10643387, y: -0.13067627, z: 0.019729614}
  - {x: -0.09453201, y: -0.13067627, z: 0.019729614}
  - {x: -0.09453201, y: -0.13067627, z: 0.019729614}
  - {x: -0.10643387, y: -0.13067627, z: 0.019729614}
  - {x: -0.10643387, y: -0.13659668, z: 0.014328003}
  - {x: -0.09453201, y: -0.13659668, z: 0.014328003}
  - {x: -0.09453201, y: -0.13659668, z: 0.014328003}
  - {x: -0.10643387, y: -0.13659668, z: 0.014328003}
  - {x: -0.10643387, y: -0.14038086, z: 0.0074920654}
  - {x: -0.09453201, y: -0.14038086, z: 0.0074920654}
  - {x: -0.09453201, y: -0.14038086, z: 0.0074920654}
  - {x: -0.10643387, y: -0.14038086, z: 0.0074920654}
  - {x: -0.10643387, y: -0.14169312, z: -0.000091552734}
  - {x: -0.09453201, y: -0.14169312, z: -0.000091552734}
  - {x: -0.09453201, y: -0.14169312, z: -0.000091552734}
  - {x: -0.10643387, y: -0.14169312, z: -0.000091552734}
  - {x: -0.10643387, y: -0.14038086, z: -0.007659912}
  - {x: -0.09453201, y: -0.14038086, z: -0.007659912}
  - {x: -0.09453201, y: -0.14038086, z: -0.007659912}
  - {x: -0.10643387, y: -0.14038086, z: -0.007659912}
  - {x: -0.10643387, y: -0.13659668, z: -0.01449585}
  - {x: -0.09453201, y: -0.13659668, z: -0.01449585}
  - {x: -0.09453201, y: -0.13659668, z: -0.01449585}
  - {x: -0.10643387, y: -0.13659668, z: -0.01449585}
  - {x: -0.10643387, y: -0.13070679, z: -0.019897461}
  - {x: -0.09453201, y: -0.13070679, z: -0.019897461}
  - {x: -0.09453201, y: -0.13070679, z: -0.019897461}
  - {x: -0.10643387, y: -0.13070679, z: -0.019897461}
  - {x: -0.10643387, y: -0.12322998, z: -0.023376465}
  - {x: -0.09453201, y: -0.12322998, z: -0.023376465}
  - {x: -0.09453201, y: -0.12322998, z: -0.023376465}
  - {x: -0.10643387, y: -0.12322998, z: -0.023376465}
  - {x: -0.10643387, y: -0.114990234, z: -0.02458191}
  - {x: -0.09453201, y: -0.114990234, z: -0.02458191}
  - {x: -0.09453201, y: -0.114990234, z: -0.02458191}
  - {x: -0.10643387, y: -0.114990234, z: -0.02458191}
  - {x: -0.10643387, y: -0.10671997, z: -0.023391724}
  - {x: -0.09453201, y: -0.10671997, z: -0.023391724}
  - {x: -0.09453201, y: -0.10671997, z: -0.023391724}
  - {x: -0.10643387, y: -0.10671997, z: -0.023391724}
  - {x: -0.10643387, y: -0.099243164, z: -0.01991272}
  - {x: -0.09453201, y: -0.099243164, z: -0.01991272}
  - {x: -0.09453201, y: -0.099243164, z: -0.01991272}
  - {x: -0.10643387, y: -0.099243164, z: -0.01991272}
  - {x: -0.10643387, y: -0.09335327, z: -0.014480591}
  - {x: -0.09453201, y: -0.09335327, z: -0.014480591}
  - {x: -0.09453201, y: -0.09335327, z: -0.014480591}
  - {x: -0.10643387, y: -0.09335327, z: -0.014480591}
  - {x: -0.10643387, y: -0.089538574, z: -0.007659912}
  - {x: -0.09453201, y: -0.089538574, z: -0.007659912}
  - {x: -0.09453201, y: -0.089538574, z: -0.007659912}
  - {x: -0.10643387, y: -0.089538574, z: -0.007659912}
  - {x: -0.10643387, y: -0.08822632, z: -0.000076293945}
  - {x: -0.09453201, y: -0.08822632, z: -0.000076293945}
  - {x: -0.09453201, y: -0.08822632, z: -0.000076293945}
  - {x: -0.10643387, y: -0.08822632, z: -0.000076293945}
  - {x: -0.10643387, y: -0.089538574, z: 0.0074920654}
  - {x: -0.09453201, y: -0.089538574, z: 0.0074920654}
  - {x: -0.09453201, y: -0.089538574, z: 0.0074920654}
  - {x: -0.10643387, y: -0.089538574, z: 0.0074920654}
  - {x: -0.10643387, y: -0.09335327, z: 0.014312744}
  - {x: -0.09453201, y: -0.09335327, z: 0.014312744}
  - {x: -0.09453201, y: -0.09335327, z: 0.014312744}
  - {x: -0.10643387, y: -0.09335327, z: 0.014312744}
  - {x: -0.10643387, y: -0.09927368, z: 0.019744873}
  - {x: -0.09453201, y: -0.09927368, z: 0.019744873}
  - {x: -0.09453201, y: -0.09927368, z: 0.019744873}
  - {x: -0.10643387, y: -0.09927368, z: 0.019744873}
  - {x: -0.10643387, y: -0.10671997, z: 0.023208618}
  - {x: -0.09453201, y: -0.10671997, z: 0.023208618}
  - {x: -0.09453201, y: -0.10671997, z: 0.023208618}
  - {x: -0.10643387, y: -0.10671997, z: 0.023208618}
  - {x: -0.10643387, y: -0.114990234, z: 0.024414062}
  - {x: -0.09453201, y: -0.114990234, z: 0.024414062}
  - {x: -0.09453201, y: -0.114990234, z: 0.024414062}
  - {x: -0.10643387, y: -0.114990234, z: 0.024414062}
  - {x: -0.10643387, y: -0.1232605, z: 0.023208618}
  - {x: -0.09453201, y: -0.1232605, z: 0.023208618}
  - {x: -0.10643387, y: -0.10671997, z: 0.023208618}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.114990234, z: 0.024414062}
  - {x: -0.10643387, y: -0.09927368, z: 0.019744873}
  - {x: -0.10643387, y: -0.114990234, z: 0.024414062}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.1232605, z: 0.023208618}
  - {x: -0.10643387, y: -0.09335327, z: 0.014312744}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.09927368, z: 0.019744873}
  - {x: -0.10643387, y: -0.13067627, z: 0.019729614}
  - {x: -0.10643387, y: -0.089538574, z: 0.0074920654}
  - {x: -0.10643387, y: -0.13067627, z: 0.019729614}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.13659668, z: 0.014328003}
  - {x: -0.10643387, y: -0.08822632, z: -0.000076293945}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.089538574, z: 0.0074920654}
  - {x: -0.10643387, y: -0.14038086, z: 0.0074920654}
  - {x: -0.10643387, y: -0.089538574, z: -0.007659912}
  - {x: -0.10643387, y: -0.14038086, z: 0.0074920654}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.14169312, z: -0.000091552734}
  - {x: -0.10643387, y: -0.09335327, z: -0.014480591}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.089538574, z: -0.007659912}
  - {x: -0.10643387, y: -0.14169312, z: -0.000091552734}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.14038086, z: -0.007659912}
  - {x: -0.10643387, y: -0.099243164, z: -0.01991272}
  - {x: -0.10643387, y: -0.13659668, z: -0.01449585}
  - {x: -0.10643387, y: -0.10671997, z: -0.023391724}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.099243164, z: -0.01991272}
  - {x: -0.10643387, y: -0.13659668, z: -0.01449585}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.13070679, z: -0.019897461}
  - {x: -0.10643387, y: -0.114990234, z: -0.02458191}
  - {x: -0.10643387, y: -0.12322998, z: -0.023376465}
  - {x: -0.10643387, y: -0.12322998, z: -0.023376465}
  - {x: -0.10643387, y: -0.11495972, z: -0.000076293945}
  - {x: -0.10643387, y: -0.114990234, z: -0.02458191}
  - {x: -0.09453201, y: -0.114990234, z: 0.024414062}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.10671997, z: 0.023208618}
  - {x: -0.09453201, y: -0.1232605, z: 0.023208618}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.114990234, z: 0.024414062}
  - {x: -0.09453201, y: -0.09927368, z: 0.019744873}
  - {x: -0.09453201, y: -0.13067627, z: 0.019729614}
  - {x: -0.09453201, y: -0.09927368, z: 0.019744873}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.09335327, z: 0.014312744}
  - {x: -0.09453201, y: -0.13659668, z: 0.014328003}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.13067627, z: 0.019729614}
  - {x: -0.09453201, y: -0.089538574, z: 0.0074920654}
  - {x: -0.09453201, y: -0.14038086, z: 0.0074920654}
  - {x: -0.09453201, y: -0.089538574, z: 0.0074920654}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.08822632, z: -0.000076293945}
  - {x: -0.09453201, y: -0.14169312, z: -0.000091552734}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.14038086, z: 0.0074920654}
  - {x: -0.09453201, y: -0.089538574, z: -0.007659912}
  - {x: -0.09453201, y: -0.14038086, z: -0.007659912}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.14169312, z: -0.000091552734}
  - {x: -0.09453201, y: -0.089538574, z: -0.007659912}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.09335327, z: -0.014480591}
  - {x: -0.09453201, y: -0.13659668, z: -0.01449585}
  - {x: -0.09453201, y: -0.09335327, z: -0.014480591}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.099243164, z: -0.01991272}
  - {x: -0.09453201, y: -0.13070679, z: -0.019897461}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.13659668, z: -0.01449585}
  - {x: -0.09453201, y: -0.10671997, z: -0.023391724}
  - {x: -0.09453201, y: -0.12322998, z: -0.023376465}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.13070679, z: -0.019897461}
  - {x: -0.09453201, y: -0.10671997, z: -0.023391724}
  - {x: -0.09453201, y: -0.11495972, z: -0.000076293945}
  - {x: -0.09453201, y: -0.114990234, z: -0.02458191}
  - {x: -0.09453201, y: -0.114990234, z: -0.02458191}
  m_Textures0:
  - {x: -2.1287012, y: -0.0000011324883}
  - {x: -1.5287013, y: -0.0000011324883}
  - {x: -1.9529891, y: 0.42426294}
  - {x: -1.9529653, y: 0.4242105}
  - {x: -1.5287013, y: -0.0000011324883}
  - {x: -1.5287013, y: 0.5999989}
  - {x: -1.5287014, y: 0.5999989}
  - {x: -1.5287014, y: -0.0000011026859}
  - {x: -1.1044372, y: 0.42421046}
  - {x: -1.1044135, y: 0.42426294}
  - {x: -1.5287013, y: -0.0000011920929}
  - {x: -0.92870134, y: -0.0000011920929}
  - {x: -0.92870134, y: -0.0000011324883}
  - {x: -1.5287013, y: -0.0000011324883}
  - {x: -1.1044135, y: -0.42426518}
  - {x: -1.1044372, y: -0.42421275}
  - {x: -1.5287014, y: -0.0000011324883}
  - {x: -1.5287014, y: -0.60000116}
  - {x: -1.5287013, y: -0.60000116}
  - {x: -1.5287013, y: -0.0000011622906}
  - {x: -1.9529653, y: -0.42421272}
  - {x: -1.9529891, y: -0.4242653}
  - {x: -1.5287013, y: -0.0000011324883}
  - {x: -2.1287012, y: -0.0000011324883}
  - {x: -0.8164104, y: -2.716418}
  - {x: -0.8146195, y: 2.4707224}
  - {x: -0.58412784, y: 2.4706428}
  - {x: -0.35360754, y: 2.4705634}
  - {x: -0.35539848, y: -2.716577}
  - {x: -1.6437359, y: -2.7164183}
  - {x: -1.6419449, y: 2.4707224}
  - {x: -1.4114045, y: 2.4706428}
  - {x: -1.1809335, y: 2.4705634}
  - {x: -1.1827245, y: -2.7165773}
  - {x: -1.6417199, y: -2.7265656}
  - {x: -1.7565556, y: 2.4680302}
  - {x: -1.4697978, y: 2.4743695}
  - {x: -1.1829509, y: 2.4807107}
  - {x: -1.0681152, y: -2.713885}
  - {x: -0.8164082, y: -2.7164183}
  - {x: -0.8146173, y: 2.4707224}
  - {x: -0.58409697, y: 2.4706428}
  - {x: -0.35360545, y: 2.4705634}
  - {x: -0.3553964, y: -2.7165773}
  - {x: 0.35539988, y: -2.7164187}
  - {x: 0.35539988, y: 2.470564}
  - {x: 0.58499575, y: 2.470564}
  - {x: 0.81462026, y: 2.470564}
  - {x: 0.81462026, y: -2.7164187}
  - {x: 1.1827267, y: -2.7164187}
  - {x: 1.1827267, y: 2.470564}
  - {x: 1.4123719, y: 2.470564}
  - {x: 1.6419455, y: 2.470564}
  - {x: 1.6419455, y: -2.7164187}
  - {x: 1.1827245, y: -2.7164187}
  - {x: 1.1827245, y: 2.470564}
  - {x: 1.4123003, y: 2.470564}
  - {x: 1.6419454, y: 2.470564}
  - {x: 1.6419454, y: -2.7164187}
  - {x: 0.3556229, y: -2.7265716}
  - {x: 0.24071786, y: 2.4680274}
  - {x: 0.52757365, y: 2.4743726}
  - {x: 0.81439364, y: 2.4807172}
  - {x: 0.92929864, y: -2.7138817}
  - {x: 1.0330122, y: -0.4799177}
  - {x: 0.38459587, y: -0.4799177}
  - {x: 0.9762049, y: -0.26518434}
  - {x: 0.88940907, y: -0.051782787}
  - {x: 0.9572147, y: -0.24740595}
  - {x: 0.39132583, y: -0.48200387}
  - {x: 1.5128596, y: -0.638626}
  - {x: 1.0535259, y: -1.1009046}
  - {x: 1.3431168, y: -0.5478214}
  - {x: 1.1345226, y: -0.4569695}
  - {x: 1.3059545, y: -0.53751254}
  - {x: 1.0570506, y: -1.1070011}
  - {x: 1.9006383, y: -0.2722777}
  - {x: 1.9529948, y: -0.920985}
  - {x: 1.706996, y: -0.33861697}
  - {x: 1.5014358, y: -0.44699442}
  - {x: 1.6711774, y: -0.3561896}
  - {x: 1.9607675, y: -0.9092728}
  - {x: 1.9397016, y: 0.12807643}
  - {x: 2.4377816, y: -0.30214453}
  - {x: 1.871896, y: -0.067546844}
  - {x: 1.7960968, y: -0.30005848}
  - {x: 1.8529038, y: -0.085326314}
  - {x: 2.4445157, y: -0.3000586}
  - {x: 1.7905853, y: 0.28227377}
  - {x: 2.4365602, y: 0.32201266}
  - {x: 1.8606448, y: 0.06685102}
  - {x: 1.9396796, y: -0.12808087}
  - {x: 1.8719082, y: 0.06759688}
  - {x: 2.4378006, y: 0.30213788}
  - {x: 1.4994391, y: 0.4377159}
  - {x: 1.9572481, y: 0.91535985}
  - {x: 1.6837171, y: 0.36115205}
  - {x: 1.8798165, y: 0.2653513}
  - {x: 1.7083397, y: 0.34586322}
  - {x: 1.9572396, y: 0.91537666}
  - {x: 1.134477, y: 0.45698452}
  - {x: 1.0570593, y: 1.1070142}
  - {x: 1.3059586, y: 0.53749967}
  - {x: 1.5148584, y: 0.62934816}
  - {x: 1.3305787, y: 0.5527848}
  - {x: 1.057047, y: 1.1069931}
  - {x: 0.88184404, y: 0.054057002}
  - {x: 0.3982587, y: 0.48324728}
  - {x: 0.9605032, y: 0.22909552}
  - {x: 1.0385274, y: 0.46211553}
  - {x: 0.9684546, y: 0.24669135}
  - {x: 0.39256036, y: 0.5018928}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.9997491, y: 0.9994308}
  - {x: 0.999749, y: 0.00045373812}
  - {x: 0.0006936856, y: 0.00045373812}
  - {x: 0.00069404463, y: 0.9994308}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.9997478, y: 0.9994308}
  - {x: 0.99974805, y: 0.00045373812}
  - {x: 0.00069427, y: 0.00045373812}
  - {x: 0.0006939089, y: 0.9994308}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.9720591, y: 0.34663567}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9720596, y: 0.6532523}
  - {x: 0.7918344, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.79183465, y: 0.90131074}
  - {x: 0.5002242, y: 0.0038277227}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.20861442, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691566, y: 0.97177905}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.028389715, y: 0.34663555}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.098857604, y: 0.7915537}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.028389134, y: 0.34663606}
  - {x: 0.028388768, y: 0.6532523}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.20861363, y: 0.09857795}
  - {x: 0.20861363, y: 0.90131074}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.5002234, y: 0.0038278755}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.6535327, y: 0.97177905}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.9015904, y: 0.20833413}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.97205883, y: 0.65325207}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.9997491, y: 0.9994308}
  - {x: 0.999749, y: 0.00045373812}
  - {x: 0.0006936856, y: 0.00045373812}
  - {x: 0.00069404463, y: 0.9994308}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.9997478, y: 0.9994308}
  - {x: 0.99974805, y: 0.00045373812}
  - {x: 0.00069427, y: 0.00045373812}
  - {x: 0.0006939089, y: 0.9994308}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.9720591, y: 0.34663567}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9720596, y: 0.6532523}
  - {x: 0.7918344, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.79183465, y: 0.90131074}
  - {x: 0.5002242, y: 0.0038277227}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.20861442, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691566, y: 0.97177905}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.028389715, y: 0.34663555}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.098857604, y: 0.7915537}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.028389134, y: 0.34663606}
  - {x: 0.028388768, y: 0.6532523}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.20861363, y: 0.09857795}
  - {x: 0.20861363, y: 0.90131074}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.5002234, y: 0.0038278755}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.6535327, y: 0.97177905}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.9015904, y: 0.20833413}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.97205883, y: 0.65325207}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.9997491, y: 0.9994308}
  - {x: 0.999749, y: 0.00045373812}
  - {x: 0.0006936856, y: 0.00045373812}
  - {x: 0.00069404463, y: 0.9994308}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.9997478, y: 0.9994308}
  - {x: 0.99974805, y: 0.00045373812}
  - {x: 0.00069427, y: 0.00045373812}
  - {x: 0.0006939089, y: 0.9994308}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.9720591, y: 0.34663567}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9720596, y: 0.6532523}
  - {x: 0.7918344, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.79183465, y: 0.90131074}
  - {x: 0.5002242, y: 0.0038277227}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.20861442, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691566, y: 0.97177905}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.028389715, y: 0.34663555}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.098857604, y: 0.7915537}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.028389134, y: 0.34663606}
  - {x: 0.028388768, y: 0.6532523}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.20861363, y: 0.09857795}
  - {x: 0.20861363, y: 0.90131074}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.5002234, y: 0.0038278755}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.6535327, y: 0.97177905}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.9015904, y: 0.20833413}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.97205883, y: 0.65325207}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 1.9545306, y: -1.4014683}
  - {x: 1.9543977, y: 3.785543}
  - {x: 2.1834636, y: 3.7855487}
  - {x: 2.4132812, y: 3.7855546}
  - {x: 2.4134142, y: -1.4014568}
  - {x: 1.9540946, y: -2.4951568}
  - {x: 1.9541168, y: 2.6918507}
  - {x: 2.1836996, y: 2.6918497}
  - {x: 2.413632, y: 2.6918488}
  - {x: 2.41361, y: -2.4951587}
  - {x: 1.9549644, y: -3.0002394}
  - {x: 1.9395528, y: 2.1880944}
  - {x: 2.1772006, y: 2.1887996}
  - {x: 2.413674, y: 2.1895022}
  - {x: 2.4290857, y: -2.9988313}
  - {x: -2.413655, y: -1.3499575}
  - {x: -2.413658, y: 3.837049}
  - {x: -2.1833742, y: 3.83705}
  - {x: -1.9541328, y: 3.8370507}
  - {x: -1.9541237, y: -1.3499556}
  - {x: -2.4135659, y: -1.399858}
  - {x: -2.413547, y: 3.78715}
  - {x: -2.1834934, y: 3.787149}
  - {x: -1.9541979, y: 3.7871482}
  - {x: -1.954217, y: -1.3998599}
  - {x: -2.41348, y: -2.4977605}
  - {x: -2.4134815, y: 2.6892467}
  - {x: -2.1840737, y: 2.6892471}
  - {x: -1.9543067, y: 2.6892462}
  - {x: -1.9543054, y: -2.4977605}
  - {x: -2.4135926, y: -2.989725}
  - {x: -2.4135902, y: 2.1972826}
  - {x: -2.1837149, y: 2.1972826}
  - {x: -1.9541951, y: 2.1972835}
  - {x: -1.9541943, y: -2.989725}
  - {x: 1.9574177, y: -1.1119094}
  - {x: 1.8492872, y: 4.0831695}
  - {x: 2.1322021, y: 4.089059}
  - {x: 2.4160535, y: 4.094966}
  - {x: 2.5241919, y: -1.1001124}
  - {x: -3.202767, y: 0.4562931}
  - {x: -3.349434, y: 0.6031699}
  - {x: -3.2898276, y: 0.630108}
  - {x: -3.1716366, y: 0.56105304}
  - {x: -4.1503987, y: 1.3554015}
  - {x: -4.1133847, y: 1.5325246}
  - {x: -3.976449, y: 1.3730571}
  - {x: -4.0472164, y: 1.3080451}
  - {x: -3.2306113, y: 0.7561265}
  - {x: -3.1258721, y: 0.60608613}
  - {x: -3.194907, y: 0.53954315}
  - {x: -3.2458622, y: 0.6732918}
  - {x: -3.449607, y: 0.7569357}
  - {x: -3.504736, y: 0.91466284}
  - {x: -3.4447958, y: 0.94373214}
  - {x: -3.386848, y: 0.86829555}
  - {x: -3.7400908, y: 1.2514856}
  - {x: -3.815334, y: 1.4189749}
  - {x: -3.6725106, y: 1.375699}
  - {x: -3.6757681, y: 1.2852365}
  - {x: -4.0977235, y: 1.3209985}
  - {x: -3.9750924, y: 1.2946666}
  - {x: -4.0440545, y: 1.1629436}
  - {x: -4.107442, y: 1.223109}
  - {x: -3.6397395, y: 1.235584}
  - {x: -3.76725, y: 1.2525369}
  - {x: -3.743023, y: 1.350518}
  - {x: -3.6606693, y: 1.3867961}
  - {x: -4.2244573, y: 1.3630733}
  - {x: -4.361214, y: 1.364901}
  - {x: -4.2448673, y: 1.5066293}
  - {x: -4.1998677, y: 1.4535149}
  - {x: -4.4616046, y: 1.1512114}
  - {x: -4.29676, y: 1.2250617}
  - {x: -4.364031, y: 1.0981036}
  - {x: -4.4583535, y: 1.0861796}
  - {x: 0.62697065, y: -2.1710627}
  - {x: 0.7752626, y: -2.268991}
  - {x: 0.6834668, y: -2.2829304}
  - {x: 0.5570577, y: -2.23531}
  - {x: 0.41872445, y: -2.6079497}
  - {x: 0.59010816, y: -2.6945548}
  - {x: 0.35790464, y: -2.6816447}
  - {x: 0.3340465, y: -2.6309586}
  - {x: 0.47291672, y: -3.2425117}
  - {x: 0.24930197, y: -3.2412572}
  - {x: 0.30731982, y: -3.1673903}
  - {x: 0.44597575, y: -3.1917744}
  - {x: -0.7907706, y: -2.8321939}
  - {x: -0.59319323, y: -2.915997}
  - {x: -0.6391769, y: -2.963922}
  - {x: -0.7787392, y: -2.9422836}
  - {x: -0.42036116, y: -2.8252933}
  - {x: -0.2567777, y: -2.920846}
  - {x: -0.441683, y: -2.9532647}
  - {x: -0.4783491, y: -2.8815494}
  - {x: -0.1050397, y: -3.3467586}
  - {x: -0.2711903, y: -3.3379343}
  - {x: -0.26803273, y: -3.239814}
  - {x: -0.15292966, y: -3.2776463}
  - {x: -1.5811217, y: -3.2051034}
  - {x: -1.4584764, y: -3.2442079}
  - {x: -1.4571807, y: -3.2918415}
  - {x: -1.5262346, y: -3.295958}
  - {x: -1.1139255, y: -3.0214005}
  - {x: -0.9562083, y: -3.0279644}
  - {x: -0.9849564, y: -3.0980835}
  - {x: -1.0805696, y: -3.0704594}
  - {x: -0.83997536, y: -3.5475845}
  - {x: -0.8059014, y: -3.6324584}
  - {x: -0.931393, y: -3.6485538}
  - {x: -0.91534245, y: -3.5847068}
  - {x: -2.1577413, y: -3.5718656}
  - {x: -2.1135654, y: -3.4829967}
  - {x: -2.0336947, y: -3.554071}
  - {x: -2.012257, y: -3.6447065}
  - {x: -1.9499502, y: -2.8918967}
  - {x: -1.9480501, y: -2.8137596}
  - {x: -1.76292, y: -2.8737195}
  - {x: -1.8257725, y: -2.9059663}
  - {x: -2.4904728, y: -3.4120898}
  - {x: -2.3362422, y: -3.4556243}
  - {x: -2.3985934, y: -3.5505667}
  - {x: -2.487742, y: -3.4852393}
  - {x: -3.3166203, y: 0.3024093}
  - {x: -3.3754296, y: 0.46461248}
  - {x: -3.2986856, y: 0.45821595}
  - {x: -3.2204142, y: 0.361542}
  - {x: -4.016883, y: 1.0400394}
  - {x: -3.8726356, y: 1.1654582}
  - {x: -3.9435253, y: 0.94545436}
  - {x: -4.0159082, y: 0.93524516}
  - {x: -3.1364028, y: 0.41154754}
  - {x: -3.1697283, y: 0.20964682}
  - {x: -3.2586896, y: 0.27655423}
  - {x: -3.2022471, y: 0.39970183}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050539, y: 0.00045373812}
  - {x: 0.20050527, y: 0.9994308}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.00045373812}
  - {x: 0.300411, y: 0.9994308}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.00045373812}
  - {x: 0.4003165, y: 0.9994308}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.00045373812}
  - {x: 0.50022197, y: 0.9994308}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.00045373812}
  - {x: 0.6001273, y: 0.9994308}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000327, y: 0.00045373812}
  - {x: 0.7000328, y: 0.9994308}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993796, y: 0.00045373812}
  - {x: 0.79993814, y: 0.9994308}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.8998432, y: 0.00045373812}
  - {x: 0.89984345, y: 0.9994308}
  - {x: 0.9997491, y: 0.9994308}
  - {x: 0.999749, y: 0.00045373812}
  - {x: 0.0006936856, y: 0.00045373812}
  - {x: 0.00069404463, y: 0.9994308}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059935, y: 0.00045373812}
  - {x: 0.10059965, y: 0.9994308}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.20050502, y: 0.00045373812}
  - {x: 0.2005051, y: 0.9994308}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.00045373812}
  - {x: 0.30041057, y: 0.9994308}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.00045373812}
  - {x: 0.400316, y: 0.9994308}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.00045373812}
  - {x: 0.5002215, y: 0.9994308}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.00045373812}
  - {x: 0.6001268, y: 0.9994308}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000322, y: 0.00045373812}
  - {x: 0.7000321, y: 0.9994308}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999375, y: 0.00045373812}
  - {x: 0.7999371, y: 0.9994308}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.8998428, y: 0.00045373812}
  - {x: 0.89984244, y: 0.9994308}
  - {x: 0.9997478, y: 0.9994308}
  - {x: 0.99974805, y: 0.00045373812}
  - {x: 0.00069427, y: 0.00045373812}
  - {x: 0.0006939089, y: 0.9994308}
  - {x: 0.100599565, y: 0.9994308}
  - {x: 0.100599855, y: 0.00045373812}
  - {x: 0.9720591, y: 0.34663567}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9963408, y: 0.4999439}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.9720596, y: 0.6532523}
  - {x: 0.7918344, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.901591, y: 0.20833425}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.9015915, y: 0.7915539}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.79183465, y: 0.90131074}
  - {x: 0.5002242, y: 0.0038277227}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.6535327, y: 0.028109413}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.653533, y: 0.97177905}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.20861442, y: 0.09857755}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691587, y: 0.028109383}
  - {x: 0.5002243, y: 0.99606085}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.34691566, y: 0.97177905}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.028389715, y: 0.34663555}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.09885782, y: 0.20833413}
  - {x: 0.20861419, y: 0.9013106}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.098857604, y: 0.7915537}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.028389595, y: 0.6532522}
  - {x: 0.5002245, y: 0.49994415}
  - {x: 0.0041080266, y: 0.49994385}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.028389134, y: 0.34663606}
  - {x: 0.028388768, y: 0.6532523}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.004107505, y: 0.49994415}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.09885708, y: 0.20833468}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.20861363, y: 0.09857795}
  - {x: 0.20861363, y: 0.90131074}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.0988569, y: 0.7915539}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.346915, y: 0.028109718}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.5002234, y: 0.0038278755}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.34691536, y: 0.97177905}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.6535327, y: 0.97177905}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.500224, y: 0.99606085}
  - {x: 0.65353197, y: 0.028109442}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.7918337, y: 0.09857755}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.9015904, y: 0.20833413}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.7918344, y: 0.9013106}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.97205883, y: 0.65325207}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.901591, y: 0.79155356}
  - {x: 0.9720586, y: 0.3466355}
  - {x: 0.5002239, y: 0.49994427}
  - {x: 0.99634045, y: 0.49994373}
  - {x: 0.99634045, y: 0.49994373}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0.00000001714933, w: -1}
  - {x: -1, y: 0, z: 0.00000001714933, w: -1}
  - {x: -1, y: 0, z: 0.00000001714933, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0.000000017149329, w: -1}
  - {x: -1, y: 0, z: 0.000000017149329, w: -1}
  - {x: -1, y: 0, z: 0.000000017149329, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0.00000007367067, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -0.38260856, y: 0.0006932354, z: 0.9239103, w: -1}
  - {x: -0.38253763, y: 0.0006937765, z: 0.92393965, w: -1}
  - {x: -0.38267946, y: 0.0006926939, z: 0.92388093, w: -1}
  - {x: -0.38282126, y: 0.00069161144, z: 0.92382216, w: -1}
  - {x: -0.3827504, y: 0.0006921527, z: 0.92385155, w: -1}
  - {x: -0.9238587, y: 0.0006909333, z: 0.38273317, w: -1}
  - {x: -0.92387086, y: 0.0006893229, z: 0.38270378, w: -1}
  - {x: -0.92384654, y: 0.00069115934, z: 0.38276255, w: -1}
  - {x: -0.92382216, y: 0.00069161144, z: 0.38282126, w: -1}
  - {x: -0.9238343, y: 0.0006920775, z: 0.3827919, w: -1}
  - {x: -0.92243314, y: -0.055061046, z: -0.3822113, w: -1}
  - {x: -0.9224209, y: -0.05506216, z: -0.3822406, w: -1}
  - {x: -0.92244524, y: -0.055061307, z: -0.38218194, w: -1}
  - {x: -0.9224695, y: -0.05506183, z: -0.3821233, w: -1}
  - {x: -0.92245746, y: -0.055060882, z: -0.38215265, w: -1}
  - {x: -0.38275036, y: 0.0006926718, z: -0.92385155, w: -1}
  - {x: -0.38282126, y: 0.00069368834, z: -0.92382216, w: -1}
  - {x: -0.38267946, y: 0.00069234765, z: -0.92388093, w: -1}
  - {x: -0.38253763, y: 0.0006916994, z: -0.92393965, w: -1}
  - {x: -0.38260856, y: 0.0006916774, z: -0.9239103, w: -1}
  - {x: 0.38260865, y: 0, z: -0.9239105, w: -1}
  - {x: 0.38253772, y: 0, z: -0.9239399, w: -1}
  - {x: 0.38267955, y: 3.374836e-14, z: -0.9238811, w: -1}
  - {x: 0.38282135, y: 0, z: -0.9238224, w: -1}
  - {x: 0.38275048, y: 5.0622545e-14, z: -0.9238518, w: -1}
  - {x: 0.92385894, y: 2.097285e-14, z: -0.3827333, w: -1}
  - {x: 0.9238711, y: 0, z: -0.38270393, w: -1}
  - {x: 0.9238468, y: 1.39818975e-14, z: -0.38276267, w: -1}
  - {x: 0.9238224, y: 0, z: -0.38282135, w: -1}
  - {x: 0.92383456, y: -2.0972847e-14, z: -0.382792, w: -1}
  - {x: 0.92383456, y: 2.0972846e-14, z: 0.382792, w: -1}
  - {x: 0.9238224, y: 0, z: 0.38282135, w: -1}
  - {x: 0.9238467, y: -1.39818975e-14, z: 0.38276264, w: -1}
  - {x: 0.9238711, y: 0, z: 0.38270393, w: -1}
  - {x: 0.92385894, y: -2.0972847e-14, z: 0.3827333, w: -1}
  - {x: 0.3821687, y: -0.055111885, z: 0.92244774, w: -1}
  - {x: 0.38223952, y: -0.05511168, z: 0.92241836, w: -1}
  - {x: 0.3820979, y: -0.055112757, z: 0.922477, w: -1}
  - {x: 0.38195628, y: -0.05511453, z: 0.92253554, w: -1}
  - {x: 0.382027, y: -0.055113293, z: 0.9225063, w: -1}
  - {x: 0.7736767, y: -0.6335806, z: 0.0000000075619155, w: -1}
  - {x: 0.7736767, y: -0.6335806, z: 0.0000000075619155, w: -1}
  - {x: 0.7736767, y: -0.6335806, z: 0.0000000075619155, w: -1}
  - {x: 0.7814261, y: -0.62170273, z: -0.05346934, w: -1}
  - {x: 0.7814261, y: -0.62170273, z: -0.05346934, w: -1}
  - {x: 0.7814261, y: -0.62170273, z: -0.05346934, w: -1}
  - {x: 0.95048594, y: -0.31042033, z: -0.014688901, w: -1}
  - {x: 0.95048594, y: -0.31042033, z: -0.014688901, w: -1}
  - {x: 0.95048594, y: -0.31042033, z: -0.014688901, w: -1}
  - {x: 0.9593809, y: -0.27537578, z: -0.061290108, w: -1}
  - {x: 0.9593809, y: -0.27537578, z: -0.061290108, w: -1}
  - {x: 0.9593809, y: -0.27537578, z: -0.061290108, w: -1}
  - {x: 0.96593666, y: 0.22882926, z: 0.120845534, w: -1}
  - {x: 0.96593666, y: 0.22882926, z: 0.120845534, w: -1}
  - {x: 0.96593666, y: 0.22882926, z: 0.120845534, w: -1}
  - {x: 0.95048577, y: 0.310421, z: 0.014688029, w: -1}
  - {x: 0.95048577, y: 0.310421, z: 0.014688029, w: -1}
  - {x: 0.95048577, y: 0.310421, z: 0.014688029, w: -1}
  - {x: 0.781426, y: 0.6217029, z: 0.053468835, w: -1}
  - {x: 0.781426, y: 0.6217029, z: 0.053468835, w: -1}
  - {x: 0.781426, y: 0.6217029, z: 0.053468835, w: -1}
  - {x: 0.7736767, y: 0.63358057, z: 0.00000014206019, w: -1}
  - {x: 0.7736767, y: 0.63358057, z: 0.00000014206019, w: -1}
  - {x: 0.7736767, y: 0.63358057, z: 0.00000014206019, w: -1}
  - {x: 0.7809006, y: 0.62268424, z: -0.049585078, w: -1}
  - {x: 0.7809006, y: 0.62268424, z: -0.049585078, w: -1}
  - {x: 0.7809006, y: 0.62268424, z: -0.049585078, w: -1}
  - {x: 0.78140604, y: 0.6217422, z: -0.053304866, w: -1}
  - {x: 0.78140604, y: 0.6217422, z: -0.053304866, w: -1}
  - {x: 0.78140604, y: 0.6217422, z: -0.053304866, w: -1}
  - {x: 0.9604482, y: 0.26997858, z: -0.06819723, w: -1}
  - {x: 0.9604482, y: 0.26997858, z: -0.06819723, w: -1}
  - {x: 0.9604482, y: 0.26997858, z: -0.06819723, w: -1}
  - {x: 0.9594009, y: 0.27527776, z: -0.061418083, w: -1}
  - {x: 0.9594009, y: 0.27527776, z: -0.061418083, w: -1}
  - {x: 0.9594009, y: 0.27527776, z: -0.061418083, w: -1}
  - {x: 0.9593991, y: -0.27528644, z: 0.061406735, w: -1}
  - {x: 0.9593991, y: -0.27528644, z: 0.061406735, w: -1}
  - {x: 0.9593991, y: -0.27528644, z: 0.061406735, w: -1}
  - {x: 0.9604485, y: -0.26997697, z: 0.06819931, w: -1}
  - {x: 0.9604485, y: -0.26997697, z: 0.06819931, w: -1}
  - {x: 0.9604485, y: -0.26997697, z: 0.06819931, w: -1}
  - {x: 0.787563, y: -0.60482967, z: 0.118006945, w: -1}
  - {x: 0.787563, y: -0.60482967, z: 0.118006945, w: -1}
  - {x: 0.787563, y: -0.60482967, z: 0.118006945, w: -1}
  - {x: 0.78090644, y: -0.6226732, z: 0.049631756, w: -1}
  - {x: 0.78090644, y: -0.6226732, z: 0.049631756, w: -1}
  - {x: 0.78090644, y: -0.6226732, z: 0.049631756, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938718, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938718, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.80834717, y: 0.58870614, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.58748484, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898264, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898267, y: 0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.0005100457, y: 0.9999999, z: 0, w: -1}
  - {x: -0.0005100457, y: 0.9999999, z: 0, w: -1}
  - {x: -0.00051003054, y: 0.9999999, z: 0, w: -1}
  - {x: -0.00051003054, y: 0.9999999, z: 0, w: -1}
  - {x: -0.30807084, y: 0.95136344, z: 0, w: -1}
  - {x: -0.30807084, y: 0.95136344, z: 0, w: -1}
  - {x: -0.30807084, y: 0.9513634, z: 0, w: -1}
  - {x: -0.30807084, y: 0.95136344, z: 0, w: -1}
  - {x: -0.58816075, y: 0.808744, z: 0, w: -1}
  - {x: -0.5881608, y: 0.808744, z: 0, w: -1}
  - {x: -0.58816075, y: 0.808744, z: 0, w: -1}
  - {x: -0.58816075, y: 0.808744, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938718, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938718, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.8097015, y: -0.58684194, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.58816075, y: -0.808744, z: 0, w: -1}
  - {x: -0.58816075, y: -0.808744, z: 0, w: -1}
  - {x: -0.58816075, y: -0.808744, z: 0, w: -1}
  - {x: -0.58816075, y: -0.808744, z: 0, w: -1}
  - {x: -0.30807084, y: -0.9513634, z: 0, w: -1}
  - {x: -0.30807084, y: -0.9513634, z: 0, w: -1}
  - {x: -0.30807084, y: -0.95136344, z: 0, w: -1}
  - {x: -0.30807084, y: -0.95136344, z: 0, w: -1}
  - {x: -0.00051003054, y: -0.9999999, z: 0, w: -1}
  - {x: -0.0005100306, y: -0.9999999, z: 0, w: -1}
  - {x: -0.0005100456, y: -0.9999999, z: 0, w: -1}
  - {x: -0.0005100456, y: -0.9999999, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: -0.9999984, y: -0.0018119097, z: 0, w: -1}
  - {x: -0.9999984, y: -0.0018119097, z: 0, w: -1}
  - {x: -1, y: -0.00000047778, z: 0, w: -1}
  - {x: -0.99999344, y: -0.0036229077, z: 0, w: -1}
  - {x: -1, y: -0.00000048094375, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -0.99999976, y: 0.0007034502, z: 0, w: -1}
  - {x: -0.99999976, y: 0.0007034502, z: 0, w: -1}
  - {x: -0.9999955, y: 0.002996729, z: 0, w: -1}
  - {x: -1, y: -0.0002471577, z: 0, w: -1}
  - {x: -0.99999875, y: -0.0015901749, z: 0, w: -1}
  - {x: -0.99999964, y: 0.00087242376, z: 0, w: -1}
  - {x: -0.9999997, y: 0.0007632235, z: 0, w: -1}
  - {x: -0.9999997, y: 0.0007632235, z: 0, w: -1}
  - {x: -1, y: 0.00000009846362, z: 0, w: -1}
  - {x: -1, y: 0.00000009846362, z: 0, w: -1}
  - {x: -1, y: 0.00020591781, z: 0, w: -1}
  - {x: -0.9999998, y: 0.0006540076, z: 0, w: -1}
  - {x: -1, y: -0.00020572064, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.99999976, y: -0.00070322584, z: 0, w: -1}
  - {x: -0.99999976, y: -0.00070322584, z: 0, w: -1}
  - {x: -0.99999875, y: 0.0015900038, z: 0, w: -1}
  - {x: -0.9999999, y: 0.0005288116, z: 0, w: -1}
  - {x: -1, y: -0.000062212566, z: 0, w: -1}
  - {x: -1, y: -0.000062212566, z: 0, w: -1}
  - {x: -0.9999955, y: -0.0029961132, z: 0, w: -1}
  - {x: -0.9999998, y: -0.00065326696, z: 0, w: -1}
  - {x: -0.9999984, y: 0.001812092, z: 0, w: -1}
  - {x: -0.9999984, y: 0.001812092, z: 0, w: -1}
  - {x: -0.99999344, y: 0.0036231529, z: 0, w: -1}
  - {x: -0.99999964, y: -0.00087162136, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -1, y: 0.00000059722475, z: 0, w: -1}
  - {x: -1, y: 0.0002471342, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: 1, y: 0.00000023889024, z: 0, w: -1}
  - {x: 0.9999984, y: 0.0018116042, z: 0, w: -1}
  - {x: 0.9999984, y: 0.0018116042, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00000024047225, z: 0, w: -1}
  - {x: 0.99999344, y: 0.0036225352, z: 0, w: -1}
  - {x: 1, y: 0.0002473744, z: 0, w: -1}
  - {x: 0.9999955, y: -0.0029973104, z: 0, w: -1}
  - {x: 0.99999976, y: -0.00070410577, z: 0, w: -1}
  - {x: 0.99999976, y: -0.00070410577, z: 0, w: -1}
  - {x: 0.9999997, y: -0.0007630822, z: 0, w: -1}
  - {x: 0.9999997, y: -0.0007630822, z: 0, w: -1}
  - {x: 0.99999964, y: -0.00087225146, z: 0, w: -1}
  - {x: 0.99999875, y: 0.0015894411, z: 0, w: -1}
  - {x: 0.9999998, y: -0.000653897, z: 0, w: -1}
  - {x: 1, y: -0.00020692796, z: 0, w: -1}
  - {x: 1, y: -0.00000090095915, z: 0, w: -1}
  - {x: 1, y: -0.00000090095915, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 1, y: 0.00020512621, z: 0, w: -1}
  - {x: 1, y: 0.00006213786, z: 0, w: -1}
  - {x: 1, y: 0.00006213786, z: 0, w: -1}
  - {x: 0.9999999, y: -0.00052884966, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.9999998, y: 0.00065315637, z: 0, w: -1}
  - {x: 0.9999955, y: 0.002995931, z: 0, w: -1}
  - {x: 0.99999994, y: -0.00031412867, z: 0, w: -1}
  - {x: 0.99999994, y: -0.00031412867, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999344, y: -0.003623573, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.0002475676, z: 0, w: -1}
  - {x: 1, y: -0.000001075005, z: 0, w: -1}
  - {x: 1, y: -0.000001075005, z: 0, w: -1}
  - {x: 1, y: -0.000001075005, z: 0, w: -1}
  - {x: 1, y: -0.0000010821238, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: -0.58870614, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938718, z: 0, w: -1}
  - {x: 0.9509362, y: -0.30938718, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.8083471, y: 0.58870614, z: 0, w: -1}
  - {x: 0.80834717, y: 0.58870614, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.58748484, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898264, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898267, y: 0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510676, z: 0, w: -1}
  - {x: -0.58934796, y: 0.8078794, z: 0, w: -1}
  - {x: -0.5893479, y: 0.8078793, z: 0, w: -1}
  - {x: -0.58934796, y: 0.8078793, z: 0, w: -1}
  - {x: -0.58934796, y: 0.8078793, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: 0.586842, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938718, z: 0, w: -1}
  - {x: -0.9509362, y: 0.30938718, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.9509362, y: -0.30938715, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.8097015, y: -0.58684194, z: 0, w: -1}
  - {x: -0.80970144, y: -0.586842, z: 0, w: -1}
  - {x: -0.58934796, y: -0.8078794, z: 0, w: -1}
  - {x: -0.58934796, y: -0.8078793, z: 0, w: -1}
  - {x: -0.58934796, y: -0.8078793, z: 0, w: -1}
  - {x: -0.58934796, y: -0.8078793, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: -1, y: 0.00012287291, z: 0, w: -1}
  - {x: -1, y: 0.00012287291, z: 0, w: -1}
  - {x: -1, y: -0.0000004809441, z: 0, w: -1}
  - {x: -1, y: 0.0002461984, z: 0, w: -1}
  - {x: -1, y: -0.00000048094375, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -0.99999976, y: 0.0007034502, z: 0, w: -1}
  - {x: -0.99999976, y: 0.0007034502, z: 0, w: -1}
  - {x: -0.9999955, y: 0.002996729, z: 0, w: -1}
  - {x: -1, y: -0.0002471577, z: 0, w: -1}
  - {x: -0.99999875, y: -0.0015901749, z: 0, w: -1}
  - {x: -0.99999964, y: 0.00087242376, z: 0, w: -1}
  - {x: -0.9999997, y: 0.0007632235, z: 0, w: -1}
  - {x: -0.9999997, y: 0.0007632235, z: 0, w: -1}
  - {x: -1, y: 0.00000009846362, z: 0, w: -1}
  - {x: -1, y: 0.00000009846362, z: 0, w: -1}
  - {x: -1, y: 0.00020591781, z: 0, w: -1}
  - {x: -0.9999998, y: 0.0006540076, z: 0, w: -1}
  - {x: -1, y: -0.00020572064, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.9999999, y: -0.0005288116, z: 0, w: -1}
  - {x: -0.99999976, y: -0.00070322584, z: 0, w: -1}
  - {x: -0.99999976, y: -0.00070322584, z: 0, w: -1}
  - {x: -0.99999875, y: 0.0015900038, z: 0, w: -1}
  - {x: -0.9999999, y: 0.0005288116, z: 0, w: -1}
  - {x: -1, y: -0.000062212566, z: 0, w: -1}
  - {x: -1, y: -0.000062212566, z: 0, w: -1}
  - {x: -0.9999955, y: -0.0029961132, z: 0, w: -1}
  - {x: -0.9999998, y: -0.00065326696, z: 0, w: -1}
  - {x: -1, y: -0.00012269088, z: 0, w: -1}
  - {x: -1, y: -0.00012269088, z: 0, w: -1}
  - {x: -1, y: -0.0002459546, z: 0, w: -1}
  - {x: -0.99999964, y: -0.00087162136, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -1, y: 0.00000060117986, z: 0, w: -1}
  - {x: -1, y: 0.0002471342, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: 1, y: 0.0000002404723, z: 0, w: -1}
  - {x: 1, y: -0.00012317838, z: 0, w: -1}
  - {x: 1, y: -0.00012317838, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00000024047225, z: 0, w: -1}
  - {x: 1, y: -0.00024656887, z: 0, w: -1}
  - {x: 1, y: 0.0002473744, z: 0, w: -1}
  - {x: 0.9999955, y: -0.0029973104, z: 0, w: -1}
  - {x: 0.99999976, y: -0.00070410577, z: 0, w: -1}
  - {x: 0.99999976, y: -0.00070410577, z: 0, w: -1}
  - {x: 0.9999997, y: -0.0007630822, z: 0, w: -1}
  - {x: 0.9999997, y: -0.0007630822, z: 0, w: -1}
  - {x: 0.99999964, y: -0.00087225146, z: 0, w: -1}
  - {x: 0.99999875, y: 0.0015894411, z: 0, w: -1}
  - {x: 0.9999998, y: -0.000653897, z: 0, w: -1}
  - {x: 1, y: -0.00020692796, z: 0, w: -1}
  - {x: 1, y: -0.00000090095915, z: 0, w: -1}
  - {x: 1, y: -0.00000090095915, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 0.9999999, y: 0.00052884966, z: 0, w: -1}
  - {x: 1, y: 0.00020512621, z: 0, w: -1}
  - {x: 1, y: 0.00006213786, z: 0, w: -1}
  - {x: 1, y: 0.00006213786, z: 0, w: -1}
  - {x: 0.9999999, y: -0.00052884966, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.99999875, y: -0.0015904425, z: 0, w: -1}
  - {x: 0.9999998, y: 0.00065315637, z: 0, w: -1}
  - {x: 0.9999955, y: 0.002995931, z: 0, w: -1}
  - {x: 0.9999987, y: 0.0016206128, z: 0, w: -1}
  - {x: 0.9999987, y: 0.0016206128, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 1, y: 0.0002455365, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.0002475676, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821238, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: 0.81005245, y: -0.5863574, z: 0, w: -1}
  - {x: 0.81005245, y: -0.58635736, z: 0, w: -1}
  - {x: 0.8100525, y: -0.58635736, z: 0, w: -1}
  - {x: 0.8100525, y: -0.58635736, z: 0, w: -1}
  - {x: 0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: 0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: 0.9508414, y: -0.30967814, z: 0, w: -1}
  - {x: 0.9508414, y: -0.30967814, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: 0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: 0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: 0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: 0.8100525, y: 0.58635736, z: 0, w: -1}
  - {x: 0.81005245, y: 0.5863574, z: 0, w: -1}
  - {x: 0.8100525, y: 0.58635736, z: 0, w: -1}
  - {x: 0.8100525, y: 0.58635736, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.58748484, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898264, y: 0.9510676, z: 0, w: -1}
  - {x: 0.30898267, y: 0.9510676, z: 0, w: -1}
  - {x: 0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: 0, y: 1, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: 0.9510676, z: 0, w: -1}
  - {x: -0.5874849, y: 0.8092352, z: 0, w: -1}
  - {x: -0.5874849, y: 0.80923516, z: 0, w: -1}
  - {x: -0.58748484, y: 0.80923516, z: 0, w: -1}
  - {x: -0.58748484, y: 0.8092352, z: 0, w: -1}
  - {x: -0.8100525, y: 0.58635736, z: 0, w: -1}
  - {x: -0.8100525, y: 0.58635736, z: 0, w: -1}
  - {x: -0.81005245, y: 0.5863574, z: 0, w: -1}
  - {x: -0.81005245, y: 0.5863574, z: 0, w: -1}
  - {x: -0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: -0.9508414, y: 0.30967817, z: 0, w: -1}
  - {x: -0.9508414, y: 0.30967814, z: 0, w: -1}
  - {x: -0.9508414, y: 0.30967814, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: -0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: -0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: -0.9508414, y: -0.30967817, z: 0, w: -1}
  - {x: -0.81005245, y: -0.5863574, z: 0, w: -1}
  - {x: -0.8100525, y: -0.58635736, z: 0, w: -1}
  - {x: -0.8100525, y: -0.58635736, z: 0, w: -1}
  - {x: -0.81005245, y: -0.5863574, z: 0, w: -1}
  - {x: -0.58748484, y: -0.80923516, z: 0, w: -1}
  - {x: -0.58748484, y: -0.80923516, z: 0, w: -1}
  - {x: -0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: -0.5874849, y: -0.80923516, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510676, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: -0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: 0.3089827, y: -0.9510677, z: 0, w: -1}
  - {x: -1, y: 0.00012287291, z: 0, w: -1}
  - {x: -1, y: 0.00012287291, z: 0, w: -1}
  - {x: -1, y: -0.0000004809441, z: 0, w: -1}
  - {x: -1, y: 0.0002461984, z: 0, w: -1}
  - {x: -1, y: -0.00000048094375, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -1, y: -0.00012383342, z: 0, w: -1}
  - {x: -0.9999982, y: 0.0018993743, z: 0, w: -1}
  - {x: -0.9999982, y: 0.0018993743, z: 0, w: -1}
  - {x: -0.99999964, y: -0.0008730931, z: 0, w: -1}
  - {x: -1, y: -0.0002471577, z: 0, w: -1}
  - {x: -0.9999891, y: 0.0046722027, z: 0, w: -1}
  - {x: -0.99999964, y: 0.00087242376, z: 0, w: -1}
  - {x: -0.9999982, y: -0.0019001497, z: 0, w: -1}
  - {x: -0.9999982, y: -0.0019001497, z: 0, w: -1}
  - {x: -1, y: 0.00000010252491, z: 0, w: -1}
  - {x: -1, y: 0.00000010252491, z: 0, w: -1}
  - {x: -0.9999817, y: -0.0060560717, z: 0, w: -1}
  - {x: -0.9999891, y: -0.0046730796, z: 0, w: -1}
  - {x: -0.9999817, y: 0.0060562696, z: 0, w: -1}
  - {x: -0.9999817, y: 0.0060554394, z: 0, w: -1}
  - {x: -0.9999817, y: 0.0060554394, z: 0, w: -1}
  - {x: -0.9999817, y: 0.0060554394, z: 0, w: -1}
  - {x: -0.9999982, y: -0.0018991522, z: 0, w: -1}
  - {x: -0.9999982, y: -0.0018991522, z: 0, w: -1}
  - {x: -0.9999891, y: -0.0046723736, z: 0, w: -1}
  - {x: -0.9999817, y: -0.00605544, z: 0, w: -1}
  - {x: -0.99999976, y: -0.0006909788, z: 0, w: -1}
  - {x: -0.99999976, y: -0.0006909788, z: 0, w: -1}
  - {x: -0.99999964, y: 0.0008737123, z: 0, w: -1}
  - {x: -0.9999891, y: 0.004673817, z: 0, w: -1}
  - {x: -1, y: -0.00012269088, z: 0, w: -1}
  - {x: -1, y: -0.00012269088, z: 0, w: -1}
  - {x: -1, y: -0.0002459546, z: 0, w: -1}
  - {x: -0.99999964, y: -0.00087162136, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -0.99999994, y: -0.00031219254, z: 0, w: -1}
  - {x: -1, y: 0.00000060117986, z: 0, w: -1}
  - {x: -1, y: 0.0002471342, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: -1, y: 0.0000006011799, z: 0, w: -1}
  - {x: 1, y: 0.0000002404723, z: 0, w: -1}
  - {x: 1, y: -0.00012317838, z: 0, w: -1}
  - {x: 1, y: -0.00012317838, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00012382158, z: 0, w: -1}
  - {x: 1, y: 0.00000024047225, z: 0, w: -1}
  - {x: 1, y: -0.00024656887, z: 0, w: -1}
  - {x: 1, y: 0.0002473744, z: 0, w: -1}
  - {x: 0.99999964, y: 0.00087250856, z: 0, w: -1}
  - {x: 0.9999982, y: -0.0019000352, z: 0, w: -1}
  - {x: 0.9999982, y: -0.0019000352, z: 0, w: -1}
  - {x: 0.9999982, y: 0.0019002897, z: 0, w: -1}
  - {x: 0.9999982, y: 0.0019002897, z: 0, w: -1}
  - {x: 0.99999964, y: -0.00087225146, z: 0, w: -1}
  - {x: 0.9999891, y: -0.004672935, z: 0, w: -1}
  - {x: 0.9999891, y: 0.0046731895, z: 0, w: -1}
  - {x: 0.9999817, y: 0.0060550594, z: 0, w: -1}
  - {x: 1, y: -0.0000009003424, z: 0, w: -1}
  - {x: 1, y: -0.0000009003424, z: 0, w: -1}
  - {x: 0.9999817, y: -0.006055402, z: 0, w: -1}
  - {x: 0.9999817, y: -0.006055402, z: 0, w: -1}
  - {x: 0.9999817, y: -0.006055402, z: 0, w: -1}
  - {x: 0.9999817, y: -0.0060568647, z: 0, w: -1}
  - {x: 0.99999976, y: 0.0006909079, z: 0, w: -1}
  - {x: 0.99999976, y: 0.0006909079, z: 0, w: -1}
  - {x: 0.9999817, y: 0.0060554016, z: 0, w: -1}
  - {x: 0.9999891, y: 0.0046719355, z: 0, w: -1}
  - {x: 0.9999891, y: 0.0046719355, z: 0, w: -1}
  - {x: 0.9999891, y: 0.0046719355, z: 0, w: -1}
  - {x: 0.9999891, y: -0.0046739276, z: 0, w: -1}
  - {x: 0.99999964, y: -0.00087389536, z: 0, w: -1}
  - {x: 0.99999994, y: -0.0003141284, z: 0, w: -1}
  - {x: 0.99999994, y: -0.0003141284, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 0.99999964, y: 0.0008708191, z: 0, w: -1}
  - {x: 1, y: 0.0002455365, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.00012433904, z: 0, w: -1}
  - {x: 1, y: -0.0002475676, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821241, z: 0, w: -1}
  - {x: 1, y: -0.0000010821238, z: 0, w: -1}
  - {x: 0.000023135837, y: 0.9351284, z: 0.35430908, w: -1}
  - {x: 0.000023154902, y: 0.9350177, z: 0.35460106, w: -1}
  - {x: 0.00002343735, y: 0.9352388, z: 0.35401767, w: -1}
  - {x: 0.000024040357, y: 0.9354592, z: 0.35343477, w: -1}
  - {x: 0.000023578566, y: 0.93534917, z: 0.35372594, w: -1}
  - {x: -0.000004098204, y: 0.4093515, z: 0.9123768, w: -1}
  - {x: -0.0000040997716, y: 0.40857157, z: 0.91272634, w: -1}
  - {x: -0.0000040966297, y: 0.41013026, z: 0.912027, w: -1}
  - {x: -0.0000040934788, y: 0.41168705, z: 0.91132534, w: -1}
  - {x: -0.000004095057, y: 0.41090924, z: 0.9116763, w: -1}
  - {x: 0.0030285544, y: -0.41125792, z: 0.911514, w: -1}
  - {x: 0.003028386, y: -0.41168514, z: 0.91132116, w: -1}
  - {x: 0.0030294075, y: -0.4108293, z: 0.9117073, w: -1}
  - {x: 0.0030311123, y: -0.4099718, z: 0.9120932, w: -1}
  - {x: 0.003029917, y: -0.41040125, z: 0.91190004, w: -1}
  - {x: 0.0000038460294, y: -0.934474, z: 0.35603136, w: -1}
  - {x: 0.000003841665, y: -0.9342929, z: 0.3565065, w: -1}
  - {x: 0.0000035288078, y: -0.9346555, z: 0.35555467, w: -1}
  - {x: 0.000002894366, y: -0.9350177, z: 0.3546011, w: -1}
  - {x: 0.0000033723775, y: -0.93483645, z: 0.35507867, w: -1}
  - {x: -0.0000038472294, y: -0.934766, z: -0.35526416, w: -1}
  - {x: -0.0000038440658, y: -0.9348769, z: -0.35497206, w: -1}
  - {x: -0.0000035290016, y: -0.9346547, z: -0.3555568, w: -1}
  - {x: -0.000002892551, y: -0.9344319, z: -0.35614198, w: -1}
  - {x: -0.0000033714714, y: -0.93454343, z: -0.35584912, w: -1}
  - {x: 0.0000010251209, y: -0.4131673, z: -0.9106552, w: -1}
  - {x: 0.0000020502353, y: -0.41239014, z: -0.9110074, w: -1}
  - {x: -0.00000068127935, y: -0.41394323, z: -0.9103027, w: -1}
  - {x: -0.0000040941177, y: -0.41549438, z: -0.9095958, w: -1}
  - {x: -0.0000020470545, y: -0.41471937, z: -0.90994936, w: -1}
  - {x: 0, y: 0.41401392, z: -0.9102706, w: -1}
  - {x: 0, y: 0.4147881, z: -0.90991807, w: -1}
  - {x: 0.0000013644989, y: 0.41323856, z: -0.91062284, w: -1}
  - {x: 0.0000040934774, y: 0.41168702, z: -0.9113253, w: -1}
  - {x: 0.000002046751, y: 0.41246337, z: -0.9109742, w: -1}
  - {x: 0.023787117, y: 0.9348639, z: -0.3542085, w: -1}
  - {x: 0.02378844, y: 0.9347531, z: -0.3545007, w: -1}
  - {x: 0.02378516, y: 0.9349743, z: -0.35391724, w: -1}
  - {x: 0.023781236, y: 0.9351947, z: -0.3533348, w: -1}
  - {x: 0.023783516, y: 0.9350847, z: -0.35362557, w: -1}
  - {x: 0.25544146, y: 0.84309846, z: -0.47321737, w: -1}
  - {x: 0.11035508, y: 0.8404677, z: -0.53050536, w: -1}
  - {x: 0.25544146, y: 0.84309846, z: -0.47321737, w: -1}
  - {x: 0.39430875, y: 0.8251723, z: -0.404489, w: -1}
  - {x: 0.027027175, y: 0.96990126, z: 0.2419939, w: -1}
  - {x: 0.056837454, y: 0.9686085, z: 0.24200635, w: -1}
  - {x: 0.027027175, y: 0.96990126, z: 0.2419939, w: -1}
  - {x: 0, y: 0.97017694, z: 0.24239796, w: -1}
  - {x: 0.28242907, y: 0.95758635, z: 0.057116535, w: -1}
  - {x: 0.23937029, y: 0.97035664, z: 0.033315435, w: -1}
  - {x: 0.19802502, y: 0.9801437, z: 0.010216918, w: -1}
  - {x: 0.23937029, y: 0.97035664, z: 0.033315435, w: -1}
  - {x: 0.25110307, y: 0.94347066, z: 0.21635707, w: -1}
  - {x: 0.10779169, y: 0.9795319, z: 0.16999502, w: -1}
  - {x: 0.25110307, y: 0.94347066, z: 0.21635707, w: -1}
  - {x: 0.38862994, y: 0.88471293, z: 0.25739035, w: -1}
  - {x: 0.17878298, y: 0.97176147, z: 0.15400104, w: -1}
  - {x: 0.3467783, y: 0.9187933, z: 0.18858324, w: -1}
  - {x: 0.17878298, y: 0.97176147, z: 0.15400104, w: -1}
  - {x: 0, y: 0.99332875, z: 0.115316726, w: -1}
  - {x: 0.3274055, y: 0.9380745, z: 0.11323389, w: -1}
  - {x: 0.37776208, y: 0.91965944, z: 0.10734207, w: -1}
  - {x: 0.42951587, y: 0.89735836, z: 0.10131221, w: -1}
  - {x: 0.37776208, y: 0.91965944, z: 0.10734207, w: -1}
  - {x: 0.38660052, y: 0.91899276, z: 0.07741026, w: -1}
  - {x: 0.31115133, y: 0.9478364, z: 0.0692177, w: -1}
  - {x: 0.38660052, y: 0.91899276, z: 0.07741026, w: -1}
  - {x: 0.46257177, y: 0.8825235, z: 0.08473351, w: -1}
  - {x: 0.19784147, y: 0.9793284, z: 0.042127237, w: -1}
  - {x: 0.38530594, y: 0.92275727, z: 0.0076409224, w: -1}
  - {x: 0.19784147, y: 0.9793284, z: 0.042127237, w: -1}
  - {x: 0, y: 0.99726987, z: 0.0738437, w: -1}
  - {x: 0.25712568, y: 0.91592264, z: 0.30817562, w: -1}
  - {x: 0.36904767, y: 0.8900729, z: 0.26753324, w: -1}
  - {x: 0.47757798, y: 0.84992737, z: 0.22258224, w: -1}
  - {x: 0.36904767, y: 0.8900729, z: 0.26753324, w: -1}
  - {x: -0.24831717, y: 0.30714345, z: -0.91869557, w: -1}
  - {x: -0.09406142, y: 0.2629435, z: -0.96021515, w: -1}
  - {x: -0.24831717, y: 0.30714345, z: -0.91869557, w: -1}
  - {x: -0.39576745, y: 0.3428722, z: -0.85194296, w: -1}
  - {x: -0.20284918, y: 0.08201602, z: -0.9757692, w: -1}
  - {x: -0.39331618, y: 0.11704583, z: -0.9119225, w: -1}
  - {x: -0.20284918, y: 0.08201602, z: -0.9757692, w: -1}
  - {x: 0, y: 0.0449459, z: -0.99898946, w: -1}
  - {x: -0.34494737, y: 0.011981696, z: -0.9385456, w: -1}
  - {x: -0.41325882, y: 0.0048859995, z: -0.9106005, w: -1}
  - {x: -0.48178068, y: -0.001993776, z: -0.8762896, w: -1}
  - {x: -0.41325882, y: 0.0048859995, z: -0.9106005, w: -1}
  - {x: -0.36458007, y: 0.5642986, z: -0.74070805, w: -1}
  - {x: -0.23193103, y: 0.5729368, z: -0.78609896, w: -1}
  - {x: -0.36458007, y: 0.5642986, z: -0.74070805, w: -1}
  - {x: -0.48894694, y: 0.54437214, z: -0.6816084, w: -1}
  - {x: -0.21744071, y: 0.55899227, z: -0.8001545, w: -1}
  - {x: -0.42313525, y: 0.53872377, z: -0.72851443, w: -1}
  - {x: -0.21744071, y: 0.55899227, z: -0.8001545, w: -1}
  - {x: 0, y: 0.55344754, z: -0.832884, w: -1}
  - {x: -0.33392757, y: 0.73208576, z: -0.5937532, w: -1}
  - {x: -0.39410856, y: 0.7116195, z: -0.58161515, w: -1}
  - {x: -0.45539567, y: 0.68734455, z: -0.56583774, w: -1}
  - {x: -0.39410856, y: 0.7116195, z: -0.58161515, w: -1}
  - {x: -0.38494432, y: 0.84399486, z: -0.37348443, w: -1}
  - {x: -0.27799764, y: 0.8781032, z: -0.38942546, w: -1}
  - {x: -0.38494432, y: 0.84399486, z: -0.37348443, w: -1}
  - {x: -0.48457053, y: 0.7999875, z: -0.35385224, w: -1}
  - {x: -0.20559958, y: 0.76190287, z: -0.61419284, w: -1}
  - {x: -0.40344247, y: 0.7120505, z: -0.57464623, w: -1}
  - {x: -0.20559958, y: 0.76190287, z: -0.61419284, w: -1}
  - {x: 0, y: 0.77818954, z: -0.6280295, w: -1}
  - {x: 0.26954842, y: -0.5472779, z: -0.79235756, w: -1}
  - {x: 0.19403139, y: -0.5702252, z: -0.798245, w: -1}
  - {x: 0.11396952, y: -0.5909353, z: -0.79862785, w: -1}
  - {x: 0.19403139, y: -0.5702252, z: -0.798245, w: -1}
  - {x: -0.30540028, y: 0.8010244, z: -0.5148695, w: -1}
  - {x: -0.32137686, y: 0.79255253, z: -0.51824456, w: -1}
  - {x: -0.30540028, y: 0.8010244, z: -0.5148695, w: -1}
  - {x: -0.29026058, y: 0.8088517, z: -0.51137835, w: -1}
  - {x: 0.07068693, y: 0.08811539, z: -0.993599, w: -1}
  - {x: 0.14683715, y: 0.08805471, z: -0.9852336, w: -1}
  - {x: 0.07068693, y: 0.08811539, z: -0.993599, w: -1}
  - {x: 0, y: 0.08901968, z: -0.9960299, w: -1}
  - {x: 0.35028362, y: -0.46867537, z: -0.8109531, w: -1}
  - {x: 0.37880635, y: -0.45534253, z: -0.8057102, w: -1}
  - {x: 0.40559635, y: -0.4420092, z: -0.8000747, w: -1}
  - {x: 0.37880635, y: -0.45534253, z: -0.8057102, w: -1}
  - {x: 0.10987432, y: 0.63404673, z: -0.76544917, w: -1}
  - {x: -0.029143449, y: 0.58329976, z: -0.811734, w: -1}
  - {x: 0.10987432, y: 0.63404673, z: -0.76544917, w: -1}
  - {x: 0.2451364, y: 0.6697266, z: -0.700981, w: -1}
  - {x: -0.11700257, y: 0.9775193, z: 0.17540379, w: -1}
  - {x: -0.24091569, y: 0.9554981, z: 0.17024411, w: -1}
  - {x: -0.11700257, y: 0.9775193, z: 0.17540379, w: -1}
  - {x: 0, y: 0.9844953, z: 0.17541134, w: -1}
  - {x: 0.041241944, y: 0.9874582, z: 0.15239881, w: -1}
  - {x: -0.08717743, y: 0.9924425, z: 0.086359516, w: -1}
  - {x: -0.21274647, y: 0.97693014, z: 0.018611865, w: -1}
  - {x: -0.08717743, y: 0.9924425, z: 0.086359516, w: -1}
  - {x: 0, y: -0.95775783, z: -0.2875761, w: -1}
  - {x: 0, y: -0.95775783, z: -0.2875761, w: -1}
  - {x: 0, y: -0.8314242, z: -0.55563825, w: -1}
  - {x: 0, y: -0.8314242, z: -0.5556382, w: -1}
  - {x: 0, y: -0.8314242, z: -0.55563825, w: -1}
  - {x: 0, y: -0.8314242, z: -0.55563825, w: -1}
  - {x: 0, y: -0.6197191, z: -0.7848237, w: -1}
  - {x: 0, y: -0.619719, z: -0.7848238, w: -1}
  - {x: 0, y: -0.6197191, z: -0.7848237, w: -1}
  - {x: 0, y: -0.6197191, z: -0.7848237, w: -1}
  - {x: 0, y: -0.3320361, z: -0.9432667, w: -1}
  - {x: 0, y: -0.3320361, z: -0.9432667, w: -1}
  - {x: 0, y: -0.33203614, z: -0.9432667, w: -1}
  - {x: 0, y: -0.33203614, z: -0.9432667, w: -1}
  - {x: 0, y: 0.00016934788, z: -1, w: -1}
  - {x: 0, y: 0.00016934788, z: -1, w: -1}
  - {x: 0, y: 0.00016934788, z: -1, w: -1}
  - {x: 0, y: 0.00016934788, z: -1, w: -1}
  - {x: 0, y: 0.33219585, z: -0.9432105, w: -1}
  - {x: 0, y: 0.33219585, z: -0.9432105, w: -1}
  - {x: 0, y: 0.3321958, z: -0.9432105, w: -1}
  - {x: 0, y: 0.3321958, z: -0.9432105, w: -1}
  - {x: 0, y: 0.61870855, z: -0.78562057, w: -1}
  - {x: 0, y: 0.61870855, z: -0.78562057, w: -1}
  - {x: 0, y: 0.6187085, z: -0.7856206, w: -1}
  - {x: 0, y: 0.61870855, z: -0.78562057, w: -1}
  - {x: 0, y: 0.83158237, z: -0.5554014, w: -1}
  - {x: 0, y: 0.8315824, z: -0.5554014, w: -1}
  - {x: 0, y: 0.8315824, z: -0.5554014, w: -1}
  - {x: 0, y: 0.8315824, z: -0.5554014, w: -1}
  - {x: 0, y: 0.9581329, z: -0.28632355, w: -1}
  - {x: 0, y: 0.9581329, z: -0.2863236, w: -1}
  - {x: 0, y: 0.9581329, z: -0.2863236, w: -1}
  - {x: 0, y: 0.9581329, z: -0.2863236, w: -1}
  - {x: 0, y: 0.99999934, z: -0.0011678226, w: -1}
  - {x: 0, y: 0.99999934, z: -0.0011678226, w: -1}
  - {x: 0, y: 0.99999934, z: -0.0011678529, w: -1}
  - {x: 0, y: 0.99999934, z: -0.0011678529, w: -1}
  - {x: 0, y: 0.9584667, z: 0.2852044, w: -1}
  - {x: 0, y: 0.9584667, z: 0.2852044, w: -1}
  - {x: 0, y: 0.9584667, z: 0.2852044, w: -1}
  - {x: 0, y: 0.9584667, z: 0.2852044, w: -1}
  - {x: 0, y: 0.83080214, z: 0.55656797, w: -1}
  - {x: 0, y: 0.83080214, z: 0.55656797, w: -1}
  - {x: 0, y: 0.83080214, z: 0.556568, w: -1}
  - {x: 0, y: 0.83080214, z: 0.556568, w: -1}
  - {x: 0, y: 0.6193192, z: 0.7851393, w: -1}
  - {x: 0, y: 0.6193192, z: 0.7851393, w: -1}
  - {x: 0, y: 0.6193192, z: 0.78513926, w: -1}
  - {x: 0, y: 0.6193192, z: 0.78513926, w: -1}
  - {x: 0, y: 0.33409262, z: 0.9425402, w: -1}
  - {x: 0, y: 0.33409262, z: 0.9425402, w: -1}
  - {x: 0, y: 0.33409262, z: 0.9425402, w: -1}
  - {x: 0, y: 0.33409262, z: 0.9425402, w: -1}
  - {x: 0, y: -0.00016934788, z: 1, w: -1}
  - {x: 0, y: -0.00016934788, z: 1, w: -1}
  - {x: 0, y: -0.00016934788, z: 1, w: -1}
  - {x: 0, y: -0.00016934788, z: 1, w: -1}
  - {x: 0, y: -0.33425227, z: 0.9424836, w: -1}
  - {x: 0, y: -0.33425227, z: 0.9424836, w: -1}
  - {x: 0, y: -0.33425224, z: 0.9424836, w: -1}
  - {x: 0, y: -0.3342522, z: 0.9424836, w: -1}
  - {x: 0, y: -0.62032956, z: 0.78434134, w: -1}
  - {x: 0, y: -0.62032956, z: 0.7843412, w: -1}
  - {x: 0, y: -0.62032956, z: 0.7843413, w: -1}
  - {x: 0, y: -0.62032956, z: 0.7843413, w: -1}
  - {x: 0, y: -0.8315504, z: 0.5554493, w: -1}
  - {x: 0, y: -0.8315504, z: 0.55544925, w: -1}
  - {x: 0, y: -0.8315504, z: 0.5554493, w: -1}
  - {x: 0, y: -0.8315504, z: 0.5554493, w: -1}
  - {x: 0, y: -0.95822537, z: 0.28601435, w: -1}
  - {x: 0, y: -0.95822537, z: 0.28601435, w: -1}
  - {x: 0, y: -0.95822537, z: 0.28601435, w: -1}
  - {x: 0, y: -0.95822537, z: 0.28601435, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -1, z: 0, w: -1}
  - {x: 0, y: -0.95775783, z: -0.2875761, w: -1}
  - {x: 0, y: -0.95775783, z: -0.2875761, w: -1}
  - {x: 0, y: -0.0008914494, z: 0.99999964, w: -1}
  - {x: 0, y: -0.0008914494, z: 0.99999964, w: -1}
  - {x: 0, y: -0.00124663, z: 0.9999992, w: -1}
  - {x: 0, y: -0.00053596374, z: 0.9999999, w: -1}
  - {x: 0, y: -0.0012466302, z: 0.9999992, w: -1}
  - {x: 0, y: -0.002103508, z: 0.9999978, w: -1}
  - {x: 0, y: -0.002103508, z: 0.9999978, w: -1}
  - {x: 0, y: -0.0017745261, z: 0.99999845, w: -1}
  - {x: 0, y: -0.0017745261, z: 0.99999845, w: -1}
  - {x: 0, y: -0.0012774578, z: 0.9999992, w: -1}
  - {x: 0, y: -0.0029605823, z: 0.99999565, w: -1}
  - {x: 0, y: -0.0022726548, z: 0.99999744, w: -1}
  - {x: 0, y: 0.0003868886, z: 0.99999994, w: -1}
  - {x: 0, y: -0.0005895748, z: 0.9999998, w: -1}
  - {x: 0, y: -0.0005895748, z: 0.9999998, w: -1}
  - {x: 0, y: 0.00000010790628, z: 1, w: -1}
  - {x: 0, y: 0.00000010790628, z: 1, w: -1}
  - {x: 0, y: -0.00050591433, z: 0.9999999, w: -1}
  - {x: 0, y: -0.0015634994, z: 0.9999988, w: -1}
  - {x: 0, y: 0.0005051114, z: 0.9999999, w: -1}
  - {x: 0, y: 0.0005042585, z: 0.9999999, w: -1}
  - {x: 0, y: 0.0005042585, z: 0.9999999, w: -1}
  - {x: 0, y: 0.0005042585, z: 0.9999999, w: -1}
  - {x: 0, y: 0.00014397476, z: 1, w: -1}
  - {x: 0, y: 0.00014397476, z: 1, w: -1}
  - {x: 0, y: 0.0022718166, z: 0.99999744, w: -1}
  - {x: 0, y: -0.0005051755, z: 0.9999999, w: -1}
  - {x: 0, y: 0.00052998675, z: 0.9999999, w: -1}
  - {x: 0, y: 0.00052998675, z: 0.9999999, w: -1}
  - {x: 0, y: -0.001979, z: 0.99999803, w: -1}
  - {x: 0, y: 0.0015638577, z: 0.9999988, w: -1}
  - {x: 0, y: 0.0015131738, z: 0.99999887, w: -1}
  - {x: 0, y: 0.0015131738, z: 0.99999887, w: -1}
  - {x: 0, y: 0.0017802909, z: 0.99999845, w: -1}
  - {x: 0, y: 0.0028798918, z: 0.9999959, w: -1}
  - {x: 0, y: 0.0011106188, z: 0.9999994, w: -1}
  - {x: 0, y: 0.0011106188, z: 0.9999994, w: -1}
  - {x: 0, y: 0.001245985, z: 0.9999992, w: -1}
  - {x: 0, y: -0.0006562432, z: 0.9999998, w: -1}
  - {x: 0, y: 0.0012459828, z: 0.9999992, w: -1}
  - {x: 0, y: 0.0012459828, z: 0.9999992, w: -1}
  - {x: 0, y: 0.0012459828, z: 0.9999992, w: -1}
  - {x: 0, y: 0.0012463675, z: -0.9999992, w: -1}
  - {x: 0, y: 0.0008911159, z: -0.99999964, w: -1}
  - {x: 0, y: 0.0008911159, z: -0.99999964, w: -1}
  - {x: 0, y: 0.0021034945, z: -0.9999978, w: -1}
  - {x: 0, y: 0.0021034945, z: -0.9999978, w: -1}
  - {x: 0, y: 0.0012463677, z: -0.9999992, w: -1}
  - {x: 0, y: 0.0005355594, z: -0.9999999, w: -1}
  - {x: 0, y: 0.0029608184, z: -0.99999565, w: -1}
  - {x: 0, y: 0.0012768216, z: -0.9999992, w: -1}
  - {x: 0, y: 0.0017738072, z: -0.99999845, w: -1}
  - {x: 0, y: 0.0017738072, z: -0.99999845, w: -1}
  - {x: 0, y: 0.0005897286, z: -0.9999998, w: -1}
  - {x: 0, y: 0.0005897286, z: -0.9999998, w: -1}
  - {x: 0, y: -0.0003867004, z: -0.99999994, w: -1}
  - {x: 0, y: 0.0022718522, z: -0.99999744, w: -1}
  - {x: 0, y: 0.0015636197, z: -0.9999988, w: -1}
  - {x: 0, y: 0.00050481065, z: -0.9999999, w: -1}
  - {x: 0, y: -0.0000009830832, z: -1, w: -1}
  - {x: 0, y: -0.0000009830832, z: -1, w: -1}
  - {x: 0, y: -0.000504217, z: -0.9999999, w: -1}
  - {x: 0, y: -0.000504217, z: -0.9999999, w: -1}
  - {x: 0, y: -0.000504217, z: -0.9999999, w: -1}
  - {x: 0, y: -0.00050575956, z: -0.9999999, w: -1}
  - {x: 0, y: -0.00053006713, z: -0.9999999, w: -1}
  - {x: 0, y: -0.00053006713, z: -0.9999999, w: -1}
  - {x: 0, y: 0.00050513394, z: -0.9999999, w: -1}
  - {x: 0, y: -0.0022722962, z: -0.99999744, w: -1}
  - {x: 0, y: -0.0022722962, z: -0.99999744, w: -1}
  - {x: 0, y: -0.0022722962, z: -0.99999744, w: -1}
  - {x: 0, y: -0.0015639781, z: -0.9999988, w: -1}
  - {x: 0, y: 0.0019788013, z: -0.99999803, w: -1}
  - {x: 0, y: 0.00009985822, z: -1, w: -1}
  - {x: 0, y: 0.00009985822, z: -1, w: -1}
  - {x: 0, y: -0.0028807658, z: -0.9999959, w: -1}
  - {x: 0, y: -0.0028807658, z: -0.9999959, w: -1}
  - {x: 0, y: -0.0028807658, z: -0.9999959, w: -1}
  - {x: 0, y: -0.0017807481, z: -0.99999845, w: -1}
  - {x: 0, y: -0.00029550897, z: -1, w: -1}
  - {x: 0, y: -0.00029550897, z: -1, w: -1}
  - {x: 0, y: 0.00065576873, z: -0.9999998, w: -1}
  - {x: 0, y: -0.0012465097, z: -0.9999992, w: -1}
  - {x: 0, y: -0.0012465097, z: -0.9999992, w: -1}
  - {x: 0, y: -0.0012465097, z: -0.9999992, w: -1}
  - {x: 0, y: -0.0012465059, z: -0.9999992, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 1285
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &5228820311652149913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 3734770876508798978
  m_Size: {x: 1, y: 1, z: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1238
  m_ShapeBox:
    m_Center: {x: 0.2733612, y: 0.22804251, z: 0.26327518}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  references:
    version: 2
    RefIds:
    - rid: 3734770876508798978
      type: {class: Cylinder, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data:
        m_AxisDivisions: 8
        m_HeightCuts: 0
        m_Smooth: 0
--- !u!23 &1310716941597793860
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7fff5185359246d4d97ac337fc55066c, type: 2}
  - {fileID: 2100000, guid: 478606fcd0fe8e84581891a752b5364a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4102158821355279633
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.5432739, z: 0.05}
  m_Center: {x: 0.07, y: 0, z: 0}
--- !u!54 &265917788973944225
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &2402978863349888236
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1783056023, guid: 661092b4961be7145bfbe56e1e62337b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AllowCollisionOwnershipTransfer: 1
--- !u!114 &8529164691690629047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0adaeabeb33320b4482457823cbb5f5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  _udonSharpBackingUdonBehaviour: {fileID: 1617607676671781162}
  Damage: 25
  DamageCache: 0
  OriginalDamage: 0
  DamageType: 0
  CanPenetrateIntestellarArmor: 0
  IsLocalDamage: 0
  PlayerSystemObject: {fileID: 0}
  playerSystem: {fileID: 0}
--- !u!114 &1617607676671781162
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45115577ef41a5b4ca741ed302693907, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactTextPlacement: {fileID: 0}
  interactText: Use
  interactTextGO: {fileID: 0}
  proximity: 2
  SynchronizePosition: 0
  AllowCollisionOwnershipTransfer: 0
  Reliable: 0
  _syncMethod: 1
  serializedProgramAsset: {fileID: 11400000, guid: 059c5e8c4a929f9439882dcf91b5d106,
    type: 2}
  programSource: {fileID: 11400000, guid: ce93491881936a44d8ce008050009f2e, type: 2}
  serializedPublicVariablesBytesString: Ai8AAAAAATIAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAFQAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AAAAAAAYBAAAAAAAAACcBBAAAAHQAeQBwAGUAAWgAAABTAHkAcwB0AGUAbQAuAEMAbwBsAGwAZQBjAHQAaQBvAG4AcwAuAEcAZQBuAGUAcgBpAGMALgBMAGkAcwB0AGAAMQBbAFsAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4ALgBJAG4AdABlAHIAZgBhAGMAZQBzAC4ASQBVAGQAbwBuAFYAYQByAGkAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AXQBdACwAIABtAHMAYwBvAHIAbABpAGIAAQEJAAAAVgBhAHIAaQBhAGIAbABlAHMALwEAAAABaAAAAFMAeQBzAHQAZQBtAC4AQwBvAGwAbABlAGMAdABpAG8AbgBzAC4ARwBlAG4AZQByAGkAYwAuAEwAaQBzAHQAYAAxAFsAWwBWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAEkAbgB0AGUAcgBmAGEAYwBlAHMALgBJAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlACwAIABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgBdAF0ALAAgAG0AcwBjAG8AcgBsAGkAYgABAAAABgEAAAAAAAAAAi8CAAAAAUkAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAGAAMQBbAFsAUwB5AHMAdABlAG0ALgBJAG4AdAAzADIALAAgAG0AcwBjAG8AcgBsAGkAYgBdAF0ALAAgAFYAUgBDAC4AVQBkAG8AbgAuAEMAbwBtAG0AbwBuAAIAAAAGAgAAAAAAAAAnAQQAAAB0AHkAcABlAAEXAAAAUwB5AHMAdABlAG0ALgBTAHQAcgBpAG4AZwAsACAAbQBzAGMAbwByAGwAaQBiACcBCgAAAFMAeQBtAGIAbwBsAE4AYQBtAGUAAR8AAABfAF8AXwBVAGQAbwBuAFMAaABhAHIAcABCAGUAaABhAHYAaQBvAHUAcgBWAGUAcgBzAGkAbwBuAF8AXwBfACcBBAAAAHQAeQBwAGUAARYAAABTAHkAcwB0AGUAbQAuAEkAbgB0ADMAMgAsACAAbQBzAGMAbwByAGwAaQBiABcBBQAAAFYAYQBsAHUAZQACAAAABwUHBQcF
  publicVariablesUnityEngineObjects: []
  publicVariablesSerializationDataFormat: 0
--- !u!114 &4920511608281680595
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1804438810, guid: 661092b4961be7145bfbe56e1e62337b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MomentumTransferMethod: 0
  DisallowTheft: 0
  ExactGun: {fileID: 1094651692091364414}
  ExactGrip: {fileID: 0}
  allowManipulationWhenEquipped: 0
  orientation: 2
  AutoHold: 1
  InteractionText: Konrusk Baton
  UseText: Hit players to jail them!
  useEventBroadcastType: 0
  UseDownEventName: 
  UseUpEventName: 
  pickupDropEventBroadcastType: 0
  PickupEventName: 
  DropEventName: 
  ThrowVelocityBoostMinSpeed: 1
  ThrowVelocityBoostScale: 1
  currentlyHeldBy: {fileID: 0}
  pickupable: 0
  proximity: 2
--- !u!33 &5234092477611288778
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7467639311059526379}
  m_Mesh: {fileID: 0}
--- !u!1 &7946525266150535636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 686092620287250105}
  - component: {fileID: 3529743230241939998}
  - component: {fileID: 1739200294784433167}
  - component: {fileID: 4799393259217546530}
  m_Layer: 11
  m_Name: PlayerYeeter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &686092620287250105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7946525266150535636}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 708098044211706455}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3529743230241939998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7946525266150535636}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 323327aa77baa7040bbf833d3430ce60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  _udonSharpBackingUdonBehaviour: {fileID: 1739200294784433167}
  ThrowerType: 1
  Force: 5
  Upwards: 1
  direction: 0
--- !u!114 &1739200294784433167
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7946525266150535636}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45115577ef41a5b4ca741ed302693907, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactTextPlacement: {fileID: 0}
  interactText: Use
  interactTextGO: {fileID: 0}
  proximity: 2
  SynchronizePosition: 0
  AllowCollisionOwnershipTransfer: 0
  Reliable: 0
  _syncMethod: 1
  serializedProgramAsset: {fileID: 11400000, guid: 3ab51f2ce67db6847892b5f005c16add,
    type: 2}
  programSource: {fileID: 11400000, guid: 633490d1f252d484c8ce08f567f77edc, type: 2}
  serializedPublicVariablesBytesString: Ai8AAAAAATIAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAFQAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AAAAAAAYBAAAAAAAAACcBBAAAAHQAeQBwAGUAAWgAAABTAHkAcwB0AGUAbQAuAEMAbwBsAGwAZQBjAHQAaQBvAG4AcwAuAEcAZQBuAGUAcgBpAGMALgBMAGkAcwB0AGAAMQBbAFsAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4ALgBJAG4AdABlAHIAZgBhAGMAZQBzAC4ASQBVAGQAbwBuAFYAYQByAGkAYQBiAGwAZQAsACAAVgBSAEMALgBVAGQAbwBuAC4AQwBvAG0AbQBvAG4AXQBdACwAIABtAHMAYwBvAHIAbABpAGIAAQEJAAAAVgBhAHIAaQBhAGIAbABlAHMALwEAAAABaAAAAFMAeQBzAHQAZQBtAC4AQwBvAGwAbABlAGMAdABpAG8AbgBzAC4ARwBlAG4AZQByAGkAYwAuAEwAaQBzAHQAYAAxAFsAWwBWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAEkAbgB0AGUAcgBmAGEAYwBlAHMALgBJAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlACwAIABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgBdAF0ALAAgAG0AcwBjAG8AcgBsAGkAYgABAAAABgEAAAAAAAAAAi8CAAAAAUkAAABWAFIAQwAuAFUAZABvAG4ALgBDAG8AbQBtAG8AbgAuAFUAZABvAG4AVgBhAHIAaQBhAGIAbABlAGAAMQBbAFsAUwB5AHMAdABlAG0ALgBJAG4AdAAzADIALAAgAG0AcwBjAG8AcgBsAGkAYgBdAF0ALAAgAFYAUgBDAC4AVQBkAG8AbgAuAEMAbwBtAG0AbwBuAAIAAAAGAgAAAAAAAAAnAQQAAAB0AHkAcABlAAEXAAAAUwB5AHMAdABlAG0ALgBTAHQAcgBpAG4AZwAsACAAbQBzAGMAbwByAGwAaQBiACcBCgAAAFMAeQBtAGIAbwBsAE4AYQBtAGUAAR8AAABfAF8AXwBVAGQAbwBuAFMAaABhAHIAcABCAGUAaABhAHYAaQBvAHUAcgBWAGUAcgBzAGkAbwBuAF8AXwBfACcBBAAAAHQAeQBwAGUAARYAAABTAHkAcwB0AGUAbQAuAEkAbgB0ADMAMgAsACAAbQBzAGMAbwByAGwAaQBiABcBBQAAAFYAYQBsAHUAZQACAAAABwUHBQcF
  publicVariablesUnityEngineObjects: []
  publicVariablesSerializationDataFormat: 0
--- !u!65 &4799393259217546530
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7946525266150535636}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.5432739, z: 0.05}
  m_Center: {x: 0.07, y: 0, z: 0}
