﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.AI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CubixMinionAI : UdonSharpBehaviour
{
    public int AImode; //0 is BounceBot, 1 is Bluemon, 2 Is Flying
    public int Debuffmode; //0 is normal, 1 is none
    public float ResizeMin = 0.5f;
    public float ResizeMax = 2f;

    //Bounce Bot Var
    private Rigidbody rb;
    public int force = 50;
    public float Range = 100f;
    public Collider BounceBotCollider;
    public Vector3 SpawnedPosition, TargetPosition;

    //Bounce Sound
    public AudioSource AudioSource;
    public AudioClip BounceSound;

    //Bluemon Var
    public NavMeshAgent agent;
    public float sightRange = 30f;
    public float Cooldown = 1f, DebuffCooldown = 0.5f;
    public LayerMask whatIsPlayer;
    public int State;
    private float speed = 6f;

    //PlaneAI Var
    public float planespeed = 15f;
    public float Turn;

    public GameObject[] Bodies;

    private VRCPlayerApi localPlayer;

    //Extra Required Stuff
    private Vector3 outDirection;

    //Settings
    public bool RandomMode;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        rb = GetComponent<Rigidbody>();

        //Randomized Scale
        float scale = Random.Range(ResizeMin, ResizeMax);
        transform.localScale = new Vector3(scale,scale,scale);

        if(RandomMode == true){AImode = Random.Range(0, 3);}

        SpawnedPosition = transform.position;

        if(AImode == 0){
            Bodies[0].SetActive(true);
            Bodies[1].SetActive(false);
            Bodies[2].SetActive(false);
            SendCustomEventDelayedSeconds(nameof(UpdateTarget), 0.5f);
        }
        if(AImode == 1){
            Bodies[0].SetActive(false);
            Bodies[1].SetActive(true);
            Bodies[2].SetActive(false);
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 5f);
        }
        if(AImode == 2){
            speed = planespeed;
            Bodies[0].SetActive(false);
            Bodies[1].SetActive(false);
            Bodies[2].SetActive(true);
            BounceBotCollider.enabled = false;
            SendCustomEventDelayedSeconds(nameof(PlaneAIInterval), 0.1f);
        }
    }

    void OnEnable()
    {
        SpawnedPosition = transform.position;
        //reset velocity
        if(rb != null){rb.velocity = Vector3.zero;}
    }

    public void UpdateTarget()
    {
        if (localPlayer == null) return;

        if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < Range){TargetPosition = localPlayer.GetPosition();}
        else{TargetPosition = SpawnedPosition;}

        if(Vector3.Distance(transform.position, SpawnedPosition) > Range && Vector3.Distance(transform.position, TargetPosition) > Range){transform.position = SpawnedPosition; rb.velocity = Vector3.zero;}

        SendCustomEventDelayedSeconds(nameof(UpdateTarget), 0.5f);
    }


    //Bluemon Code
    public void UpdateDestination()
    {
        rb.isKinematic = true;
        agent.enabled = true;

        if (!agent.isOnNavMesh){Destroy(gameObject);}

        bool playerInSightRange = Physics.CheckSphere(transform.position, sightRange, whatIsPlayer);

        if (playerInSightRange) { State = 1; }
        else if (!playerInSightRange) { State = 0; }

        agent.speed = speed;

        // Adjust update timing dynamically
        if(State == 0){
            SetRandomDestination();
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 1f);
        }
        else if (State == 1){
            TargetedDestination();
            SendCustomEventDelayedSeconds(nameof(UpdateDestination), 0.1f);
        }
    }
    void SetRandomDestination()
    {
        if (!agent.isOnNavMesh) return;

        Vector3 targetPosition = Random.insideUnitSphere * 100f + transform.position;
        agent.SetDestination(targetPosition);
    }
    void TargetedDestination()
    {
        if (!agent.isOnNavMesh) return;

        agent.SetDestination(localPlayer.GetPosition());
    }

    public void ResetCooldown(){Cooldown = 1f;}

    //PlaneAI
    public void PlaneAIInterval(){
        if (localPlayer.GetPosition() != null)
        {
            if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < Range){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                if(rb.isKinematic == false){rb.velocity = transform.forward * speed;}
            }
            else{
                Vector3 direction = (SpawnedPosition - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                if(rb.isKinematic == false){rb.velocity = transform.forward * speed;}
            }
        }
        SendCustomEventDelayedSeconds(nameof(PlaneAIInterval), 0.03f);
    }


    public void Delete(){Destroy(gameObject);}

    //when collision happens
    private void OnCollisionEnter(Collision collision)
    {
        Vector3 PlayerDirection = localPlayer.GetPosition();
        Vector3 inDirection = collision.contacts[0].normal;
        if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < Range){outDirection = -inDirection + (transform.position - localPlayer.GetPosition()).normalized*0.3f;}
        else{outDirection = -inDirection + (transform.position - SpawnedPosition).normalized*0.3f;}
        int layer = collision.gameObject.layer;

        rb.AddForce(outDirection * -force, ForceMode.Impulse);

        AudioSource.PlayOneShot(BounceSound);
    }

    // Corrected Udon-compatible trigger event
    //public override void OnPlayerCollisionEnter(VRCPlayerApi player)
    //{
        //if(player == localPlayer && AImode == 0){
        //    Vector3 triggerPosition = transform.position + new Vector3(0,-1,0);         
        //    Vector3 directionToPlayer = TargetPosition - triggerPosition;
        //    Vector3 oppositeDirection = -directionToPlayer.normalized;

        //    localPlayer.SetVelocity(oppositeDirection * 15);
        //}
    //}
    public override void OnPlayerTriggerStay(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

        if (player == localPlayer && AImode == 0 || AImode == 1 && Cooldown > 0 && Debuffmode == 0){
            float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
            float SetRunSpeedValue = localPlayer.GetRunSpeed();
            float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
            float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
            float SetGravityStrengthValue = localPlayer.GetGravityStrength();

            if(SetWalkSpeedValue > 2f){localPlayer.SetWalkSpeed(SetWalkSpeedValue-0.1f);}
            if(SetRunSpeedValue > 2f){localPlayer.SetRunSpeed(SetRunSpeedValue-0.1f);}
            if(SetStrafeSpeedValue > 2f){localPlayer.SetStrafeSpeed(SetStrafeSpeedValue-0.1f);}
            if(SetJumpImpulseValue > 2f){localPlayer.SetJumpImpulse(SetJumpImpulseValue-0.1f);}
            if(SetGravityStrengthValue < 2f){localPlayer.SetGravityStrength(SetGravityStrengthValue+0.1f);}

            //then round numbers to say between 0.2 and 20
            localPlayer.SetWalkSpeed(Mathf.Round(localPlayer.GetWalkSpeed() * 10) / 10);
            localPlayer.SetRunSpeed(Mathf.Round(localPlayer.GetRunSpeed() * 10) / 10);
            localPlayer.SetStrafeSpeed(Mathf.Round(localPlayer.GetStrafeSpeed() * 10) / 10);
            localPlayer.SetJumpImpulse(Mathf.Round(localPlayer.GetJumpImpulse() * 10) / 10);
            localPlayer.SetGravityStrength(Mathf.Round(localPlayer.GetGravityStrength() * 10) / 10);


            Cooldown = 0f;
            SendCustomEventDelayedSeconds(nameof(ResetCooldown), DebuffCooldown);
        }
    }
}
