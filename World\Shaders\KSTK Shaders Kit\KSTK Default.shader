Shader "KSTK Custom Shaders/KSTK Default"
{
    Properties
    {
        _MainTex("Texture", 2D) = "white" {}
        _Color("Color Tint", Color) = (1,1,1,1)
        _HasLighting("Has Lighting", Range(0,1)) = 0.5
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma shader_feature _HASLIGHTING_ON

        sampler2D _MainTex;
        fixed4 _Color;
        float _HasLighting;

        struct Input
        {
            float2 uv_MainTex;
        };

        void surf(Input IN, inout SurfaceOutputStandard o)
        {
            fixed4 texColor = tex2D(_MainTex, IN.uv_MainTex);

            if (_HasLighting == 1)
            {
                o.Albedo = texColor.rgb * _Color.rgb;
            }
            else{
                o.Albedo = texColor.rgb * _Color.rgb;
                o.Emission = o.Albedo; // Makes it appear unaffected by light
            }
        }
        ENDCG
    }
    FallBack "Diffuse"
}
