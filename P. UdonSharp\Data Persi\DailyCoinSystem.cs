﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class DailyCoinSystem : UdonSharpBehaviour
{
    public GameObject MainPlayerSystem;

    void Start()
    {
        if(MainPlayerSystem == null){MainPlayerSystem = GameObject.Find("PLAYER_MAINSYSTEM");}

        SendCustomEventDelayedSeconds(nameof(PlaytimeCoins), 60f);
    }

    public void PlaytimeCoins()
    {
        if(MainPlayerSystem != null){
            MainPlayerSystem.GetComponent<MainPlayerCurrencySystem>().AddPoints(5);
        }
        SendCustomEventDelayedSeconds(nameof(PlaytimeCoins), 60f);
    }
}
