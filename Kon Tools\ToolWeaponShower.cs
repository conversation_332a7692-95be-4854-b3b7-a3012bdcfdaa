using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using VRC.SDKBase;
using VRC.SDK3.Components;
using UdonSharp;
using System.Linq;

#if UNITY_EDITOR
public class ToolWeaponShower : EditorWindow
{
    private Vector2 scrollPosition;
    private List<Object> pickupObjects = new List<Object>();
    private List<string> objectTypes = new List<string>();
    private List<string> syncInfo = new List<string>();
    private bool[] selectedObjects;
    private bool selectAll = false;
    private int pickupCount = 0;
    private bool showHelp = false;
    private bool showStats = false;
    private bool showVRCPickups = true;
    private bool showVRCSyncObjects = true;
    private bool includeInactive = false;
    private string searchFilter = "";

    // Filter options
    private bool filterByPickupOnly = false;
    private bool filterBySyncOnly = false;

    // Statistics
    private int totalVRCPickups = 0;
    private int totalVRCSyncObjects = 0;
    private int pickupsWithSync = 0;
    private int pickupsWithoutSync = 0;

    [MenuItem("Kon Tools/VRCPickup Search Tool")]
    public static void ShowWindow()
    {
        GetWindow<ToolWeaponShower>("VRCPickup Search Tool");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("VRCPickup Search Tool", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Find VRCPickup Objects", GUILayout.Height(30)))
        {
            FindVRCPickupObjects();
        }

        if (pickupObjects.Count > 0 && GUILayout.Button("Select All Found Objects", GUILayout.Height(30)))
        {
            SelectAllFoundObjects();
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        showHelp = EditorGUILayout.Foldout(showHelp, "Help");
        if (showHelp)
        {
            EditorGUILayout.HelpBox(
                "This tool helps you find and manage VRCPickup objects in your scene.\n\n" +
                "1. Click 'Find VRCPickup Objects' to scan the scene\n" +
                "2. Use filters to show only VRCPickups, VRC Sync objects, or both\n" +
                "3. Use individual filters to check objects with pickup or sync components\n" +
                "4. Click on object names to select them in the hierarchy\n" +
                "5. Use 'Select All Found Objects' to select all filtered results\n\n" +
                "Filters:\n" +
                "- Show VRCPickups: Shows objects with VRCPickup components\n" +
                "- Show VRC Sync Objects: Shows objects with VRC sync components\n" +
                "- Pickup Only: Shows only objects that have VRCPickup but no sync\n" +
                "- Sync Only: Shows only objects that have sync but no VRCPickup",
                MessageType.Info);
        }

        showStats = EditorGUILayout.Foldout(showStats, "Statistics");
        if (showStats)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Scene Statistics", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Total VRCPickup Objects: {totalVRCPickups}");
            EditorGUILayout.LabelField($"Total VRC Sync Objects: {totalVRCSyncObjects}");
            EditorGUILayout.LabelField($"Pickups with Sync: {pickupsWithSync}");
            EditorGUILayout.LabelField($"Pickups without Sync: {pickupsWithoutSync}");
            EditorGUILayout.LabelField($"Currently Showing: {pickupCount} objects");
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Filter options
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Filter Options", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        showVRCPickups = EditorGUILayout.ToggleLeft("Show VRCPickups", showVRCPickups, GUILayout.Width(150));
        showVRCSyncObjects = EditorGUILayout.ToggleLeft("Show VRC Sync Objects", showVRCSyncObjects, GUILayout.Width(150));
        includeInactive = EditorGUILayout.ToggleLeft("Include Inactive Objects", includeInactive);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        filterByPickupOnly = EditorGUILayout.ToggleLeft("Pickup Only (no sync)", filterByPickupOnly, GUILayout.Width(150));
        filterBySyncOnly = EditorGUILayout.ToggleLeft("Sync Only (no pickup)", filterBySyncOnly, GUILayout.Width(150));
        EditorGUILayout.EndHorizontal();

        searchFilter = EditorGUILayout.TextField("Search by Name", searchFilter);

        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();

        if (pickupObjects.Count > 0)
        {
            EditorGUILayout.LabelField($"Found {pickupCount} objects", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            bool newSelectAll = EditorGUILayout.Toggle("Select All", selectAll);
            if (newSelectAll != selectAll)
            {
                selectAll = newSelectAll;
                for (int i = 0; i < selectedObjects.Length; i++)
                {
                    selectedObjects[i] = selectAll;
                }
            }

            if (GUILayout.Button("Focus Selected in Scene"))
            {
                FocusSelectedObjects();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            int displayedCount = 0;
            for (int i = 0; i < pickupObjects.Count; i++)
            {
                if (pickupObjects[i] != null)
                {
                    GameObject obj = GetGameObjectFromComponent(pickupObjects[i]);
                    if (obj == null) continue;

                    // Apply filters
                    bool hasPickup = HasVRCPickup(obj);
                    bool hasSync = HasVRCSync(obj);

                    // Filter by component type - if toggle is OFF, don't show that type
                    if (!showVRCPickups && hasPickup && !hasSync)
                        continue;
                    if (!showVRCSyncObjects && hasSync && !hasPickup)
                        continue;
                    if (!showVRCPickups && !showVRCSyncObjects)
                        continue;

                    // Individual filters
                    if (filterByPickupOnly && (!hasPickup || hasSync))
                        continue;

                    if (filterBySyncOnly && (!hasSync || hasPickup))
                        continue;

                    // Skip inactive objects if not including them
                    if (!includeInactive && !obj.activeInHierarchy)
                        continue;

                    // Apply name filter
                    if (!string.IsNullOrEmpty(searchFilter) && !obj.name.ToLower().Contains(searchFilter.ToLower()))
                        continue;

                    displayedCount++;

                    EditorGUILayout.BeginHorizontal();
                    selectedObjects[i] = EditorGUILayout.Toggle(selectedObjects[i], GUILayout.Width(20));

                    if (GUILayout.Button(obj.name, EditorStyles.label))
                    {
                        Selection.activeGameObject = obj;
                        EditorGUIUtility.PingObject(obj);
                    }

                    EditorGUILayout.LabelField(objectTypes[i], GUILayout.Width(150));

                    // Show sync info
                    if (i < syncInfo.Count)
                    {
                        string sync = syncInfo[i];
                        if (sync.Contains("ObjectSync") || sync.Contains("Sync"))
                            GUI.color = Color.green;
                        else if (sync.Contains("No Sync"))
                            GUI.color = Color.yellow;
                        else
                            GUI.color = Color.white;

                        EditorGUILayout.LabelField(sync, GUILayout.Width(100));
                        GUI.color = Color.white;
                    }

                    EditorGUILayout.EndHorizontal();
                }
            }

            // Update the displayed count
            pickupCount = displayedCount;

            EditorGUILayout.EndScrollView();
        }
    }

    private void FindVRCPickupObjects()
    {
        pickupObjects.Clear();
        objectTypes.Clear();
        syncInfo.Clear();

        // Reset statistics
        totalVRCPickups = 0;
        totalVRCSyncObjects = 0;
        pickupsWithSync = 0;
        pickupsWithoutSync = 0;

        // Find all VRCPickup components specifically
        VRC_Pickup[] legacyPickups = GameObject.FindObjectsOfType<VRC_Pickup>(includeInactive);
        VRCPickup[] sdk3Pickups = GameObject.FindObjectsOfType<VRCPickup>(includeInactive);

        // Find all VRCObjectSync components specifically
        VRCObjectSync[] objectSyncs = GameObject.FindObjectsOfType<VRCObjectSync>(includeInactive);

        // Process VRC_Pickup (legacy) objects
        foreach (VRC_Pickup pickup in legacyPickups)
        {
            GameObject obj = pickup.gameObject;
            bool hasSync = HasVRCSync(obj);

            pickupObjects.Add(obj);
            totalVRCPickups++;

            if (hasSync)
            {
                objectTypes.Add("VRCPickup + Sync");
                pickupsWithSync++;
            }
            else
            {
                objectTypes.Add("VRCPickup Only");
                pickupsWithoutSync++;
            }

            syncInfo.Add(GetSyncDetails(obj));
        }

        // Process VRCPickup (SDK3) objects
        foreach (VRCPickup pickup in sdk3Pickups)
        {
            GameObject obj = pickup.gameObject;

            // Skip if already added (in case object has both legacy and SDK3 pickup)
            if (pickupObjects.Contains(obj)) continue;

            bool hasSync = HasVRCSync(obj);

            pickupObjects.Add(obj);
            totalVRCPickups++;

            if (hasSync)
            {
                objectTypes.Add("VRCPickup + Sync");
                pickupsWithSync++;
            }
            else
            {
                objectTypes.Add("VRCPickup Only");
                pickupsWithoutSync++;
            }

            syncInfo.Add(GetSyncDetails(obj));
        }

        // Process VRCObjectSync objects that don't have pickups
        foreach (VRCObjectSync objectSync in objectSyncs)
        {
            GameObject obj = objectSync.gameObject;

            // Skip if already added (object has pickup)
            if (pickupObjects.Contains(obj)) continue;

            pickupObjects.Add(obj);
            objectTypes.Add("Sync Only");
            syncInfo.Add(GetSyncDetails(obj));
            totalVRCSyncObjects++;
        }

        pickupCount = pickupObjects.Count;
        selectedObjects = new bool[pickupCount];

        Debug.Log($"Found {totalVRCPickups} VRCPickup objects and {totalVRCSyncObjects} VRC Sync objects in the scene.");
    }

    private bool HasVRCPickup(GameObject obj)
    {
        // Check for both VRC_Pickup (legacy) and VRCPickup (SDK3)
        return obj.GetComponent<VRC_Pickup>() != null || obj.GetComponent<VRCPickup>() != null;
    }

    private bool HasVRCSync(GameObject obj)
    {
        // Check for VRCObjectSync (the main VRC sync component)
        if (obj.GetComponent<VRCObjectSync>() != null)
            return true;

        // Check for other VRC-specific sync components
        var components = obj.GetComponents<Component>();
        foreach (var component in components)
        {
            if (component != null)
            {
                string typeName = component.GetType().Name;
                // Only check for VRC-specific sync components, not all components with "Sync" in the name
                if (typeName == "VRCObjectSync" || typeName.StartsWith("VRC") && typeName.Contains("Sync"))
                    return true;
            }
        }

        return false;
    }

    private string GetSyncDetails(GameObject obj)
    {
        List<string> syncTypes = new List<string>();

        if (obj.GetComponent<VRCObjectSync>() != null)
            syncTypes.Add("ObjectSync");

        // Check for other VRC sync components
        var components = obj.GetComponents<Component>();
        foreach (var component in components)
        {
            if (component != null)
            {
                string typeName = component.GetType().Name;
                if (typeName.StartsWith("VRC") && typeName.Contains("Sync") && typeName != "VRCObjectSync")
                {
                    syncTypes.Add(typeName);
                }
            }
        }

        if (syncTypes.Count > 0)
            return string.Join(", ", syncTypes);
        else
            return "No Sync";
    }

    private GameObject GetGameObjectFromComponent(Object obj)
    {
        if (obj is GameObject)
            return (GameObject)obj;
        else if (obj is Component)
            return ((Component)obj).gameObject;
        return null;
    }

    private void SelectAllFoundObjects()
    {
        List<GameObject> objectsToSelect = new List<GameObject>();

        for (int i = 0; i < pickupObjects.Count; i++)
        {
            if (pickupObjects[i] != null)
            {
                GameObject obj = GetGameObjectFromComponent(pickupObjects[i]);
                if (obj != null)
                {
                    // Apply the same filters as in the display
                    bool hasPickup = HasVRCPickup(obj);
                    bool hasSync = HasVRCSync(obj);

                    // Filter by component type - if toggle is OFF, don't show that type
                    if (!showVRCPickups && hasPickup && !hasSync)
                        continue;
                    if (!showVRCSyncObjects && hasSync && !hasPickup)
                        continue;
                    if (!showVRCPickups && !showVRCSyncObjects)
                        continue;

                    if (filterByPickupOnly && (!hasPickup || hasSync))
                        continue;

                    if (filterBySyncOnly && (!hasSync || hasPickup))
                        continue;

                    if (!includeInactive && !obj.activeInHierarchy)
                        continue;

                    if (!string.IsNullOrEmpty(searchFilter) && !obj.name.ToLower().Contains(searchFilter.ToLower()))
                        continue;

                    objectsToSelect.Add(obj);
                }
            }
        }

        if (objectsToSelect.Count > 0)
        {
            Selection.objects = objectsToSelect.ToArray();
            Debug.Log($"Selected {objectsToSelect.Count} objects in the hierarchy.");
        }
        else
        {
            Debug.Log("No objects match the current filters.");
        }
    }

    private void FocusSelectedObjects()
    {
        List<GameObject> objectsToFocus = new List<GameObject>();

        for (int i = 0; i < pickupObjects.Count; i++)
        {
            if (selectedObjects[i] && pickupObjects[i] != null)
            {
                GameObject obj = GetGameObjectFromComponent(pickupObjects[i]);
                if (obj != null)
                    objectsToFocus.Add(obj);
            }
        }

        if (objectsToFocus.Count > 0)
        {
            Selection.objects = objectsToFocus.ToArray();
            if (objectsToFocus.Count == 1)
                EditorGUIUtility.PingObject(objectsToFocus[0]);
            Debug.Log($"Focused on {objectsToFocus.Count} selected objects.");
        }
        else
        {
            Debug.Log("No objects are selected.");
        }
    }

    // Add context menu items for quick access
    [MenuItem("GameObject/Kon Tools/Find VRCPickup Components", false, 20)]
    static void FindVRCPickupContextMenu()
    {
        var window = GetWindow<ToolWeaponShower>("VRCPickup Search Tool");
        window.FindVRCPickupObjects();
    }
}
#endif
