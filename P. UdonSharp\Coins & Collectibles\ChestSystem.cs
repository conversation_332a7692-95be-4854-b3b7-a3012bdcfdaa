﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class ChestSystem : UdonSharpBehaviour
{
    public Animator chestAnimator;

    public GameObject UI, Highlight, Coin;

    public void OpenChest()
    {
        if(UI.activeSelf == true){UI.SetActive(false); Highlight.SetActive(false);}
        chestAnimator.SetBool("IsOpen", true);

        SendCustomEventDelayedSeconds(nameof(CoinEnable), 1f);
    }

    public void CoinEnable()
    {
        Coin.SetActive(true);
    }
}
