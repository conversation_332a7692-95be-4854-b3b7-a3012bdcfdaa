using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;
using System;
using VRC.SDK3.Video.Components.Base;
using VRC.SDK3.Video.Components;
using UdonSharp.Video;


[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PlayerWorldStartSystem : UdonSharpBehaviour
{
    //Health Variables
    public int HEALTH = 100,MAXHEALTH = 100, ARMOR = 0, MAXARMOR = 3, ProtectionBoost = 1, DamageReduction = 1, DamageReceived = 1, DifficultyDamage = 1, DifficultyDamageReceived = 1;
    public bool ImmunityFrames, ImmunityFramesConstant, HealImmunityFrames;
    public bool SpecialImmunityFrames ,IsDead, CanChangeHealth;
    public TextMeshProUGUI[] HealthText, ArmorText, DMGReceivedMultText, DMGMultText;
    public Slider[] HealthSlider, ArmorSlider, DMGReceivedMultSlider, DMGMultSlider;
    public Sprite[] ArmorPointStateSprites;
    public Image ArmorPointStateImage;

    //Damage Variables
    public int GeneralDMGIncrease, GlassCannonDMGIncrease, BurnDamageIncrease;

    //Class Related
    public int ClassEquipped;
    public DoubleJump DoubleJumpScript;

    //DamageBoost Related
    public bool IsDamageBoosted;
    public float DamageBoostDuration = 10f;

    //SpeedBoost Related
    public float SpeedMultiplierCache;
    public bool IsSpeedBoosted;
    public float SpeedBoostDuration = 10f;
    //BurnBoos Related
    public GameObject FireBoostObject;
    public bool IsFireBoosted;
    public float FireBoostDuration = 60f;
    //Protection Boost Related
    public GameObject ProtectionBoostObject;
    public bool IsProtected;
    public float ProtectionBoostDuration = 60f;
    //IsArmorBoosted Related
    public bool IsArmorBoosted;

    //DeathScreen
    public GameObject DeathScreen, RespawnButton, DeathMusic, DeathSpeech, DeathParticles;
    public AudioClip DeathSound;

    //Additional
    public bool Heightmode = true; //true is Human, false is Konrusk
    public bool HasStarted;
    public bool FriendlyFire = true;
    public GameObject[] FriendlyFireKnockbackObjects;

    //PrisonRelated
    public bool PlayerInPrison;
    public Collider PrisonCollider;
    public int PrisonCooldown;

    //Effects and Sounds
    public AudioSource AudioSource;
    public AudioClip HurtSound, ArmorHurtSound;
    public GameObject HurtIndicator;
    public MeshRenderer HurtMeshRenderer;
    public LayerMask damageLayer;

    //VideoplayerVolume
    public VideoPlayerManager videoPlayerManager;
    public float VolumeCache;

    //SpawnMenu
    public GameObject[] SpawnMenu;
    public SpawnMenuSystem SpawnMenuSystem;

    //BattleTower
    public BattleTowerMainSystem BattleTowerMainSystem;

    //Difficulty
    public int Difficulty;
    public TextMeshProUGUI DifficultyText;
    public Image DifficultyImage;
    public Color[] DifficultyColors;

    private VRCPlayerApi localPlayer;

    private void Start()
    {
        localPlayer = Networking.LocalPlayer;

        CanChangeHealth = true;
        GeneralDMGIncrease = 1;
        GlassCannonDMGIncrease = 1;
        BurnDamageIncrease = 1;
        DifficultyDamageReceived = 1;

        //Health
        HEALTH = MAXHEALTH;
        for (int i = 0; i < HealthText.Length; i++){
            HealthText[i].text = HEALTH.ToString();
        }
        for (int i = 0; i < HealthSlider.Length; i++){
            if(HealthSlider[i] != null){HealthSlider[i].value = HEALTH; HealthSlider[i].maxValue = MAXHEALTH;}
        }
        //Armor
        ARMOR = MAXARMOR;
        for (int i = 0; i < ArmorText.Length; i++){
            if(ArmorText[i] != null){ArmorText[i].text = ARMOR.ToString();}
        }
        for (int i = 0; i < ArmorSlider.Length; i++){
            if(ArmorSlider[i] != null){ArmorSlider[i].value = ARMOR; ArmorSlider[i].maxValue = MAXARMOR;}
        }

        ArmorPointStateImage.sprite = ArmorPointStateSprites[ARMOR];

        DeathScreen.SetActive(false);
        HurtIndicator.SetActive(false);

        //FriendlyFire knockback objects
        FriendlyFireKnockbackToggle();


        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
        SendCustomEventDelayedSeconds(nameof(HasProperlyStarted), 15f);
    }
    public void HasProperlyStarted(){HasStarted = true;}

    #region CustomUpdate
    public void CustomUpdate()
    {
        if(!IsDead && HEALTH > 0){
            //Health
            for (int i = 0; i < HealthText.Length; i++){
                if(HealthText[i] != null){HealthText[i].text = HEALTH.ToString();}
            }
            for (int i = 0; i < HealthSlider.Length; i++){
                if(HealthSlider[i] != null){HealthSlider[i].value = HEALTH; HealthSlider[i].maxValue = MAXHEALTH;}
            }
            //Armor
            for (int i = 0; i < ArmorText.Length; i++){
                if(ArmorText[i] != null){ArmorText[i].text = ARMOR.ToString();}
            }
            for (int i = 0; i < ArmorSlider.Length; i++){
                if(ArmorSlider[i] != null){ArmorSlider[i].value = ARMOR; ArmorSlider[i].maxValue = MAXARMOR;}
            }
            //Damage Received Mult
            for (int i = 0; i < DMGReceivedMultText.Length; i++){
                if(DMGReceivedMultText[i] != null){DMGReceivedMultText[i].text = (100/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived).ToString() + "%";}
            }
            for (int i = 0; i < DMGReceivedMultSlider.Length; i++){
                if(DMGReceivedMultSlider[i] != null){DMGReceivedMultSlider[i].value = 100/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived; DMGReceivedMultSlider[i].maxValue = 100;}
            }
            //Damage Mult
            for (int i = 0; i < DMGMultText.Length; i++){
                int totalMultiplier = 1;
                if(GeneralDMGIncrease > 1) totalMultiplier += (GeneralDMGIncrease - 1);
                if(BurnDamageIncrease > 1) totalMultiplier += (BurnDamageIncrease - 1);
                if(GlassCannonDMGIncrease > 1) totalMultiplier += (GlassCannonDMGIncrease - 1);

                if(DMGMultText[i] != null){DMGMultText[i].text = (100*totalMultiplier/DifficultyDamage).ToString() + "%";}
            }
            for (int i = 0; i < DMGMultSlider.Length; i++){
                int totalMultiplier = 1;
                if(GeneralDMGIncrease > 1) totalMultiplier += (GeneralDMGIncrease - 1);
                if(BurnDamageIncrease > 1) totalMultiplier += (BurnDamageIncrease - 1);
                if(GlassCannonDMGIncrease > 1) totalMultiplier += (GlassCannonDMGIncrease - 1);

                if(DMGMultSlider[i] != null){DMGMultSlider[i].value = 100*totalMultiplier/DifficultyDamage; DMGMultSlider[i].maxValue = 100;}
            }
        }
        if(!IsDead && HEALTH <= 0){HasDiedFunction();}

        //HurtIndicator
        var color = HurtMeshRenderer.material.color;
        if(color.a > 0){color.a -= 0.05f; HurtMeshRenderer.material.color = color;}
        else{HurtIndicator.SetActive(false);}

        if(PrisonCollider != null && PlayerInPrison){
            if(!PrisonCollider.bounds.Contains(localPlayer.GetPosition())){
                localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);
                if(HEALTH <= 0){
                    for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}

                    // Restore video volume when respawning in prison
                    if(videoPlayerManager != null){
                        videoPlayerManager.SetVolume(VolumeCache);
                    }

                    IsDead = false;
                    DeathScreen.SetActive(false);
                    HEALTH = MAXHEALTH;
                    ARMOR = MAXARMOR;
                }
            }
        }

        //CustomTimer
        if(!IsDead){SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);}
    }
    #endregion



    #region Classes / Modules

    public void Class1(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 100;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 100; HealthSlider[i].maxValue = 100;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 0;
        DamageReduction = 1;
        DamageReceived = 1;
        GlassCannonDMGIncrease = 1;
        DoubleJumpScript.IsEnabled = false;
        ClassSpeedFunction();
    }
    public void Class2(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 125;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 125; HealthSlider[i].maxValue = 125;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 1;
        DamageReduction = 2;
        DamageReceived = 1;
        GlassCannonDMGIncrease = 1;
        DoubleJumpScript.IsEnabled = false;
        ClassSpeedFunction();
    }
    public void Class3(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 75;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 75; HealthSlider[i].maxValue = 75;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 2;
        DamageReduction = 1;
        DamageReceived = 2;
        GlassCannonDMGIncrease = 2;
        DoubleJumpScript.IsEnabled = true;
        ClassSpeedFunction();
    }

    public void ClassSpeedFunction(){
        if(ClassEquipped == 0){
            localPlayer.SetWalkSpeed(4f); 
            localPlayer.SetRunSpeed(7f); 
            localPlayer.SetStrafeSpeed(8f); 
            localPlayer.SetJumpImpulse(5f); 
            localPlayer.SetGravityStrength(1f);
        }
        else if(ClassEquipped == 1){
            localPlayer.SetWalkSpeed(3f); 
            localPlayer.SetRunSpeed(6f); 
            localPlayer.SetStrafeSpeed(7f); 
            localPlayer.SetJumpImpulse(4f); 
            localPlayer.SetGravityStrength(2f);
            SendCustomEventDelayedSeconds(nameof(ClassSpeedFunction), 0.2f);
        }
        else if(ClassEquipped == 2){
            localPlayer.SetWalkSpeed(8f); 
            localPlayer.SetRunSpeed(14f); 
            localPlayer.SetStrafeSpeed(16f); 
            localPlayer.SetJumpImpulse(10f); 
            localPlayer.SetGravityStrength(1f);
            SendCustomEventDelayedSeconds(nameof(ClassSpeedFunction), 0.2f);
        }
    }
    #endregion

    #region Difficulty Settings
    public void DifficultyChanger(){
        if(Difficulty == 0){
            Difficulty = 1;
            DifficultyDamageReceived = 2;
            DifficultyDamage = 2;
            DifficultyText.text = "Difficulty Mode: \nHard";
            DifficultyImage.color = DifficultyColors[1];
        }
        else if(Difficulty == 1){
            Difficulty = 2;
            DifficultyDamageReceived = 3;
            DifficultyDamage = 1;
            DifficultyText.text = "Difficulty Mode: \nRealistic";
            DifficultyImage.color = DifficultyColors[2];
        }
        else if(Difficulty == 2){
            Difficulty = 0;
            DifficultyDamageReceived = 1;
            DifficultyDamage = 1;
            DifficultyText.text = "Difficulty Mode: \nNormal";
            DifficultyImage.color = DifficultyColors[0];
        }
    }
    #endregion

    #region DamageBoost
    public void DamageBoostedFunction(int DamageBoost)
    {
        if(IsDamageBoosted == false){
            GeneralDMGIncrease = DamageBoost;
            IsDamageBoosted = true;
            SendCustomEventDelayedSeconds(nameof(DamageDecreaseFunction), DamageBoostDuration/DifficultyDamageReceived);
        }
    }
    public void DamageDecreaseFunction()
    {
        GeneralDMGIncrease = 1;
        IsDamageBoosted = false;
    }
    #endregion

    #region SpeedBoost
    //Speed Boost Stuff
    public void SpeedBoostedFunction(float Speed)
    {
        if(IsSpeedBoosted == false && ClassEquipped == 0){
            float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
            float SetRunSpeedValue = localPlayer.GetRunSpeed();
            float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
            float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
            float SetGravityStrengthValue = localPlayer.GetGravityStrength();

            localPlayer.SetWalkSpeed(SetWalkSpeedValue*Speed);
            localPlayer.SetRunSpeed(SetRunSpeedValue*Speed);
            localPlayer.SetStrafeSpeed(SetStrafeSpeedValue*Speed);
            localPlayer.SetJumpImpulse(SetJumpImpulseValue*Speed);
            localPlayer.SetGravityStrength(SetGravityStrengthValue*Speed);
            SpeedMultiplierCache = Speed;
            SendCustomEventDelayedSeconds(nameof(SpeedDecreaseFunction), SpeedBoostDuration/DifficultyDamageReceived);
            IsSpeedBoosted = true;
        }
    }
    public void SpeedDecreaseFunction()
    {
        if(IsSpeedBoosted == true && ClassEquipped == 0){
            float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
            float SetRunSpeedValue = localPlayer.GetRunSpeed();
            float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
            float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
            float SetGravityStrengthValue = localPlayer.GetGravityStrength();
            
            localPlayer.SetWalkSpeed(SetWalkSpeedValue/SpeedMultiplierCache);
            localPlayer.SetRunSpeed(SetRunSpeedValue/SpeedMultiplierCache);
            localPlayer.SetStrafeSpeed(SetStrafeSpeedValue/SpeedMultiplierCache);
            localPlayer.SetJumpImpulse(SetJumpImpulseValue/SpeedMultiplierCache);
            localPlayer.SetGravityStrength(SetGravityStrengthValue/SpeedMultiplierCache);
            SpeedMultiplierCache = 0;
            IsSpeedBoosted = false;
        }
    }
    #endregion

    #region FireBoost
    public void FireBoostedFunction()
    {
        if(IsFireBoosted == false){
            BurnDamageIncrease = 2;
            FireBoostObject.SetActive(true);
            SendCustomEventDelayedSeconds(nameof(FireDecreaseFunction), FireBoostDuration/DifficultyDamageReceived);
            IsFireBoosted = true;
        }
    }
    public void FireDecreaseFunction()
    {
        BurnDamageIncrease = 1;
        FireBoostObject.SetActive(false);
        IsFireBoosted = false;
    }
    #endregion

    #region Protection Boost
    public void ProtectionBoostedFunction(int ProtectionBoostValue)
    {
        if(IsProtected == false){
            ProtectionBoost = ProtectionBoostValue;
            ProtectionBoostObject.SetActive(true);
            SendCustomEventDelayedSeconds(nameof(ProtectionDecreaseFunction), ProtectionBoostDuration/DifficultyDamageReceived);
            IsProtected = true;
        }
    }
    public void ProtectionDecreaseFunction()
    {
        ProtectionBoostObject.SetActive(false);
        ProtectionBoost = 1;
        IsProtected = false;
    }
    #endregion

    #region Health

    public void HurtIndicatorFunction(int DamageColor)
    {
        if(HEALTH > MAXHEALTH){HEALTH = MAXHEALTH;}
        HurtIndicator.SetActive(true);
        var color = HurtMeshRenderer.material.color;
        color = new Color(0, 0, 0, 0.25f);
        if(DamageColor == 0){color.r = 1.0f;} // Red
        else if(DamageColor == 1){color.g = 0.5f;} // Green
        else if(DamageColor == 2){color.b = 0.5f;} // Blue
        else if(DamageColor == 3){color.g = 1.0f;} // Healing
        else if(DamageColor == 4){color.b = 0.1f;} // MovementIncrease
        else if(DamageColor == 5){color.r = 1.0f; color.g = 1.0f;} // Fire
        else if(DamageColor == 6){color.g = 0.3f; color.b = 0.3f;} // Cold
        else if(DamageColor == 7){color.r = 0.3f; color.b = 0.3f;} // Storm
        else if(DamageColor == 8){color.r = 0.3f;} // DamageIncrease

        HurtMeshRenderer.material.color = color;
    }
    public void HasDiedFunction()
    {
        var color = HurtMeshRenderer.material.color;
        color.a = 0;
        HurtMeshRenderer.material.color = color;
        IsDead = true;
        AudioSource.PlayOneShot(DeathSound);
        DeathScreen.SetActive(true);
        DeathMusic.SetActive(true);
        DeathSpeech.SetActive(false);
        DeathParticles.SetActive(false);
        RespawnButton.SetActive(true);
        for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(false);}

        // Mute video audio during death screen
        if(videoPlayerManager != null){
            VolumeCache = videoPlayerManager.GetVolume();
            videoPlayerManager.SetVolume(0);
        }

        //Floor Reset when the battletowersystem's object is active in the hierarchy
        if(BattleTowerMainSystem != null && BattleTowerMainSystem.gameObject.activeInHierarchy == true && BattleTowerMainSystem.IsEnabled == true){
            BattleTowerMainSystem.Floor = 1;
            BattleTowerMainSystem.SavingFloor();
            BattleTowerMainSystem.FloorComplete = false;
        }

        // Drop pickup from left hand
        VRC_Pickup leftPickup = localPlayer.GetPickupInHand(VRC.SDK3.Components.VRCPickup.PickupHand.Left);
        if (leftPickup != null && leftPickup.allowManipulationWhenEquipped == true){leftPickup.Drop(localPlayer);}

        // Drop pickup from right hand
        VRC_Pickup rightPickup = localPlayer.GetPickupInHand(VRC.SDK3.Components.VRCPickup.PickupHand.Right);
        if (rightPickup != null && rightPickup.allowManipulationWhenEquipped == true){rightPickup.Drop(localPlayer);}

        if (localPlayer != null){localPlayer.TeleportTo(DeathScreen.transform.position, DeathScreen.transform.rotation);}
        SendCustomEventDelayedSeconds(nameof(CheckIfRespawned), 30f);
    }
    public void CheckIfRespawned()
    {
        if(IsDead == true){
            DeathSpeech.SetActive(true);
            DeathParticles.SetActive(true);
            RespawnButton.SetActive(false);
            DeathMusic.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(HasRespawnedFunction), 5.1f);
        }
    }
    public void HasRespawnedFunction()
    {
        if(IsDead == true){
            IsDead = false;
            DeathScreen.SetActive(false);
            HEALTH = MAXHEALTH;
            ARMOR = MAXARMOR;
            if (localPlayer != null){localPlayer.TeleportTo(new Vector3(0,0,0), new Quaternion(0,0,0,0));}

            if(videoPlayerManager != null){videoPlayerManager.SetVolume(VolumeCache);}

            // Spawn Menu
            for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}
            SpawnMenuSystem.CloseMenu();
            SpawnMenuSystem.MinimizedMode = true;
            SpawnMenuSystem.MenuContainer.SetActive(false);
            SpawnMenuSystem.spawnpoint.gameObject.SetActive(false);
            SpawnMenuSystem.MinimizedMenu.SetActive(true);
            SpawnMenuSystem.PickupCollider.enabled = false;

            ArmorPointStateImage.sprite = ArmorPointStateSprites[ARMOR];

            if(!IsDead && IsProtected == false){
                ProtectionBoostDuration = 10;
                ProtectionBoostedFunction(4);
                HurtIndicatorFunction(6);
            }

            SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
        }
    }

    public void DeactivateImmunityFrames(){ImmunityFrames = false;  ImmunityFramesConstant = false;}
    public void DeactivateHealImmunityFrames(){HealImmunityFrames = false;}

    #endregion

    #region Extras

    public void FriendlyFireToggle(){
        FriendlyFire = !FriendlyFire;
        FriendlyFireKnockbackToggle();
    }
    public void FriendlyFireKnockbackToggle(){
        if(FriendlyFire == true){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(true);
                }
            }
        }
        else if(FriendlyFire == false){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(false);
                }
            }
        }
    }


    public void DeactivatePrisonCooldown(){PrisonCooldown = 0;}

    public void DeleteEverything(){Destroy(gameObject);}

    public void ArmorCooldownFunction(){IsArmorBoosted = false;}
    #endregion

    #region Damage
    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;
        if (HasStarted == false) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFrames && SpecialImmunityFrames == false){
                if(ARMOR > 0){
                    if(Difficulty == 0){ //Normal
                        if(ARMOR == 1){ARMOR -= 1;}
                        else if(ARMOR == 2){ARMOR -= 1;}
                        else if(ARMOR == 3){ARMOR -= 1;}

                        if(HEALTH < MAXHEALTH/3){HEALTH -= Mathf.Max(1, 0);}
                        else if(HEALTH < MAXHEALTH/2 && HEALTH >= MAXHEALTH/3){HEALTH -= Mathf.Max(1, DamageInput.Damage/4/ProtectionBoost/DamageReduction*DamageReceived);}
                        else if(HEALTH <= MAXHEALTH && HEALTH >= MAXHEALTH/2){HEALTH -= Mathf.Max(1, DamageInput.Damage/2/ProtectionBoost/DamageReduction*DamageReceived);}
                    }
                    else if(Difficulty == 1){ //Hard
                        if(ARMOR == 1){ARMOR -= 1;}
                        else if(ARMOR == 2){ARMOR -= 2;}
                        else if(ARMOR == 3){ARMOR -= 2;}

                        if(HEALTH < MAXHEALTH/2){HEALTH -= Mathf.Max(1, DamageInput.Damage/4/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived);}
                        else if(HEALTH <= MAXHEALTH && HEALTH >= MAXHEALTH/2){HEALTH -= Mathf.Max(1, DamageInput.Damage/2/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived);}
                    }
                    else if(Difficulty == 2){ //Realistic
                        if(ARMOR == 1){ARMOR -= 1;}
                        else if(ARMOR == 2){ARMOR -= 2;}
                        else if(ARMOR == 3){ARMOR -= 3;}

                        if(HEALTH < MAXHEALTH/2){HEALTH -= Mathf.Max(1, 0);}
                        else if(HEALTH <= MAXHEALTH && HEALTH >= MAXHEALTH/2){HEALTH -= Mathf.Max(1, DamageInput.Damage*2/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived);}
                    }

                    AudioSource.PlayOneShot(ArmorHurtSound);
                    ArmorPointStateImage.sprite = ArmorPointStateSprites[ARMOR];
                }
                else{HEALTH -= Mathf.Max(1, DamageInput.Damage/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived); AudioSource.PlayOneShot(HurtSound);}
                ImmunityFrames = true;
                HurtIndicatorFunction(DamageInput.DamageColor);
                if(ClassEquipped == 0){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);}
                if(ClassEquipped == 1){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.3f);}
                if(ClassEquipped == 2){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.1f);}
            }
            if(DamageInput.IsHealing && !HealImmunityFrames && HEALTH < MAXHEALTH){
                HEALTH += DamageInput.Damage;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.4f);
            }
        }
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = !PlayerInPrison;
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
        else if(IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = true;
            HasRespawnedFunction();
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;
        if (HasStarted == false) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFramesConstant && SpecialImmunityFrames == false){
                HEALTH -= Mathf.Max(1, DamageInput.Damage/ProtectionBoost/DamageReduction*DamageReceived*DifficultyDamageReceived);
                ImmunityFramesConstant = true;
                AudioSource.PlayOneShot(HurtSound);
                HurtIndicatorFunction(DamageInput.DamageColor);
                if(ClassEquipped == 0){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);} //Regular
                if(ClassEquipped == 1){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.3f);} //Tank
                if(ClassEquipped == 2){SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.1f);} //Glass Cannon
            }
            if(DamageInput.IsHealing && !HealImmunityFrames && HEALTH < MAXHEALTH){
                HEALTH += DamageInput.Damage*DamageReceived;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.4f);
            }
        }

        PlayerSpeedInput SpeedInput = collision.gameObject.GetComponent<PlayerSpeedInput>();
        if(!IsDead && SpeedInput != null && SpeedInput.SpeedMultiplier != 0 && IsSpeedBoosted == false && ClassEquipped == 0){
            SpeedBoostDuration = SpeedInput.SpeedBoostDuration;
            SpeedBoostedFunction(SpeedInput.SpeedMultiplier);
            HurtIndicatorFunction(4);
        }

        PlayerDamageBuffInput DamageBuffInput = collision.gameObject.GetComponent<PlayerDamageBuffInput>();
        if(!IsDead && DamageBuffInput != null && DamageBuffInput.DamageBuff != 0 && IsDamageBoosted == false){
            DamageBoostDuration = DamageBuffInput.DamageBoostDuration;
            DamageBoostedFunction(DamageBuffInput.DamageBuff);
            HurtIndicatorFunction(8);
        }

        ProtectionBoostInput ProtectionBoostInput = collision.gameObject.GetComponent<ProtectionBoostInput>();
        if(!IsDead && ProtectionBoostInput != null && ProtectionBoostInput.ProtectionBoostMultiplier != 0 && IsProtected == false){
            ProtectionBoostDuration = ProtectionBoostInput.ProtectionBoostDuration;
            ProtectionBoostedFunction(ProtectionBoostInput.ProtectionBoostMultiplier);
            HurtIndicatorFunction(6);
        }

        ArmorPointsInput ArmorPointsInput = collision.gameObject.GetComponent<ArmorPointsInput>();
        if(!IsDead && ArmorPointsInput != null && ArmorPointsInput.ArmorPoints != 0 && IsArmorBoosted == false){
            if(ARMOR < MAXARMOR){
                ARMOR += ArmorPointsInput.ArmorPoints;

                ArmorPointStateImage.sprite = ArmorPointStateSprites[ARMOR];
            }
            else{
                if(ClassEquipped == 0){
                    float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
                    float SetRunSpeedValue = localPlayer.GetRunSpeed();
                    float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
                    float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
                    float SetGravityStrengthValue = localPlayer.GetGravityStrength();

                    if(SetWalkSpeedValue > 4f){localPlayer.SetWalkSpeed(SetWalkSpeedValue-0.2f);}
                    if(SetRunSpeedValue > 4f){localPlayer.SetRunSpeed(SetRunSpeedValue-0.2f);}
                    if(SetStrafeSpeedValue > 4f){localPlayer.SetStrafeSpeed(SetStrafeSpeedValue-0.2f);}
                    if(SetJumpImpulseValue > 4f){localPlayer.SetJumpImpulse(SetJumpImpulseValue-0.2f);}
                    if(SetGravityStrengthValue < 2f){localPlayer.SetGravityStrength(SetGravityStrengthValue+0.1f);}
                }
            }
            HurtIndicatorFunction(7);
            IsArmorBoosted = true;
            SendCustomEventDelayedSeconds(nameof(ArmorCooldownFunction), ArmorPointsInput.CooldownDuration/DifficultyDamageReceived);
        }
    }
    #endregion
}


