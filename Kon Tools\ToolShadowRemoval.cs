using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using System.Linq;

#if UNITY_EDITOR
public class ToolShadowRemoval : EditorWindow
{
    private Vector2 scrollPosition;
    private List<Object> shadowCastingObjects = new List<Object>();
    private List<string> objectTypes = new List<string>();
    private List<string> objectImpacts = new List<string>();
    private bool[] selectedObjects;
    private bool selectAll = false;
    private int shadowCount = 0;
    private bool showHelp = false;
    private bool showStats = false;
    private bool showRenderers = true;
    private bool showLights = true;
    private bool includeInactive = false;
    private string searchFilter = "";
    private ShadowMode shadowMode = ShadowMode.DisableShadows;

    // Statistics
    private int totalRenderers = 0;
    private int totalLights = 0;
    private int shadowCastingRenderers = 0;
    private int shadowCastingLights = 0;
    private int directionalLightCount = 0;
    private int spotLightCount = 0;
    private int pointLightCount = 0;

    private enum ShadowMode
    {
        DisableShadows,
        ReceiveOnly,
        ReceiveAndCast
    }

    [MenuItem("Kon Tools/Shadow Removal Tool")]
    public static void ShowWindow()
    {
        GetWindow<ToolShadowRemoval>("Shadow Removal Tool");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("Shadow Removal Tool", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Find Shadow Casting Objects", GUILayout.Height(30)))
        {
            FindShadowCastingObjects();
        }

        if (shadowCastingObjects.Count > 0 && GUILayout.Button("Disable All Shadows", GUILayout.Height(30)))
        {
            if (EditorUtility.DisplayDialog("Disable All Shadows",
                "Are you sure you want to disable ALL shadows in the scene?\n\nThis will affect all renderers and lights that cast shadows.",
                "Yes, Disable All", "Cancel"))
            {
                DisableAllShadows();
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        showHelp = EditorGUILayout.Foldout(showHelp, "Help");
        if (showHelp)
        {
            EditorGUILayout.HelpBox(
                "This tool helps optimize your scene by finding and disabling shadow casting.\n\n" +
                "1. Click 'Find Shadow Casting Objects' to scan the scene\n" +
                "2. Select objects you want to modify or use 'Disable All Shadows'\n" +
                "3. Click 'Apply Shadow Mode to Selected' to modify shadow settings\n\n" +
                "You can filter by object type (Renderers/Lights) and include inactive objects.\n\n" +
                "Shadow Modes:\n" +
                "- Disable Shadows: Completely turns off shadow casting and receiving\n" +
                "- Receive Only: Objects will receive shadows but not cast them\n" +
                "- Receive And Cast: Objects will both receive and cast shadows (default Unity behavior)",
                MessageType.Info);
        }

        EditorGUILayout.Space();

        // Shadow statistics
        showStats = EditorGUILayout.Foldout(showStats, "Shadow Statistics");
        if (showStats)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField("Scene Shadow Statistics", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Total Renderers: {totalRenderers} (Shadow Casting: {shadowCastingRenderers})");
            EditorGUILayout.LabelField($"Total Lights: {totalLights} (Shadow Casting: {shadowCastingLights})");

            if (shadowCastingLights > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Light Types:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Directional Lights: {directionalLightCount} (High Impact)");
                EditorGUILayout.LabelField($"Spot Lights: {spotLightCount} (Medium Impact)");
                EditorGUILayout.LabelField($"Point Lights: {pointLightCount} (High Impact)");
            }

            if (shadowCastingRenderers > 0 || shadowCastingLights > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Performance Impact", EditorStyles.boldLabel);

                float impactScore = CalculateImpactScore();
                string impactLevel = "Low";
                Color impactColor = Color.green;

                if (impactScore > 30)
                {
                    impactLevel = "High";
                    impactColor = Color.red;
                }
                else if (impactScore > 10)
                {
                    impactLevel = "Medium";
                    impactColor = Color.yellow;
                }

                GUI.color = impactColor;
                EditorGUILayout.LabelField($"Estimated Performance Impact: {impactLevel}");
                GUI.color = Color.white;

                string recommendation = "Consider disabling shadows on non-essential objects.";
                if (impactScore > 30)
                {
                    recommendation = "Strongly recommended to reduce shadow casting objects.";
                }
                else if (impactScore < 5)
                {
                    recommendation = "Current shadow usage is reasonable.";
                }

                EditorGUILayout.LabelField($"Recommendation: {recommendation}");
            }

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();

        // Filter options
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Filter Options", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        showRenderers = EditorGUILayout.ToggleLeft("Show Renderers", showRenderers, GUILayout.Width(120));
        showLights = EditorGUILayout.ToggleLeft("Show Lights", showLights, GUILayout.Width(120));
        includeInactive = EditorGUILayout.ToggleLeft("Include Inactive Objects", includeInactive);
        EditorGUILayout.EndHorizontal();

        searchFilter = EditorGUILayout.TextField("Search by Name", searchFilter);

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Shadow Mode", EditorStyles.boldLabel);
        shadowMode = (ShadowMode)EditorGUILayout.EnumPopup(shadowMode);

        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();

        if (shadowCastingObjects.Count > 0)
        {
            EditorGUILayout.LabelField($"Found {shadowCount} objects casting shadows", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            bool newSelectAll = EditorGUILayout.Toggle("Select All", selectAll);
            if (newSelectAll != selectAll)
            {
                selectAll = newSelectAll;
                for (int i = 0; i < selectedObjects.Length; i++)
                {
                    selectedObjects[i] = selectAll;
                }
            }

            if (GUILayout.Button("Apply Shadow Mode to Selected"))
            {
                DisableSelectedShadows();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            for (int i = 0; i < shadowCastingObjects.Count; i++)
            {
                if (shadowCastingObjects[i] != null)
                {
                    // Apply filters
                    bool isRenderer = shadowCastingObjects[i] is Renderer;
                    bool isLight = shadowCastingObjects[i] is Light;

                    if ((!showRenderers && isRenderer) || (!showLights && isLight))
                        continue;

                    GameObject obj = null;
                    if (isRenderer)
                        obj = ((Renderer)shadowCastingObjects[i]).gameObject;
                    else if (isLight)
                        obj = ((Light)shadowCastingObjects[i]).gameObject;

                    if (obj == null)
                        continue;

                    // Skip inactive objects if not including them
                    if (!includeInactive && !obj.activeInHierarchy)
                        continue;

                    // Apply name filter
                    if (!string.IsNullOrEmpty(searchFilter) && !obj.name.ToLower().Contains(searchFilter.ToLower()))
                        continue;

                    EditorGUILayout.BeginHorizontal();
                    selectedObjects[i] = EditorGUILayout.Toggle(selectedObjects[i], GUILayout.Width(20));

                    if (GUILayout.Button(obj.name, EditorStyles.label))
                    {
                        Selection.activeGameObject = obj;
                        EditorGUIUtility.PingObject(obj);
                    }

                    EditorGUILayout.LabelField(objectTypes[i], GUILayout.Width(150));

                    // Show performance impact
                    if (i < objectImpacts.Count)
                    {
                        string impact = objectImpacts[i];
                        if (impact == "High")
                            GUI.color = Color.red;
                        else if (impact == "Medium")
                            GUI.color = Color.yellow;
                        else
                            GUI.color = Color.green;

                        EditorGUILayout.LabelField(impact, GUILayout.Width(50));
                        GUI.color = Color.white;
                    }

                    EditorGUILayout.EndHorizontal();
                }
            }

            EditorGUILayout.EndScrollView();
        }
    }

    private float CalculateImpactScore()
    {
        // Simple heuristic for performance impact
        float score = 0;

        // Directional lights have the highest impact
        score += directionalLightCount * 10;

        // Point lights are also expensive
        score += pointLightCount * 5;

        // Spot lights are less expensive
        score += spotLightCount * 3;

        // Add score for renderers based on their count
        score += shadowCastingRenderers * 0.5f;

        return score;
    }

    private string GetObjectImpact(Object obj)
    {
        if (obj is Light)
        {
            Light light = (Light)obj;
            switch (light.type)
            {
                case LightType.Directional:
                    return "High";
                case LightType.Point:
                    return "High";
                case LightType.Spot:
                    return "Medium";
                default:
                    return "Low";
            }
        }
        else if (obj is Renderer)
        {
            Renderer renderer = (Renderer)obj;

            // Check if it's a skinned mesh renderer (usually more complex)
            if (renderer is SkinnedMeshRenderer)
                return "Medium";

            // Check bounds size as a rough estimate of complexity
            float volume = renderer.bounds.size.x * renderer.bounds.size.y * renderer.bounds.size.z;
            if (volume > 100)
                return "Medium";
            else if (volume > 10)
                return "Low";
            else
                return "Minimal";
        }

        return "Unknown";
    }

    private void FindShadowCastingObjects()
    {
        shadowCastingObjects.Clear();
        objectTypes.Clear();
        objectImpacts.Clear();

        // Reset statistics
        totalRenderers = 0;
        totalLights = 0;
        shadowCastingRenderers = 0;
        shadowCastingLights = 0;
        directionalLightCount = 0;
        spotLightCount = 0;
        pointLightCount = 0;

        // Find all renderers in the scene
        Renderer[] allRenderers = GameObject.FindObjectsOfType<Renderer>(includeInactive);
        totalRenderers = allRenderers.Length;

        foreach (Renderer renderer in allRenderers)
        {
            // Check if the renderer casts shadows
            if (renderer.shadowCastingMode != ShadowCastingMode.Off)
            {
                shadowCastingObjects.Add(renderer);
                objectTypes.Add($"Renderer ({renderer.shadowCastingMode})");
                objectImpacts.Add(GetObjectImpact(renderer));
                shadowCastingRenderers++;
            }
        }

        // Find all lights in the scene
        Light[] allLights = GameObject.FindObjectsOfType<Light>(includeInactive);
        totalLights = allLights.Length;

        foreach (Light light in allLights)
        {
            // Check if the light casts shadows
            if (light.shadows != LightShadows.None)
            {
                shadowCastingObjects.Add(light);
                objectTypes.Add($"Light ({light.shadows})");
                objectImpacts.Add(GetObjectImpact(light));
                shadowCastingLights++;

                // Count light types
                switch (light.type)
                {
                    case LightType.Directional:
                        directionalLightCount++;
                        break;
                    case LightType.Spot:
                        spotLightCount++;
                        break;
                    case LightType.Point:
                        pointLightCount++;
                        break;
                }
            }
        }

        shadowCount = shadowCastingObjects.Count;
        selectedObjects = new bool[shadowCount];

        Debug.Log($"Found {shadowCount} objects casting shadows in the scene.");
    }

    private void DisableSelectedShadows()
    {
        int disabledCount = 0;

        for (int i = 0; i < shadowCastingObjects.Count; i++)
        {
            if (selectedObjects[i] && shadowCastingObjects[i] != null)
            {
                if (shadowCastingObjects[i] is Renderer)
                {
                    Renderer renderer = (Renderer)shadowCastingObjects[i];
                    Undo.RecordObject(renderer, "Modify Shadow Casting");

                    switch (shadowMode)
                    {
                        case ShadowMode.DisableShadows:
                            renderer.shadowCastingMode = ShadowCastingMode.Off;
                            renderer.receiveShadows = false;
                            break;
                        case ShadowMode.ReceiveOnly:
                            renderer.shadowCastingMode = ShadowCastingMode.Off;
                            renderer.receiveShadows = true;
                            break;
                        case ShadowMode.ReceiveAndCast:
                            renderer.shadowCastingMode = ShadowCastingMode.On;
                            renderer.receiveShadows = true;
                            break;
                    }

                    EditorUtility.SetDirty(renderer);
                    disabledCount++;
                }
                else if (shadowCastingObjects[i] is Light)
                {
                    Light light = (Light)shadowCastingObjects[i];
                    Undo.RecordObject(light, "Modify Shadow Casting");

                    switch (shadowMode)
                    {
                        case ShadowMode.DisableShadows:
                        case ShadowMode.ReceiveOnly:
                            light.shadows = LightShadows.None;
                            break;
                        case ShadowMode.ReceiveAndCast:
                            light.shadows = LightShadows.Soft;
                            break;
                    }

                    EditorUtility.SetDirty(light);
                    disabledCount++;
                }
            }
        }

        string actionText = "Modified shadows";
        if (shadowMode == ShadowMode.DisableShadows)
            actionText = "Disabled shadows";
        else if (shadowMode == ShadowMode.ReceiveOnly)
            actionText = "Set objects to receive shadows only";
        Debug.Log($"{actionText} on {disabledCount} objects.");

        // Refresh the list
        FindShadowCastingObjects();
    }

    private void DisableAllShadows()
    {
        int disabledCount = 0;

        // Disable all renderer shadows
        Renderer[] allRenderers = GameObject.FindObjectsOfType<Renderer>(includeInactive);
        foreach (Renderer renderer in allRenderers)
        {
            if (renderer.shadowCastingMode != ShadowCastingMode.Off)
            {
                Undo.RecordObject(renderer, "Disable All Shadows");

                switch (shadowMode)
                {
                    case ShadowMode.DisableShadows:
                        renderer.shadowCastingMode = ShadowCastingMode.Off;
                        renderer.receiveShadows = false;
                        break;
                    case ShadowMode.ReceiveOnly:
                        renderer.shadowCastingMode = ShadowCastingMode.Off;
                        renderer.receiveShadows = true;
                        break;
                    case ShadowMode.ReceiveAndCast:
                        renderer.shadowCastingMode = ShadowCastingMode.On;
                        renderer.receiveShadows = true;
                        break;
                }

                EditorUtility.SetDirty(renderer);
                disabledCount++;
            }
        }

        // Disable all light shadows
        Light[] allLights = GameObject.FindObjectsOfType<Light>(includeInactive);
        foreach (Light light in allLights)
        {
            if (light.shadows != LightShadows.None)
            {
                Undo.RecordObject(light, "Disable All Shadows");

                switch (shadowMode)
                {
                    case ShadowMode.DisableShadows:
                    case ShadowMode.ReceiveOnly:
                        light.shadows = LightShadows.None;
                        break;
                    case ShadowMode.ReceiveAndCast:
                        light.shadows = LightShadows.Soft;
                        break;
                }

                EditorUtility.SetDirty(light);
                disabledCount++;
            }
        }

        string actionText = "Modified shadows";
        if (shadowMode == ShadowMode.DisableShadows)
            actionText = "Disabled shadows";
        else if (shadowMode == ShadowMode.ReceiveOnly)
            actionText = "Set objects to receive shadows only";
        Debug.Log($"{actionText} on {disabledCount} objects.");

        // Refresh the list
        FindShadowCastingObjects();
    }

    // Add context menu items to quickly access the tool
    [MenuItem("GameObject/Kon Tools/Disable Shadows", false, 20)]
    static void DisableShadowsContextMenu()
    {
        ApplyShadowModeToSelection(ShadowMode.DisableShadows);
    }

    [MenuItem("GameObject/Kon Tools/Shadows/Receive Only", false, 21)]
    static void ReceiveShadowsOnlyContextMenu()
    {
        ApplyShadowModeToSelection(ShadowMode.ReceiveOnly);
    }

    [MenuItem("GameObject/Kon Tools/Shadows/Receive And Cast", false, 22)]
    static void ReceiveAndCastShadowsContextMenu()
    {
        ApplyShadowModeToSelection(ShadowMode.ReceiveAndCast);
    }

    static void ApplyShadowModeToSelection(ShadowMode mode)
    {
        GameObject[] selection = Selection.gameObjects;
        if (selection.Length == 0) return;

        int modifiedCount = 0;

        foreach (GameObject obj in selection)
        {
            // Check for renderers
            Renderer[] renderers = obj.GetComponentsInChildren<Renderer>(true);
            foreach (Renderer renderer in renderers)
            {
                Undo.RecordObject(renderer, "Modify Shadow Settings");

                switch (mode)
                {
                    case ShadowMode.DisableShadows:
                        renderer.shadowCastingMode = ShadowCastingMode.Off;
                        renderer.receiveShadows = false;
                        break;
                    case ShadowMode.ReceiveOnly:
                        renderer.shadowCastingMode = ShadowCastingMode.Off;
                        renderer.receiveShadows = true;
                        break;
                    case ShadowMode.ReceiveAndCast:
                        renderer.shadowCastingMode = ShadowCastingMode.On;
                        renderer.receiveShadows = true;
                        break;
                }

                EditorUtility.SetDirty(renderer);
                modifiedCount++;
            }

            // Check for lights
            Light[] lights = obj.GetComponentsInChildren<Light>(true);
            foreach (Light light in lights)
            {
                Undo.RecordObject(light, "Modify Shadow Settings");

                switch (mode)
                {
                    case ShadowMode.DisableShadows:
                    case ShadowMode.ReceiveOnly:
                        light.shadows = LightShadows.None;
                        break;
                    case ShadowMode.ReceiveAndCast:
                        light.shadows = LightShadows.Soft;
                        break;
                }

                EditorUtility.SetDirty(light);
                modifiedCount++;
            }
        }

        string actionText = "Modified shadows";
        if (mode == ShadowMode.DisableShadows)
            actionText = "Disabled shadows";
        else if (mode == ShadowMode.ReceiveOnly)
            actionText = "Set objects to receive shadows only";

        Debug.Log($"{actionText} on {modifiedCount} objects.");
    }
}
#endif
