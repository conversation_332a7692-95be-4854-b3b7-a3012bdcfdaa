using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MultiAttackEnemyTemplate : MonoBehaviour
{
//public GameObject Body;

//public int Attacks;

//void Start(){SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 0.1f);}

//public void EnemyAISystem()
//{
//    Attacks = Random.Range(1, 4);//
//    if(Attacks == 1)
//    {
//        SendCustomEventDelayedSeconds(nameof(AttackOne), 1f);
//        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f, 5f));
//    }
//    else if(Attacks == 2)
//    {
//        SendCustomEventDelayedSeconds(nameof(AttackTwo), 1f);
//        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), Random.Range(2f, 5f));
//    }
//    else if(Attacks == 3)
//    {
//        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
//    }
//}

//public void AttackOne()
//{
//    SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
//}

//public void AttackTwo()
//{
//    SendCustomEventDelayedSeconds(nameof(EnemyAISystem), 1f);
//}
}
