﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CoreHealthSystem : UdonSharpBehaviour
{
    public int Health,HealthMax = 100;
    public bool IsDead;
    public float DeathDelay = 1f;
    public int State; //-1 = Blue, 0 = White, 1 = Green
    public GameObject[] StateActiveObjects;
    public MeshRenderer[] MeshRenderers;
    public int CanDie = 1;
    public int IsCore;

    //Effects
    public ParticleSystem Particle;
    public ParticleSystem Particle2;
    public AudioSource AudioSource;
    public GameObject[] Body;
    public Rigidbody rb;
    public GameObject[] DamageAreas;
    public LayerMask damageLayer;

    public GameObject[] MinionsProtectors;

    //HealthBar
    public GameObject HealthBar;
    public TextMeshPro DamageText;
    public Slider HealthSlider;
    public bool CanBeHit;
    public float ImmunityFramesDoneDelay = 0.1f;

    //Buffs
    public EnemyCoreAI CoreAI;
    public EnemyCoreAI[] MinionCores;

    public int MoneyDropAmount = 0;

    public void Start()
    {
        if(MeshRenderers != null && MeshRenderers.Length > 0){
            for (int i = 0; i < MeshRenderers.Length; i++){
                MeshRenderers[i].material.color = Color.white;
            }
        }
        rb = GetComponent<Rigidbody>();
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.maxValue = HealthMax;
        HealthSlider.value = Health;
        CanBeHit = true;
        SendCustomEventDelayedSeconds(nameof(AIUpdate), 0.05f);
    }

    void OnEnable()
    {
        Health = HealthMax;
        DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
        HealthSlider.value = Health;
    }

    public void AIUpdate()
    {
        if(State == 0 && !IsDead && StateActiveObjects != null && IsCore == 1){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material.color != Color.white){MeshRenderers[i].material.color = Color.white;}
                    }
                }
                CoreAI.speed = 5f;
                CoreAI.Turn = 0.04f;
                if(MinionCores != null){
                    for (int i2 = 0; i2 < MinionCores.Length; i2++){
                        if (MinionCores[i2] != null){
                            MinionCores[i2].speed = 10f;
                            MinionCores[i2].Turn = 0.05f;
                        }
                    }
                }
            }
            StateActiveObjects[0].SetActive(false);
            StateActiveObjects[1].SetActive(false);
        }
        else if(State == -1 && !IsDead && StateActiveObjects != null && IsCore == 1){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material.color != Color.blue){MeshRenderers[i].material.color = Color.blue;}
                    }
                }
                CoreAI.speed = 5f*2;
                CoreAI.Turn = 0.04f*2;
                if(MinionCores != null){
                    for (int i2 = 0; i2 < MinionCores.Length; i2++){
                        if (MinionCores[i2] != null){
                            MinionCores[i2].speed = 10f*2;
                            MinionCores[i2].Turn = 0.05f*2;
                        }
                    }
                }
            }
            StateActiveObjects[0].SetActive(true);
            StateActiveObjects[1].SetActive(false);
        }
        else if(State == 1 && !IsDead && StateActiveObjects != null && IsCore == 1){
            if(MeshRenderers != null && MeshRenderers.Length > 0){
                for (int i = 0; i < MeshRenderers.Length; i++){
                    if(MeshRenderers[i] != null){
                        if(MeshRenderers[i].material.color != Color.green){MeshRenderers[i].material.color = Color.green;}
                    }
                }
                if(Health < HealthMax){Health++; DamageText.text = Health.ToString() + "/" + HealthMax.ToString(); HealthSlider.value = Health;}
                if(Health > HealthMax){Health = HealthMax;}
                CoreAI.speed = 5f;
                CoreAI.Turn = 0.04f;
                if(MinionCores != null){
                    for (int i2 = 0; i2 < MinionCores.Length; i2++){
                        if (MinionCores[i2] != null){
                            MinionCores[i2].speed = 10f;
                            MinionCores[i2].Turn = 0.05f;
                        }
                    }
                }
            }
            StateActiveObjects[0].SetActive(false);
            StateActiveObjects[1].SetActive(true);
        }
        if(MinionsProtectors != null && MinionsProtectors.Length > 0){
            bool hasActiveProtectors = false;
            for (int i = 0; i < MinionsProtectors.Length; i++) {
                if (MinionsProtectors[i] != null) {
                    hasActiveProtectors = true;
                    break;
                }
            }
            CanDie = hasActiveProtectors ? 0 : 1;
        }
        else{
            CanDie = 1;
        }

        if(CanDie == 0 && !IsDead){HealthBar.SetActive(false); Health = HealthMax; DamageText.text = Health.ToString() + "/" + HealthMax.ToString(); HealthSlider.value = Health;}
        else if(CanDie == 1 && !IsDead){HealthBar.SetActive(true);}

        SendCustomEventDelayedSeconds(nameof(AIUpdate), 1f);
    }

    public void ImmunityFramesDone(){CanBeHit = true;}
    public void Dead()
    {
        IsDead = true;

        if(MoneyDropAmount != 0){
            GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
            PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(MoneyDropAmount);
        }

        if(Particle != null){Particle.Play();}
        for (int i = 0; i < Body.Length; i++){
            Body[i].SetActive(false);
        }
        if(rb != null){rb.isKinematic = true;}
        HealthBar.SetActive(false);
        if(DamageAreas != null){
            for (int i = 0; i < DamageAreas.Length; i++){
                DamageAreas[i].SetActive(false);
            }
        }
        SendCustomEventDelayedSeconds(nameof(Delete), DeathDelay);
    }
    public void Delete(){Destroy(gameObject);}

    public void OnDestroy()
    {
        if(HealthBar != null){Destroy(HealthBar);}
        for (int i = 0; i < MinionsProtectors.Length; i++){
            if(MinionsProtectors[i] != null){Destroy(MinionsProtectors[i]);}
        }
    }

    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && !IsDead && CanDie == 1)
        {
            int Damage = DamageInput.Damage;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
        else if(DamageInput == null && CanBeHit && !IsDead && CanDie == 1)
        {
            int Damage = 1;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }

        CanBeInfected CanBeInfected = collision.gameObject.GetComponent<CanBeInfected>();
        if(!IsDead && CanBeInfected != null)
        {
            if(CanBeInfected.InfectionType == -1){State = 0;}
            else if(CanBeInfected.InfectionType == 0){State = -1;}
            else if(CanBeInfected.InfectionType == 1){State = 1;}
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        WeaponDamage DamageInput = collision.gameObject.GetComponent<WeaponDamage>();
        if(CanBeHit && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0 && !IsDead && CanDie == 1)
        {
            int Damage = DamageInput.Damage;
            Health -= Damage;
            DamageText.text = Health.ToString() + "/" + HealthMax.ToString();
            HealthSlider.value = Health;
            CanBeHit = false;

            Particle2.Play();
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Health <= 0){Dead();}

            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
        }
    }
}

