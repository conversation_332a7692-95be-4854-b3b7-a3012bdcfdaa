﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PopupSystemMain : UdonSharpBehaviour
{
    public MainPlayerCurrencySystem MainPlayerCurrencySystem;
    public PopupSystemOutput PopupSystemOutput;
    public string PopupMessage;

    public int OptionalCoins;

    public void OnEnable()
    {
        PopupSystemOutput.ShowPopUp(PopupMessage);
        if(OptionalCoins != 0 && MainPlayerCurrencySystem != null){MainPlayerCurrencySystem.AddPoints(OptionalCoins);}
    }
}
