﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class EnemyCoreAI : UdonSharpBehaviour
{
    public float speed,Turn;
    public float sightRange;
    public Vector3 SpawnedPoint;

    public int CoreType; //0 Is main, 1 is Minion

    //Forminions
    public Transform MainCore;

    //Formain
    public GameObject[] MinionCores;

    private VRCPlayerApi localPlayer;
    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
    }

    public void UpdateTimer(){
        if(localPlayer == null) return;
        if(CoreType == 0){
        if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRange && Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRange / 3){
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;
        }
        else if (Vector3.Distance(localPlayer.GetPosition(), transform.position) <= sightRange / 3){
            Vector3 Direction = (localPlayer.GetPosition() + new Vector3(0,1,0)) - transform.position;
            transform.rotation = Quaternion.LookRotation(Direction);
            GetComponent<Rigidbody>().velocity = Vector3.zero;
            float Animation = Mathf.Cos(Time.time*2);
            transform.position = transform.position + new Vector3(0, Animation/20, 0);
            if(MinionCores != null){
                foreach (GameObject Minion in MinionCores){
                    if (Minion != null && Minion.activeSelf == false){
                        Minion.SetActive(true);
                        Minion.transform.parent = null;
                        Minion.transform.position = transform.position;
                        Minion.transform.rotation = Quaternion.Euler(Random.Range(0,360),Random.Range(0,360),Random.Range(0,360));
                    }
                }
            }
        }
        else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRange){
            Vector3 direction = (SpawnedPoint - transform.position).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;
            if(MinionCores != null){
                foreach (GameObject Minion in MinionCores){
                    if (Minion != null && Minion.activeSelf == true){
                        Minion.SetActive(false);
                        Minion.transform.parent = transform;
                    }
                }
            }
        }
        }
        else if(CoreType == 1){
            Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position + new Vector3(Random.Range(-3,3),Random.Range(-3,3),Random.Range(-3,3))).normalized;
            Quaternion rotation = Quaternion.LookRotation(direction);
            float rotationSpeed = Turn;
            transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
            GetComponent<Rigidbody>().velocity = transform.forward * speed;
        }
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.02f);
    }

    void Destroy(){
        for (int i = 0; i < MinionCores.Length; i++){
            if (MinionCores[i] != null){Destroy(MinionCores[i]);}
        }
        Destroy(gameObject);
    }
}
