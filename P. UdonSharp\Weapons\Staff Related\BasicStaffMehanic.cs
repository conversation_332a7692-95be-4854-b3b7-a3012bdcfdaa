using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicStaffMehanic : UdonSharpBehaviour
{
    public int Stafftype,CooldownDelay,CooldownFinishesAt; //0 is Staff, 1 is Weapon, 2 Harpoon Gun, 3 Hitscan, 4 Automatic, 5 FlameThrower
    public GameObject[] Minions; // Array of minions instead of a single minion
    public int MinionCount = 1; // How many minions to spawn (default: 1 for backward compatibility)
    private GameObject[] ActiveMinions; // Track active minions

    public bool RandomizedRotation,RandomizedPosition; //For Weapon

    public float HarpoonGunSpeed; //For Harpoon Gun
    public Transform HarpoonPosition; //For Harpoon Gun
    public HpoonOutput HarpoonGunOutput; //For Harpoon Gun

    public AudioSource AudioSource; //For effect
    public ParticleSystem Particle; //For effect

    //Automatic Related
    public float AutomaticFireRate; //For Automatic
    public bool IsActive;
    public bool GunIsOnCooldown;
    public float GunCooldown;

    //Extras
    public bool FollowsWeapon,GlobalActive, AttacksPlayers;

    void Start()
    {
        CooldownDelay = CooldownFinishesAt;
        // Initialize the active minions array
        ActiveMinions = new GameObject[MinionCount];

        // Limit MinionCount to the number of available minions
        if (MinionCount > Minions.Length){MinionCount = Minions.Length;}

        // Add object transform position to all minions who have "BasicFriendlyMinion" as one of their components
        if (AttacksPlayers == false){
            for (int i = 0; i < Minions.Length; i++)
            {
                BasicFriendlyMinion minion = Minions[i].GetComponent<BasicFriendlyMinion>();
                if (minion != null){minion.Weaponposition = gameObject.transform;}
            }
        }
    }

    public override void OnPickupUseDown()
    {
        if(Stafftype == 0){
            // Spawn multiple minions for staff type 0
            for (int i = 0; i < MinionCount; i++)
            {
                // Calculate spawn position with slight offset for each minion
                Vector3 spawnPosition = gameObject.transform.position;
                if (i > 0)
                {
                    // Add a small offset for each additional minion
                    spawnPosition += new Vector3(
                        Random.Range(-0.3f, 0.3f),
                        Random.Range(0.1f, 0.3f),
                        Random.Range(-0.3f, 0.3f)
                    );
                }

                // Get the minion from the array using modulo to cycle through available minions
                int minionIndex = i % Minions.Length;
                GameObject minion = Minions[minionIndex];

                minion.SetActive(true);
                minion.transform.position = spawnPosition;
                minion.transform.rotation = gameObject.transform.rotation;
                minion.transform.parent = null;

                // Store reference to the active minion
                ActiveMinions[i] = minion;
            }
        }
        if(AudioSource != null && Stafftype == 1 && CooldownDelay >= CooldownFinishesAt){
            
            // Spawn multiple minions for staff type 1
            for (int i = 0; i < MinionCount; i++)
            {
                // Calculate spawn position with slight offset for each minion
                Vector3 spawnPosition = gameObject.transform.position += new Vector3(Random.Range(-0.5f, 0.5f),Random.Range(-0.5f, 0.5f),Random.Range(-0.5f, 0.5f));
                Vector3 spawnPositionOg = gameObject.transform.position;

                // Get the minion from the array using modulo to cycle through available minions
                int minionIndex = i % Minions.Length;
                GameObject minion = Minions[minionIndex];

                if(RandomizedPosition){minion.transform.position = spawnPosition;}
                else{minion.transform.position = spawnPositionOg;}
                if(RandomizedRotation){minion.transform.rotation = gameObject.transform.rotation * Quaternion.Euler(Random.Range(-15f, 15f), Random.Range(-15f, 15f), Random.Range(-15f, 15f));}
                else{minion.transform.rotation = gameObject.transform.rotation;}
                minion.transform.parent = null;
                minion.SetActive(true);

                // Store reference to the active minion
                ActiveMinions[i] = minion;
            }

            AudioSource.PlayOneShot(AudioSource.clip);
            if(Particle != null) {Particle.Play();}
            CooldownDelay = 0;
            SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);
        }
        if(AudioSource != null && Stafftype == 2 && CooldownDelay >= CooldownFinishesAt){
            // For harpoon gun, we'll still use just the first minion
            GameObject minion = Minions[0];

            minion.transform.position = HarpoonPosition.position;
            minion.transform.rotation = HarpoonPosition.rotation;
            minion.transform.parent = null;
            minion.GetComponent<Rigidbody>().isKinematic = false;
            minion.GetComponent<Rigidbody>().velocity = Vector3.zero;
            minion.GetComponent<Rigidbody>().angularVelocity = Vector3.zero;
            minion.GetComponent<Rigidbody>().velocity = -minion.transform.forward * HarpoonGunSpeed;
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Particle != null) {Particle.Play();}
            CooldownDelay = 0;
            HarpoonGunOutput.IsActive = 1;
            SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);

            // Store reference to the active minion
            ActiveMinions[0] = minion;
        }
        if(Stafftype == 3 && GunIsOnCooldown == false){
            Minions[0].SetActive(true);
            if(Particle != null) {Particle.Play();}
            if(AudioSource != null) {AudioSource.PlayOneShot(AudioSource.clip);}

            GunIsOnCooldown = true;

            SendCustomEventDelayedSeconds(nameof(ClosingProjectile), 0.1f);
            SendCustomEventDelayedSeconds(nameof(GunCooldownFunction), GunCooldown);
        }
        if(Stafftype == 4 && GunIsOnCooldown == false){
            IsActive = true;
            GunIsOnCooldown = true;
            StartFiring();
            SendCustomEventDelayedSeconds(nameof(GunCooldownFunction), GunCooldown);
        } 
        if(Stafftype == 5){
            Minions[0].SetActive(true);
        }
    }

    public override void OnPickupUseUp()
    {
        if(Stafftype == 0 ){
            // Deactivate all active minions
            for (int i = 0; i < MinionCount; i++)
            {
                if (ActiveMinions[i] != null)
                {
                    ActiveMinions[i].SetActive(false);
                    ActiveMinions[i].transform.parent = gameObject.transform;
                }
            }
        }
        else if(Stafftype == 1){
            // Deactivate all active minions
            for (int i = 0; i < MinionCount; i++)
            {
                if (ActiveMinions[i] != null)
                {
                    ActiveMinions[i].SetActive(false);
                    ActiveMinions[i].transform.parent = gameObject.transform;
                }
            }
        }
        else if (Stafftype == 2){
            // For harpoon gun, we'll still use just the first minion
            if (ActiveMinions[0] != null)
            {
                GameObject minion = ActiveMinions[0];
                minion.transform.parent = gameObject.transform;
                minion.GetComponent<Rigidbody>().isKinematic = true;
                minion.GetComponent<Rigidbody>().velocity = Vector3.zero;
                minion.transform.position = HarpoonPosition.position;
                minion.transform.rotation = HarpoonPosition.rotation;
                HarpoonGunOutput.IsActive = 0;
            }
        }
        else if (Stafftype == 3){Minions[0].SetActive(false);}
        else if (Stafftype == 4){IsActive = false; Minions[0].SetActive(false);}
        else if (Stafftype == 5){Minions[0].SetActive(false);}
    }

    public void Cooldown(){if(CooldownDelay < CooldownFinishesAt){CooldownDelay++; SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);}}

    public void ClosingProjectile(){Minions[0].SetActive(false);}

    public void StartFiring(){
        Minions[0].SetActive(true);
        if(Particle != null) {Particle.Play();}
        if(AudioSource != null) {AudioSource.PlayOneShot(AudioSource.clip);}

        SendCustomEventDelayedSeconds(nameof(StopFiring), AutomaticFireRate);
    }
    public void StopFiring(){
        Minions[0].SetActive(false);
    
        if(IsActive){SendCustomEventDelayedSeconds(nameof(StartFiring), AutomaticFireRate);}
    }

    public void GunCooldownFunction(){GunIsOnCooldown = false;}


    public void SimulatedUseDown()
    {
        if(Stafftype == 0){OnPickupUseDown();}
        else if(Stafftype == 1 && CooldownDelay >= CooldownFinishesAt){OnPickupUseDown();}
        else if(Stafftype == 2 && CooldownDelay >= CooldownFinishesAt){OnPickupUseDown();}
        else if(Stafftype == 3 && GunIsOnCooldown == false){OnPickupUseDown();}
        else if(Stafftype == 4 && GunIsOnCooldown == false){OnPickupUseDown();}
        else if(Stafftype == 5){OnPickupUseDown();}
    }
    public void SimulatedUseUp()
    {
        if(Stafftype == 0){OnPickupUseUp();}
        else if(Stafftype == 1){OnPickupUseUp();}
        else if(Stafftype == 2){OnPickupUseUp();}
        else if(Stafftype == 3){OnPickupUseUp();}
        else if(Stafftype == 4){OnPickupUseUp();}
        else if(Stafftype == 5){OnPickupUseUp();}
    }

    void OnDestroy(){
        for (int i = 0; i < Minions.Length; i++){if (Minions[i] != null){Destroy(Minions[i]);}}
        for (int i = 0; i < ActiveMinions.Length; i++){if (ActiveMinions[i] != null){Destroy(ActiveMinions[i]);}}
    }
}
