using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class ShopSelectionSystem : UdonSharpBehaviour
{
    //Required Inputs
    public GameObject SpawnMenuSystemObject, PlayerSystemObject;
    public GameObject PurchaseButton, OwnedButton, CannotAffordObject;
    public TextMeshPro ItemPriceText;

    //Items, Costs and Images
    public int Item, ItemSelectedID, ItemPrice;
    public int[] ItemsPriceMin, ItemsPriceMax, ItemsPriceFixed;
    public Image ItemImage;
    public Sprite[] ItemsSprite;
    public Color[] OptionalImageColor;
    public string[] ItemName;

    public int PlayerMoney;

    //Required
    public GameObject Menu;
    public bool PlayerInProximity;
    private VRCPlayerApi localPlayer;
    public LayerMask whatIsPlayer;
    public AudioSource PurchaseSource;
    public AudioClip PurchaseClip;

    //Settings
    public bool HasPredeterminedPrices1, HasPredeterminedPrices2;

    private void Start()
    {
        SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
        localPlayer = Networking.LocalPlayer;

        bool playerInSightRange = Physics.CheckSphere(transform.position, 15f, whatIsPlayer);

        if(playerInSightRange){Menu.SetActive(true);}
        else{Menu.SetActive(false);}
        SendCustomEventDelayedSeconds(nameof(RefreshShop), 1f);
    }

    private void OnEnable()
    {
        if(HasPredeterminedPrices1 == false){ItemPrice1 = Random.Range(ItemsPriceMin[0], ItemsPriceMax[0]);}
        else{ItemPrice1 = ItemsPriceFixed[RandomLength];}

        ItemImage.sprite = ItemsSprite[RandomLength];

        if(OptionalImageColor[RandomLength] != null){ItemImage.color = OptionalImageColor[RandomLength];}
        else{ItemImage.color = Color.white;}

        Item1PriceText.text = ItemName[RandomLength] + " for " + ItemPrice1.ToString();
    }

    public void RefreshShop(){
        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){
                if(spawnMenuDataSystem.IsItemUnlocked(Item1)){
                    PurchaseButton.SetActive(false);
                    OwnedButton.SetActive(true);
                }
                else{
                    PurchaseButton.SetActive(true);
                    OwnedButton.SetActive(false);
                }
            }
        }
        SendCustomEventDelayedSeconds(nameof(RefreshShop), 5f);
    }


    #region CollisionFunctions
    //Collision Related
    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(true);
        PlayerInProximity = true;
    }
    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(false);
        PlayerInProximity = false;
    }
    #endregion CollisionFunctions
}
