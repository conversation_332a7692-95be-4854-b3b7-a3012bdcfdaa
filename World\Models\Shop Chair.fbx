; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 8
		Day: 24
		Hour: 19
		Minute: 19
		Second: 25
		Millisecond: 607
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\ucr5ij1v_Shop Chair.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\The Intergalactic Empire v3.7.6\Assets\World\Models\ucr5ij1v_Shop Chair.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1645064079200, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1650817778480, "Geometry::Scene", "Mesh" {
		Vertices: *36 {
			a: 100,70.6594467163086,0,-0,70.6594467163086,0,100,70.6594467163086,-100,-0,70.6594467163086,-100,96.9064712524414,9.36145782470703,-96.9070434570312,3.09352874755859,9.36145782470703,-96.9070434570312,96.9064712524414,9.36145782470703,-3.09295654296875,3.09352874755859,9.36145782470703,-3.09295654296875,13.7002944946289,43.6931610107422,-13.7008666992188,86.2997055053711,43.6931610107422,-13.7008666992188,13.7002944946289,43.6931610107422,-86.2991333007812,86.2997055053711,43.6931610107422,-86.2991333007812
		} 
		PolygonVertexIndex: *40 {
			a: 0,2,3,-2,4,6,7,-6,6,9,8,-8,9,0,1,-9,7,8,10,-6,8,1,3,-11,4,5,10,-12,11,10,3,-3,6,4,11,-10,9,11,2,-1
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *120 {
				a: -0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0.295212090015411,0.955431699752808,-0,0.295212060213089,0.955431699752808,-0,0.295212090015411,0.955431699752808,-0,0.295212090015411,0.955431759357452,-0,-0.452962845563889,0.891529381275177,-0,-0.452962845563889,0.891529381275177,-0,-0.452962845563889,0.891529381275177,-0,-0.452962845563889,0.891529381275177,-0.955440759658813,0.295183002948761,0,-0.955440759658813,0.295183002948761,0,-0.955440759658813,0.295183002948761,0,-0.955440700054169,0.295183032751083,0,-0.891537010669708,-0.452947795391083,0,-0.891537010669708,-0.452947795391083,0,-0.891537010669708,-0.452947795391083,0,-0.891537010669708,-0.452947795391083,0,-0,0.295212090015411,-0.955431699752808,-0,0.295212090015411,-0.955431759357452,-0,0.295212090015411,-0.955431699752808,-0,0.295212060213089,-0.955431699752808,-0,-0.452962845563889,-0.891529381275177,-0,-0.452962845563889,-0.891529381275177,-0,-0.452962845563889,-0.891529381275177,-0,-0.452962845563889,-0.891529381275177,0.955440759658813,0.295183002948761,0,0.955440700054169,0.295183032751083,0,0.955440759658813,0.295183002948761,0,0.955440759658813,0.295183002948761,0,0.891537010669708,-0.452947795391083,0,0.891537010669708,-0.452947795391083,0,0.891537010669708,-0.452947795391083,0,0.891537010669708,-0.452947795391083,0
			} 
			NormalsW: *40 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *120 {
				a: 0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,0.955431699752808,-0.295212090015411,0,0.955431699752808,-0.295212060213089,0,0.955431699752808,-0.295212090015411,0,0.955431759357452,-0.295212090015411,-0,0.891529381275177,0.452962845563889,-0,0.891529381275177,0.452962845563889,-0,0.891529381275177,0.452962845563889,-0,0.891529381275177,0.452962845563889,0.295183002948761,0.955440759658813,-0,0.295183002948761,0.955440759658813,-0,0.295183002948761,0.955440759658813,-0,0.295183032751083,0.955440700054169,-0,-0.452947795391083,0.891537010669708,-0,-0.452947795391083,0.891537010669708,-0,-0.452947795391083,0.891537010669708,-0,-0.452947795391083,0.891537010669708,-0,0,0.955431699752808,0.295212090015411,0,0.955431759357452,0.295212090015411,0,0.955431699752808,0.295212090015411,0,0.955431699752808,0.295212060213089,0,0.891529381275177,-0.452962845563889,0,0.891529381275177,-0.452962845563889,0,0.891529381275177,-0.452962845563889,0,0.891529381275177,-0.452962845563889,-0.295183002948761,0.955440759658813,0,-0.295183032751083,0.955440700054169,0,-0.295183002948761,0.955440759658813,0,-0.295183002948761,0.955440759658813,0,0.452947795391083,0.891537010669708,-0,0.452947795391083,0.891537010669708,-0,0.452947795391083,0.891537010669708,-0,0.452947795391083,0.891537010669708,-0
			} 
			BinormalsW: *40 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *120 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			TangentsW: *40 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *80 {
				a: -1,0,0,0,-1,-1,0,-1,0.969064712524414,-0.969070434570312,0.0309352874755859,-0.969070434570312,0.969064712524414,-0.0309295654296875,0.0309352874755859,-0.0309295654296875,0.969064712524414,0.0906428322196007,0.0309352874755859,0.0906428322196007,0.137002944946289,0.423686981201172,0.862997055053711,0.423686981201172,0.137002944946289,0.423686981201172,0,0.70403116941452,1,0.70403116941452,0.862997055053711,0.423686981201172,-0.0309295654296875,0.090642087161541,-0.969070434570312,0.090642087161541,-0.862991333007812,0.423686295747757,-0.137008666992188,0.423686295747757,-0.862991333007812,0.423686295747757,-1,0.704030752182007,0,0.704030752182007,-0.137008666992188,0.423686295747757,-0.0309352874755859,0.175743967294693,-0.969064712524414,0.175743967294693,-0.862997055053711,0.508788108825684,-0.137002944946289,0.508788108825684,-0.862997055053711,0.508788108825684,-1,0.789132297039032,0,0.789132297039032,-0.137002944946289,0.508788108825684,0.969070434570312,0.175749972462654,0.0309295654296875,0.175749972462654,0.137008666992188,0.508794188499451,0.862991333007812,0.508794188499451,0.137008666992188,0.508794188499451,0,0.789138674736023,1,0.789138674736023,0.862991333007812,0.508794188499451
			} 
			UVIndex: *40 {
				a: 0,2,3,1,4,6,7,5,8,11,10,9,15,14,13,12,16,19,18,17,23,22,21,20,25,24,27,26,28,31,30,29,33,32,35,34,36,39,38,37
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 1645426570640, "Model::Shop_Chair", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1645440522208, "Material::Black_Metal", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.377358466386795,0.377358466386795,0.377358466386795
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.377358466386795,0.377358466386795,0.377358466386795
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Shop_Chair, Model::RootNode
	C: "OO",1645426570640,0
	
	;Material::Black_Metal, Model::Shop_Chair
	C: "OO",1645440522208,1645426570640
	
	;Geometry::Scene, Model::Shop_Chair
	C: "OO",1650817778480,1645426570640
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
