﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class Dummy : UdonSharpBehaviour
{
    public GameObject DamageText;
    public bool CanBeHit;
    public float ImmunityFramesDoneDelay = 0.1f;

    public ParticleSystem Particle;
    public AudioSource AudioSource;

    void Start(){ImmunityFramesDone();}

    public void ImmunityFramesDone(){CanBeHit = true;}

    public void CloseText(){DamageText.SetActive(false);}

    public void OnTriggerEnter(Collider collision)
    {
        int layer = collision.gameObject.layer;

        if(layer == LayerMask.NameToLayer("Pickup") && CanBeHit){
            Particle.Stop();
            Particle.Play();
            AudioSource.Play();
            DamageText.SetActive(true);
            CanBeHit = false;
            SendCustomEventDelayedSeconds(nameof(ImmunityFramesDone), ImmunityFramesDoneDelay);
            SendCustomEventDelayedSeconds(nameof(CloseText), 0.5f);
        }
    }
}
