﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class TurretEnemySystem : UdonSharpBehaviour
{
    public int TurretType; //0 Is rocket, 1 is flamethrower
    public LayerMask WhatIsPlayer;
    public GameObject bullet;
    public Transform bulletSpawnPoint;
    public float Range, ShootDelay = 1f, ReloadDelay = 1f;

    //Additional
    public GameObject OptionalHead;

    void Start()
    {
        bullet.SetActive(false);
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    public void UpdateCustom()
    {
        if(gameObject.activeInHierarchy == true){
            if(TurretType == 0){
                if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < Range){

                    if(OptionalHead != null){OptionalHead.transform.rotation = Quaternion.LookRotation(OptionalHead.transform.position - Networking.LocalPlayer.GetPosition());}

                    if(bullet != null)
                    {
                        bullet.SetActive(false);
                        bullet.transform.position = transform.position;
                        bullet.transform.rotation = transform.rotation;
                    }

                    SendCustomEventDelayedSeconds(nameof(FireTurret), ShootDelay);
                }
                else{
                    if(bullet != null){bullet.SetActive(false);}
                    SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
                }
            }
            else if(TurretType == 1){
                if(Vector3.Distance(transform.position, Networking.LocalPlayer.GetPosition()) < Range){
                    if(bullet != null){bullet.SetActive(true);}

                    if(OptionalHead != null){OptionalHead.transform.rotation = Quaternion.LookRotation((Networking.LocalPlayer.GetPosition() + new Vector3(0, 1.5f, 0)) - OptionalHead.transform.position);}
                }
                else{if(bullet != null){bullet.SetActive(false);}}

                SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);
            }
        }
        else{
            if(bullet != null){bullet.SetActive(false);}
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
        }
    }
    public void FireTurret(){
        if(bullet != null){
            bullet.SetActive(true);
            bullet.transform.position = bulletSpawnPoint.position;
            bullet.transform.rotation = bulletSpawnPoint.rotation;
        }
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), ReloadDelay);
    }


    void OnDestroy(){if(bullet != null){Destroy(bullet);}}
}
