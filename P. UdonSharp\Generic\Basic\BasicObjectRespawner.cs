﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicObjectRespawner : UdonSharpBehaviour
{   
    public GameObject Prefab;
    public GameObject SpawnedObject;
    public Transform SpawnPoint;

    public float RespawnTime = 10f;

    private int ScriptActive = 1;

    void Start(){CustomUpdate();}

    public void OnEnable(){
        ScriptActive = 1;

        if(SpawnedObject == null){
            GameObject GameObject = VRCInstantiate(Prefab);
            GameObject.transform.position = SpawnPoint.position;
            GameObject.transform.rotation = SpawnPoint.rotation;
            GameObject.transform.parent = SpawnPoint;
            SpawnedObject = GameObject;
        }
        else{SpawnedObject.GetComponent<Rigidbody>().isKinematic = false;}

        SendCustomEventDelayedSeconds(nameof(CustomUpdate), RespawnTime);
    }
    public void OnDisable(){
        ScriptActive = 0;
        if(SpawnedObject != null){SpawnedObject.GetComponent<Rigidbody>().isKinematic = true;}
    }

    public void CustomUpdate(){
        if(SpawnedObject == null){
            GameObject GameObject = VRCInstantiate(Prefab);
            GameObject.transform.position = SpawnPoint.position;
            GameObject.transform.rotation = SpawnPoint.rotation;
            GameObject.transform.parent = SpawnPoint;
            SpawnedObject = GameObject;
        }
        if(ScriptActive == 1){SendCustomEventDelayedSeconds(nameof(CustomUpdate), RespawnTime);}
    }
}
