using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GenTimeStarterHourlyV2 : UdonSharpBehaviour
{
    public GameObject[] TimedEvents;
    public int[] activationHour;
    public int currentHour,LastHour;

    //Customization
    public GameObject[] StoryEvents;
    public string[] StoryName;

    //Extras
    public GameObject[] CollabEvents;
    public string[] CollabNames;
    public bool[] CollabActive;

    public bool IsEnabled;
    private bool timerRunning = false;
    public int CurrentEventObject;
    private bool HasCompletedStory = false; // Track if CurrentEventObject has ever reached 9

    public TextMeshPro ScheduleText1, ScheduleText2;
    private GameObject PlayerSystemObject;
    private StoryProgressionSystem StoryProgressionSystem;

    void OnEnable()
    {
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");

        IsEnabled = true;
        if (!timerRunning){StartTimer();}
    }

    void OnDisable(){IsEnabled = false;}

    private void StartTimer()
    {
        if (!timerRunning)
        {
            timerRunning = true;
            SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);
        }
    }

    public void UpdateTime()
    {
        if (!IsEnabled)
        {
            timerRunning = false;
            return;
        }

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        currentHour = networkTime.Hour;

        if(PlayerSystemObject != null){
            StoryProgressionSystem = PlayerSystemObject.GetComponent<StoryProgressionSystem>();
            CurrentEventObject = StoryProgressionSystem.Progression;
        }

        //Story Event IDs
        if(CurrentEventObject < 10){
            // Check if CurrentEventObject has reached 9 and set the flag
            if(CurrentEventObject == 9){HasCompletedStory = true;}

            // If HasCompletedStory is true, disable all story events permanently
            if(HasCompletedStory)
            {
                TimedEvents[1] = null;
                TimedEvents[2] = null;
                TimedEvents[4] = null;
                TimedEvents[5] = null;
                TimedEvents[7] = null;
                TimedEvents[8] = null;
                TimedEvents[10] = null;
                TimedEvents[11] = null;
                TimedEvents[13] = null;
                TimedEvents[14] = null;
                TimedEvents[16] = null;
                TimedEvents[17] = null;
                TimedEvents[19] = null;
                TimedEvents[20] = null;
                TimedEvents[22] = null;
                TimedEvents[23] = null;
            }
            else if(!HasCompletedStory && TimedEvents[1] != StoryEvents[CurrentEventObject] && (CurrentEventObject == 0 || CurrentEventObject == 2 || CurrentEventObject == 4 || CurrentEventObject == 6 || CurrentEventObject == 8))
            {
                //disable all events
                for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null){TimedEvents[i].SetActive(false);}}

                if(CurrentEventObject == 0 || CurrentEventObject == 2 || CurrentEventObject == 4 || CurrentEventObject == 6 || CurrentEventObject == 8){
                    TimedEvents[2] = null;
                    TimedEvents[5] = null;
                    TimedEvents[8] = null;
                    TimedEvents[11] = null;
                    TimedEvents[14] = null;
                    TimedEvents[17] = null;
                    TimedEvents[20] = null;
                    TimedEvents[23] = null;

                    TimedEvents[1] = StoryEvents[CurrentEventObject];
                    TimedEvents[4] = StoryEvents[CurrentEventObject];
                    TimedEvents[7] = StoryEvents[CurrentEventObject];
                    TimedEvents[10] = StoryEvents[CurrentEventObject];
                    TimedEvents[13] = StoryEvents[CurrentEventObject];
                    TimedEvents[16] = StoryEvents[CurrentEventObject];
                    TimedEvents[19] = StoryEvents[CurrentEventObject];
                    TimedEvents[22] = StoryEvents[CurrentEventObject];
                }

                // Update ScheduleText1 (Hours 0-11)
                string scheduleText1 = "";
                scheduleText1 += "0:00: None!\n\n";
                scheduleText1 += "1:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "2:00: " + "None!" + "\n\n";
                scheduleText1 += "3:00: None!\n\n";
                scheduleText1 += "4:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "5:00: " + "None!" + "\n\n";
                scheduleText1 += "6:00: None!\n\n";
                scheduleText1 += "7:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "8:00: " + "None!" + "\n\n";
                scheduleText1 += "9:00: None!\n\n";
                scheduleText1 += "10:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "11:00: " + "None!";

                // Update ScheduleText2 (Hours 12-23)
                string scheduleText2 = "";
                scheduleText2 += "12:00: None!\n\n";
                scheduleText2 += "13:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "14:00: " + "None!" + "\n\n";
                scheduleText2 += "15:00: None!\n\n";
                scheduleText2 += "16:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "17:00: " + "None!" + "\n\n";
                scheduleText2 += "18:00: None!\n\n";
                scheduleText2 += "19:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "20:00: " + "None!" + "\n\n";
                scheduleText2 += "21:00: None!\n\n";
                scheduleText2 += "22:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "23:00: " + "None!";

                if(ScheduleText1 != null) ScheduleText1.text = scheduleText1;
                if(ScheduleText2 != null) ScheduleText2.text = scheduleText2;

            }
            else if(!HasCompletedStory && TimedEvents[2] != StoryEvents[CurrentEventObject] && (CurrentEventObject == 1 || CurrentEventObject == 3 || CurrentEventObject == 5 || CurrentEventObject == 7)){

                //disable all events
                for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null){TimedEvents[i].SetActive(false);}}

                if(CurrentEventObject == 1 || CurrentEventObject == 3 || CurrentEventObject == 5 || CurrentEventObject == 7){
                    TimedEvents[1] = null;
                    TimedEvents[4] = null;
                    TimedEvents[7] = null;
                    TimedEvents[10] = null;
                    TimedEvents[13] = null;
                    TimedEvents[16] = null;
                    TimedEvents[19] = null;
                    TimedEvents[22] = null;

                    TimedEvents[2] = StoryEvents[CurrentEventObject];
                    TimedEvents[5] = StoryEvents[CurrentEventObject];
                    TimedEvents[8] = StoryEvents[CurrentEventObject];
                    TimedEvents[11] = StoryEvents[CurrentEventObject];
                    TimedEvents[14] = StoryEvents[CurrentEventObject];
                    TimedEvents[17] = StoryEvents[CurrentEventObject];
                    TimedEvents[20] = StoryEvents[CurrentEventObject];
                    TimedEvents[23] = StoryEvents[CurrentEventObject];
                }

                // Update ScheduleText1 (Hours 0-11)
                string scheduleText1 = "";
                scheduleText1 += "0:00: None!\n\n";
                scheduleText1 += "1:00: " + "None!" + "\n\n";
                scheduleText1 += "2:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "3:00: None!\n\n";
                scheduleText1 += "4:00: " + "None!" + "\n\n";
                scheduleText1 += "5:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "6:00: None!\n\n";
                scheduleText1 += "7:00: " + "None!" + "\n\n";
                scheduleText1 += "8:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText1 += "9:00: None!\n\n";
                scheduleText1 += "10:00: " + "None!" + "\n\n";
                scheduleText1 += "11:00: " + StoryName[CurrentEventObject];

                // Update ScheduleText2 (Hours 12-23)
                string scheduleText2 = "";
                scheduleText2 += "12:00: None!\n\n";
                scheduleText2 += "13:00: " + "None!" + "\n\n";
                scheduleText2 += "14:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "15:00: None!\n\n";
                scheduleText2 += "16:00: " + "None!" + "\n\n";
                scheduleText2 += "17:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "18:00: None!\n\n";
                scheduleText2 += "19:00: " + "None!" + "\n\n";
                scheduleText2 += "20:00: " + StoryName[CurrentEventObject] + "\n\n";
                scheduleText2 += "21:00: None!\n\n";
                scheduleText2 += "22:00: " + "None!" + "\n\n";
                scheduleText2 += "23:00: " + StoryName[CurrentEventObject];

                if(ScheduleText1 != null) ScheduleText1.text = scheduleText1;
                if(ScheduleText2 != null) ScheduleText2.text = scheduleText2;
            }
        }
        else{
            if(TimedEvents != null){ //disable all events
                TimedEvents[1] = null;
                TimedEvents[2] = null;
                TimedEvents[4] = null;
                TimedEvents[5] = null;
                TimedEvents[7] = null;
                TimedEvents[8] = null;
                TimedEvents[10] = null;
                TimedEvents[11] = null;
                TimedEvents[13] = null;
                TimedEvents[14] = null;
                TimedEvents[16] = null;
                TimedEvents[17] = null;
                TimedEvents[19] = null;
                TimedEvents[20] = null;
                TimedEvents[22] = null;
                TimedEvents[23] = null;
            }
        }

        if (currentHour != LastHour)
        {
            for (int i = 0; i < TimedEvents.Length; i++){if (TimedEvents[i] != null){TimedEvents[i].SetActive(false);}}

            for (int i = 0; i < activationHour.Length; i++)
            {
                if (networkTime.Hour == activationHour[i] && i < TimedEvents.Length)
                {
                    if (TimedEvents[i] != null)
                    {
                        TimedEvents[i].SetActive(true);
                        Debug.Log("GenTimeStarter activated event " + i + " for hour " + currentHour);
                    }
                    break; // Exit loop after finding the first match
                }
            }
        }

        if(currentHour != LastHour){LastHour = currentHour;}

        if(IsEnabled){SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);}
        else{timerRunning = false;}
    }

    public void DeleteEverything(){
        for (int i = 0; i < TimedEvents.Length; i++){
            if (TimedEvents[i] != null){Destroy(TimedEvents[i]);}
        }
        Destroy(gameObject);
    }
}
