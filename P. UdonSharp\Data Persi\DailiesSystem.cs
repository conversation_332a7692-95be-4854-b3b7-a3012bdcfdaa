﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class DailiesSystem : UdonSharpBehaviour
{
    public int dayOfYear;

    public string[] DailyChestKeys;
    public GameObject[] DailyChests;

    public VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();

        dayOfYear = 0;
        dayOfYear = networkTime.DayOfYear;

        SendCustomEventDelayedSeconds(nameof(RefreshChests), 5f);
    }

    public void RefreshChests(){
        for (int i = 0; i < DailyChests.Length; i++){DailyChests[i].SetActive(false);}

        int PlayerKey1 = PlayerData.GetString(localPlayer, DailyChestKeys[0]) == dayOfYear.ToString() ? 1 : 0;
        int PlayerKey2 = PlayerData.GetString(localPlayer, DailyChestKeys[1]) == dayOfYear.ToString() ? 1 : 0;
        int PlayerKey3 = PlayerData.GetString(localPlayer, DailyChestKeys[2]) == dayOfYear.ToString() ? 1 : 0;
        int PlayerKey4 = PlayerData.GetString(localPlayer, DailyChestKeys[3]) == dayOfYear.ToString() ? 1 : 0;
        if (PlayerKey1 == 0){DailyChests[0].SetActive(true);}
        if (PlayerKey2 == 0){DailyChests[1].SetActive(true);}
        if (PlayerKey3 == 0){DailyChests[2].SetActive(true);}
        if (PlayerKey4 == 0){DailyChests[3].SetActive(true);}
    }

    public void DailyChest1Open(){PlayerData.SetString(DailyChestKeys[0], dayOfYear.ToString());}
    public void DailyChest2Open(){PlayerData.SetString(DailyChestKeys[1], dayOfYear.ToString());}
    public void DailyChest3Open(){PlayerData.SetString(DailyChestKeys[2], dayOfYear.ToString());}
    public void DailyChest4Open(){PlayerData.SetString(DailyChestKeys[3], dayOfYear.ToString());}
}
