﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalFlyingEnemyAISystem : UdonSharpBehaviour
{
    public float speed,Turn;
    public float sightRangeMax = 25;
    public float InRange = 8;
    private Vector3 SpawnedPoint;

    public int AIType; //0 Is Staring, 1 is Attacking, 2 is Flying Worm
    public bool IsGlitchBot;

    private VRCPlayerApi localPlayer;

    //Worm Dash Function
    public float DashDelay,DashDelayMax, DashingTime, dashSpeed;
    public bool IsDashing;
    public GameObject IndicatorBeforeDash;

    //Extras
    public float RandomCoreTypeCooldown = 5f;
    public float ExtraHeight = 1f;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
        if(IsGlitchBot){SendCustomEventDelayedSeconds(nameof(RandomizeCoreType), RandomCoreTypeCooldown);}
    }
    public void RandomizeCoreType(){
        AIType = Random.Range(0,2);
        SendCustomEventDelayedSeconds(nameof(RandomizeCoreType), RandomCoreTypeCooldown);
    }

    public void OnEnable(){SpawnedPoint = transform.position;}

    public void UpdateTimer(){
        if(localPlayer == null) return;
        if(AIType == 0){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && Vector3.Distance(localPlayer.GetPosition(), transform.position) > InRange){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
            else if (Vector3.Distance(localPlayer.GetPosition(), transform.position) <= InRange){
                Vector3 Direction = (localPlayer.GetPosition() + new Vector3(0,1,0)) - transform.position;
                transform.rotation = Quaternion.LookRotation(Direction);
                GetComponent<Rigidbody>().velocity = Vector3.zero;
                float Animation = Mathf.Cos(Time.time*2);
                transform.position = transform.position + new Vector3(0, Animation/40, 0);
            }
            else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRangeMax){
                Vector3 direction = (SpawnedPoint - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
        }
        else if(AIType == 1){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
            else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRangeMax){
                Vector3 direction = (SpawnedPoint - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;
            }
        }
        else if(AIType == 2){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && !IsDashing){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,ExtraHeight,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Vector3 directionPlayer = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Slerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;

                if(DashDelay >= DashDelayMax){
                    IsDashing = true; 
                    if(IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(false);}
                    transform.rotation = Quaternion.LookRotation(directionPlayer);
                }
                else if(DashDelay > DashDelayMax/1.5f && DashDelay < DashDelayMax){
                    DashDelay++;
                    if(!IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(true);}
                }
                else if(DashDelay <= DashDelayMax/1.5f){
                    DashDelay++; 
                    if(IndicatorBeforeDash.activeSelf){IndicatorBeforeDash.SetActive(false);}
                }
            }
            else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < sightRangeMax && IsDashing){
                Vector3 direction = (localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters()) - transform.position).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = 0;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * dashSpeed;

                if(DashDelay > 0){DashDelay -= DashingTime;}
                else{IsDashing = false;}
            }
            else if(Vector3.Distance(localPlayer.GetPosition(), transform.position) > sightRangeMax){
                Vector3 direction = (SpawnedPoint - transform.position + new Vector3(0,ExtraHeight,0)).normalized;
                Quaternion rotation = Quaternion.LookRotation(direction);
                float rotationSpeed = Turn;
                transform.rotation = Quaternion.Lerp(transform.rotation, rotation, rotationSpeed);
                GetComponent<Rigidbody>().velocity = transform.forward * speed;

                DashDelay = 0;
            }
        }
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.02f);
    }
}
