using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class UniversalPhysicalFlyingEnemySystem : UdonSharpBehaviour
{
    public int EnemyType = 0; // 0 = Wander Around
    public float Force = 25f;
    public float SightRange = 50f;
    public float EnemyAggression = 1f;
    public float Accuracy = 1f;
    public float Height = 1.5f;

    public GameObject Body, Head;
    public Rigidbody rb;

    public Vector3 SpawnedPoint;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        SpawnedPoint = transform.position;
        localPlayer = Networking.LocalPlayer;
        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), EnemyAggression);
        SendCustomEventDelayedSeconds(nameof(EnemyStareSystem), 1f);
    }

    void OnEnable(){SpawnedPoint = transform.position;}

    public void EnemyStareSystem()
    {
        if(EnemyType == 0){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){Head.transform.LookAt(localPlayer.GetPosition() + new Vector3(0,1.5f,0));}
            else{Head.transform.LookAt(SpawnedPoint + new Vector3(Random.Range(-300f,300f),1.5f,Random.Range(-300f,300f)));}
        }
        if(EnemyType == 1 || EnemyType == 2){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < SightRange){Head.transform.LookAt(localPlayer.GetPosition() + new Vector3(0,1.5f,0));}
            else{Head.transform.LookAt(SpawnedPoint);}
        }

        SendCustomEventDelayedSeconds(nameof(EnemyStareSystem), 0.05f);
    }

    public void EnemyAISystem()
    {
        if(EnemyType == 0){
            if(Vector3.Distance(localPlayer.GetPosition() , transform.position) < SightRange){rb.AddForce(((localPlayer.GetPosition() + new Vector3(Random.Range(-Accuracy, Accuracy), Height, Random.Range(-Accuracy, Accuracy))) - transform.position).normalized * Force*1000);}
            else{rb.AddForce(((SpawnedPoint + new Vector3(Random.Range(-300f,300f),1f,Random.Range(-300f,300f))) - transform.position).normalized * (Force*1000));}
        }
        else if(EnemyType == 1){
            if(Vector3.Distance(localPlayer.GetPosition() , transform.position) < SightRange){rb.AddForce(((localPlayer.GetPosition() + new Vector3(Random.Range(-Accuracy, Accuracy), Height, Random.Range(-Accuracy, Accuracy))) - transform.position).normalized * Force*1000);}
            else{rb.AddForce(((SpawnedPoint + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f))) - transform.position).normalized * (Force*1000));}
        }
        else if(EnemyType == 2){
            if(Vector3.Distance(localPlayer.GetPosition() , transform.position) < SightRange){rb.AddForce(((localPlayer.GetPosition() + new Vector3(Random.Range(-Accuracy, Accuracy), Random.Range(1f, Height), Random.Range(-Accuracy, Accuracy))) - transform.position).normalized * Force*1000);}
            else{rb.AddForce(((SpawnedPoint + new Vector3(Random.Range(-5f,5f),1f,Random.Range(-5f,5f))) - transform.position).normalized * (Force*1000));}
        }

        SendCustomEventDelayedSeconds(nameof(EnemyAISystem), EnemyAggression);
    }
}
