﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpaceResourceOutputSystem : UdonSharpBehaviour
{
    private GameObject PlayerSystemObject;
    private SpaceCoinsCurrencySystem SpaceCoinsCurrencySystem;

    private VRCPlayerApi localPlayer;
    public LayerMask LayerRequired;

    public GameObject RewardGiver;
    public TextMeshPro RewardText;

    void Start(){
        localPlayer = Networking.LocalPlayer; 
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
        SpaceCoinsCurrencySystem = PlayerSystemObject.GetComponent<SpaceCoinsCurrencySystem>();
    }

    public void HideReward(){RewardGiver.SetActive(false);}

    public void OnTriggerEnter(Collider collision)
    {
        if ((LayerRequired.value & (1 << collision.gameObject.layer)) == 0) return;

        SpaceResourceInput SpaceResourceInput = collision.gameObject.GetComponent<SpaceResourceInput>();
        if(SpaceResourceInput != null)
        {
            if(SpaceResourceInput.Reward != 0){
                SpaceCoinsCurrencySystem.AddPoints(SpaceResourceInput.Reward);
                Destroy(collision.gameObject);

                RewardText.text = SpaceResourceInput.Reward.ToString();
                RewardGiver.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(HideReward), 3f);
            }
        }
    }
}
