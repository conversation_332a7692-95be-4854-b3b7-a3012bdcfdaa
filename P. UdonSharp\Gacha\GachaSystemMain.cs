using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GachaSystemMain : UdonSharpBehaviour
{
    public int BannerSelected; //0 is item, 1 is regular, 2 is seasonal, 3 is collab, 4 is limited
    public int[] ItemID, ItemRarity; //0 is Item, 1 is regular, 2 is seasonal, 3 is Common Collab, 4 is collab, 5 is limited, 6 is not listed
    public string[] ItemName, ItemDescription;
    public string ROLLS_KEY = "GachaRolls";
    public Sprite[] ItemSprite, BannerSprite;
    public Sprite CurrencySprite;
    public TextMeshProUGUI ItemNameText, ItemDescriptionText, RollCountText;
    public TextMeshProUGUI ItemNameSummonText;
    public Image ItemImage;
    public Image ItemSummonImage;
    public Image BannerBackgroundImage;

    //Effects/Sounds
    public AudioSource AudioSource;
    public AudioClip ClickSound, BuzzSound;
    public Animator Animator;
    public GameObject[] SoundObjects, ParticleObjects;

    //Required
    public int RollCount, RollGuaranteeMax;
    public GameObject NotEnoughKeysObject;
    public bool InCooldown;
    public bool AnimationState;
    public int currentMonth;

    //References
    public GameObject Menu, MenuBase;
    public GameObject MainPlayerSystem, SpawnMenuSystemObject; //for finding the currency systems and be able to unlock items
    public MainPlayerCurrencySystem MainPlayerCurrencySystem; //for returning coins when a duplicate has appeared
    public KeyCurrencySystemMain KeyCurrencySystemMain; //for spending keys when gacha is used
    public SpawnMenuDataSystem SpawnMenuDataSystem; //for unlocking items
    private VRCPlayerApi localPlayer;
    public bool PlayerInProximity;

    void Start()
    {
        Menu.SetActive(false);
        NotEnoughKeysObject.SetActive(false);

        for (int i = 0; i < SoundObjects.Length; i++){SoundObjects[i].SetActive(false);}
        for (int i = 0; i < ParticleObjects.Length; i++){ParticleObjects[i].SetActive(false);}

        localPlayer = Networking.LocalPlayer;

        if(MainPlayerCurrencySystem == null){MainPlayerCurrencySystem = MainPlayerSystem.GetComponent<MainPlayerCurrencySystem>();}
        if(KeyCurrencySystemMain == null){KeyCurrencySystemMain = MainPlayerSystem.GetComponent<KeyCurrencySystemMain>();}
        if(SpawnMenuDataSystem == null){SpawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();}

        AnimationState = false;
        Animator.SetBool("IsActive", AnimationState);

        BannerBackgroundImage.sprite = BannerSprite[BannerSelected];

        SendCustomEventDelayedSeconds(nameof(RollsCountRefresh), 5f);
        SendCustomEventDelayedSeconds(nameof(SeasonalBannerRefresh), 3f);
        SendCustomEventDelayedSeconds(nameof(RegularItemMenuShower), 10f);
    }
    public void RollsCountRefresh()
    {
        LoadRollCount();
        
        RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;
    }
    public void SeasonalBannerRefresh()
    {
        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        currentMonth = networkTime.Month;

        //Depending on the month. Set the weapon range from 16 to 27 to 2 depending on the month.
        if (currentMonth == 0) { SeasonalWeaponSetter(1); } // Fallback to January if invalid
        if (currentMonth == 1) { SeasonalWeaponSetter(1); }
        if (currentMonth == 2) { SeasonalWeaponSetter(2); }
        if (currentMonth == 3) { SeasonalWeaponSetter(3); }
        if (currentMonth == 4) { SeasonalWeaponSetter(4); }
        if (currentMonth == 5) { SeasonalWeaponSetter(5); }
        if (currentMonth == 6) { SeasonalWeaponSetter(6); }
        if (currentMonth == 7) { SeasonalWeaponSetter(7); }
        if (currentMonth == 8) { SeasonalWeaponSetter(8); }
        if (currentMonth == 9) { SeasonalWeaponSetter(9); }
        if (currentMonth == 10) { SeasonalWeaponSetter(10); }
        if (currentMonth == 11) { SeasonalWeaponSetter(11); }
        if (currentMonth == 12) { SeasonalWeaponSetter(12); }

    }
    public void SeasonalWeaponSetter(int month){
        //First, set all seasonal weapons (ID 16 to 27) to rarity 6 (not obtainable)
        for(int i = 16; i <= 27; i++){ItemRarity[i] = 6;}

        //Then set the weapon corresponding to the current month to rarity 2 (seasonal banner)
        //Month 1 = January (ID 16), Month 2 = February (ID 17), etc.
        if(month >= 1 && month <= 12)
        {
            int seasonalWeaponID = 15 + month; // Month 1 -> ID 16, Month 2 -> ID 17, etc.
            ItemRarity[seasonalWeaponID] = 2;
        }
    }
    public void RegularItemMenuShower()
    {
        if(BannerSelected == 0)
        {
            // Create a list of indices for items with ItemRarity 1 (regular items)
            int[] regularItemIndices = new int[ItemID.Length];
            int regularItemCount = 0;

            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 1)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }

            // Only proceed if there are regular items available
            if (regularItemCount > 0)
            {
                int randomIndex = Random.Range(0, regularItemCount);
                int RandomItem = regularItemIndices[randomIndex];

                ItemNameText.text = ItemName[RandomItem];
                ItemDescriptionText.text = "[Description]\n" + ItemDescription[RandomItem];
                ItemImage.sprite = ItemSprite[RandomItem];
            }
        }

        SendCustomEventDelayedSeconds(nameof(RegularItemMenuShower), 10f);
    }

    #region BannerSelection
    //Changes the banner selected
    public void SelectBanner1()
    {
        BannerSelected = 0;

        //change to the regular item if there's any
        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        for (int i = 0; i < ItemRarity.Length; i++)
        {
            if (ItemRarity[i] == 1)
            {
                regularItemIndices[regularItemCount] = i;
                regularItemCount++;
            }
        }
        // Only proceed if there are regular items available
        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameText.text = ItemName[RandomItem];
            ItemDescriptionText.text = "[Description]\n" + ItemDescription[RandomItem];
            ItemImage.sprite = ItemSprite[RandomItem];
        }

        BannerBackgroundImage.sprite = BannerSprite[BannerSelected];
    }
    public void SelectBanner2()
    {
        BannerSelected = 1;

        //change to the seasonal exclusive item if there's any
        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        for (int i = 0; i < ItemRarity.Length; i++)
        {
            if (ItemRarity[i] == 2)
            {
                regularItemIndices[regularItemCount] = i;
                regularItemCount++;
            }
        }

        // Only proceed if there are regular items available
        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameText.text = ItemName[RandomItem];
            ItemDescriptionText.text = "[Description]\n" + ItemDescription[RandomItem];
            ItemImage.sprite = ItemSprite[RandomItem];
        }

        BannerBackgroundImage.sprite = BannerSprite[BannerSelected];
    }
    public void SelectBanner3()
    {
        BannerSelected = 2;

        //change to a collab exclusive item if there's any
        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        for (int i = 0; i < ItemRarity.Length; i++)
        {
            if (ItemRarity[i] == 4)
            {
                regularItemIndices[regularItemCount] = i;
                regularItemCount++;
            }
        }

        // Only proceed if there are regular items available
        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameText.text = ItemName[RandomItem];
            ItemDescriptionText.text = "[Description]\n" + ItemDescription[RandomItem];
            ItemImage.sprite = ItemSprite[RandomItem];
        }

        BannerBackgroundImage.sprite = BannerSprite[BannerSelected];
    }
    public void SelectBanner4()
    {
        BannerSelected = 3;
        
        //change to a limited exclusive item if there's any
        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        for (int i = 0; i < ItemRarity.Length; i++)
        {
            if (ItemRarity[i] == 5)
            {
                regularItemIndices[regularItemCount] = i;
                regularItemCount++;
            }
        }

        // Only proceed if there are regular items available
        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameText.text = ItemName[RandomItem];
            ItemDescriptionText.text = "[Description]\n" + ItemDescription[RandomItem];
            ItemImage.sprite = ItemSprite[RandomItem];
        }

        BannerBackgroundImage.sprite = BannerSprite[BannerSelected];
    }
    #endregion BannerSelection

    #region GachaRoll

    public void GachaRoll(){
        int PlayerKeys = PlayerData.GetInt(localPlayer, "EmperKeys");

        if(PlayerKeys > 0 && InCooldown == false){
            MenuBase.SetActive(false);

            KeyCurrencySystemMain.RemoveKey();
            AnimationState = true;
            Animator.SetBool("IsActive", AnimationState);

            if(BannerSelected == 0){ //95% chance to get a regular item, 3% chance to get a weapon (100% chance for regular weapon)
                if(RollCount < RollGuaranteeMax-1){
                    RollCount++;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    int RandomizeValueRegular = Random.Range(0, 101);

                    if(RandomizeValueRegular < 95){
                        GiveRegularItem();
                        ActivateEffect(0);
                    }
                    else if(RandomizeValueRegular >= 95){
                        GiveWeaponItem(1);
                        ActivateEffect(1);
                    }
                }
                else{ //Guaranteed chance for a weapon
                    RollCount = 0;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    GiveWeaponItem(1);
                    ActivateEffect(1);
                }
            }
            else if(BannerSelected == 1){ //95% chance to get a regular item, 3% chance to get a weapon (50% chance for regular weapon and 50% chance for seasonal)
                if(RollCount < RollGuaranteeMax-1){
                    RollCount++;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    int RandomizeValueRegular = Random.Range(0, 101);

                    if(RandomizeValueRegular < 95){GiveRegularItem(); ActivateEffect(0);}
                    else if(RandomizeValueRegular >= 95){
                        int RandomizeValueWeapon = Random.Range(0, 101);
                        if(RandomizeValueWeapon < 50){GiveWeaponItem(1); ActivateEffect(1);}
                        else if(RandomizeValueWeapon >= 50){GiveWeaponItem(2); ActivateEffect(2);}
                    }
                }
                else{ //Guaranteed chance for a weapon (50% chance for regular and 50% chance for seasonal)
                    RollCount = 0;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    int RandomizeValueWeapon = Random.Range(0, 101);
                    if(RandomizeValueWeapon < 50){GiveWeaponItem(1); ActivateEffect(1);}
                    else if(RandomizeValueWeapon >= 50){GiveWeaponItem(2); ActivateEffect(2);}
                }
            }
            else if(BannerSelected == 2){ //95% chance to get a regular item, 3% chance to get a weapon (50% chance for regular weapon and 50% chance for collab)
                if(RollCount < RollGuaranteeMax-1){
                    RollCount++;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;
                    
                    int RandomizeValueRegular = Random.Range(0, 101);
                    
                    if(RandomizeValueRegular < 95){GiveRegularItem(); ActivateEffect(0);}
                    else if(RandomizeValueRegular >= 95){
                        int RandomizeValueWeapon = Random.Range(0, 101);
                        if(RandomizeValueWeapon < 50){GiveWeaponItem(3); ActivateEffect(3);}
                        else if(RandomizeValueWeapon >= 50){GiveWeaponItem(3); ActivateEffect(3);}
                    }
                }
                else{ //Guaranteed chance for a weapon (50% chance for regular and 50% chance for collab)
                    RollCount = 0;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    int RandomizeValueWeapon = Random.Range(0, 101);
                    if(RandomizeValueWeapon < 50){ GiveWeaponItem(3); ActivateEffect(3);}
                    else if(RandomizeValueWeapon >= 50){ GiveWeaponItem(4); ActivateEffect(4);}
                }
            }
            else if(BannerSelected == 3){ //95% chance to get a regular item, 2% chance to get a weapon (75% chance for regular weapon and 25% chance for limited)
                if(RollCount < RollGuaranteeMax-1){
                    RollCount++;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;
                    
                    int RandomizeValueRegular = Random.Range(0, 101);
                    
                    if(RandomizeValueRegular < 98){ GiveRegularItem(); ActivateEffect(0);}
                    else if(RandomizeValueRegular >= 98){
                        int RandomizeValueWeapon = Random.Range(0, 101);
                        if(RandomizeValueWeapon < 25){ GiveWeaponItem(5); ActivateEffect(5);}
                        else if(RandomizeValueWeapon >= 25){ GiveWeaponItem(1); ActivateEffect(1);}
                    }
                }
                else{ //Guaranteed chance for a weapon (50% chance for regular and 50% chance for limited)
                    RollCount = 0;
                    RollCountText.text = "Summons until Guarantee: " + RollCount + "/" + RollGuaranteeMax;

                    int RandomizeValueWeapon = Random.Range(0, 101);
                    if(RandomizeValueWeapon < 25){ GiveWeaponItem(5); ActivateEffect(5);}
                    else if(RandomizeValueWeapon >= 25){ GiveWeaponItem(1); ActivateEffect(1);}
                }
            }

            InCooldown = true;
            SaveRollCount();
            SendCustomEventDelayedSeconds(nameof(OpenMenu), 10f);
            SendCustomEventDelayedSeconds(nameof(Cooldown), 2f);
        }
        else{
            NotEnoughKeysObject.SetActive(true);
            AudioSource.PlayOneShot(BuzzSound);

            InCooldown = true;
            SendCustomEventDelayedSeconds(nameof(CloseNotEnoughKeys), 2f);
            SendCustomEventDelayedSeconds(nameof(Cooldown), 2f);
        }

    }


    //Regular Items Picker
    public void GiveRegularItem(){
        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        for (int i = 0; i < ItemRarity.Length; i++)
        {
            if (ItemRarity[i] == 0)
            {
                regularItemIndices[regularItemCount] = i;
                regularItemCount++;
            }
        }

        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameSummonText.text = ItemName[RandomItem];
            ItemSummonImage.sprite = ItemSprite[RandomItem];

            //Unlock Item
            if(SpawnMenuDataSystem.IsItemUnlocked(ItemID[RandomItem]) == false){SpawnMenuDataSystem.UnlockItem(ItemID[RandomItem]);}
            else{ //Give Money instead
                int RandomCoins = Random.Range(100, 500);
                ItemNameSummonText.text = RandomCoins + " EmperCoins!";
                ItemSummonImage.sprite = CurrencySprite;
                MainPlayerCurrencySystem.AddPoints(RandomCoins);
            }
        }
    }

    //BannerPicked 1 is regular, 2 is seasonal, 3 is collab old, 4 is collab new, 5 is limited
    public void GiveWeaponItem(int BannerPicked){

        int[] regularItemIndices = new int[ItemID.Length];
        int regularItemCount = 0;

        if(BannerPicked == 1){ //Regular
            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 1)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }
        }
        else if(BannerPicked == 2){ //Seasonal
            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 2)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }
        }
        else if(BannerPicked == 3){ //Collab old
            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 3)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }
        }
        else if(BannerPicked == 4){ //Collab new
            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 4)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }
        }
        else if(BannerPicked == 5){ //Limited
            for (int i = 0; i < ItemRarity.Length; i++)
            {
                if (ItemRarity[i] == 5)
                {
                    regularItemIndices[regularItemCount] = i;
                    regularItemCount++;
                }
            }
        }

        // Debug: Log filtering results
        Debug.Log($"Found {regularItemCount} items with rarity {BannerPicked + 1}");

        if (regularItemCount > 0)
        {
            int randomIndex = Random.Range(0, regularItemCount);
            int RandomItem = regularItemIndices[randomIndex];

            ItemNameSummonText.text = ItemName[RandomItem];
            ItemSummonImage.sprite = ItemSprite[RandomItem];

            //Unlock Item
            if(SpawnMenuDataSystem.IsItemUnlocked(ItemID[RandomItem]) == false){SpawnMenuDataSystem.UnlockItem(ItemID[RandomItem]);}
            else{ //Give Money instead
                int RandomCoins = Random.Range(500, 1000);
                ItemNameSummonText.text = RandomCoins + " EmperCoins!";
                ItemSummonImage.sprite = CurrencySprite;
                MainPlayerCurrencySystem.AddPoints(RandomCoins);
            }
        }
    }

    public void ActivateEffect(int EffectValue){
        for (int i = 0; i < SoundObjects.Length; i++){SoundObjects[i].SetActive(false);}
        for (int i = 0; i < ParticleObjects.Length; i++){ParticleObjects[i].SetActive(false);}
        SoundObjects[EffectValue].SetActive(true);
        ParticleObjects[EffectValue].SetActive(true);
    }
    #endregion GachaRoll


    #region Extras
    public void Keysound(){AudioSource.PlayOneShot(ClickSound);}
    public void OpenMenu(){
        MenuBase.SetActive(true);
        AnimationState = false;
        Animator.SetBool("IsActive", AnimationState);
    }
    public void CloseNotEnoughKeys(){NotEnoughKeysObject.SetActive(false);}
    public void Cooldown(){InCooldown = false;}
    public void LoadRollCount(){RollCount = PlayerData.GetInt(localPlayer, ROLLS_KEY);}
    public void SaveRollCount(){PlayerData.SetInt(ROLLS_KEY, RollCount);}
    #endregion Extras

    #region CollisionFunctions
    //Collision Related
    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(true);
        PlayerInProximity = true;
    }
    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(false);
        PlayerInProximity = false;
    }
    #endregion CollisionFunctions
}
