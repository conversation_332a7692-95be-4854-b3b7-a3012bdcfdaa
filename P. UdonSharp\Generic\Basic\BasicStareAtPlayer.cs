﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicStareAtPlayer : UdonSharpBehaviour
{
    private VRCPlayerApi localPlayer;
    public bool IsActive;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        IsActive = true;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);  
    }

    void OnEnable()
    {
        IsActive = true;
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);
    }
    void OnDisable(){IsActive = false;}

    public void UpdateCustom()
    {
        if (localPlayer == null) return;

        // Get player position
        Vector3 playerPos = localPlayer.GetPosition() + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters());
        
        // Create a flattened direction vector (only horizontal)
        Vector3 flatDirection = playerPos - transform.position;
        flatDirection.y = 0; // Remove the vertical component
        flatDirection = flatDirection.normalized;
        
        // Create rotation only around Y axis
        Quaternion rotation = Quaternion.LookRotation(flatDirection);
        transform.rotation = rotation;

        if(IsActive){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);}
    }
}

