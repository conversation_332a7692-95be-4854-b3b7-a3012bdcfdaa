﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpaceResourceInput : UdonSharpBehaviour
{
    public string IsStorable = "This is a basic input. Nothing to see here."; //This is a basic input. Nothing to see here.
    public int Reward = 0; //Reward for getting this resource

    public GameObject OutputSystem;

    public override void OnPickup(){
        if(OutputSystem == null){OutputSystem = GameObject.Find("RESOURCES_EXTRACTOR");}
        if(OutputSystem != null){OutputSystem.GetComponent<MeshRenderer>().enabled = true;}
    }

    public override void OnDrop(){
        if(OutputSystem == null){OutputSystem = GameObject.Find("RESOURCES_EXTRACTOR");}
        if(OutputSystem != null){OutputSystem.GetComponent<MeshRenderer>().enabled = false;}
    }
}
